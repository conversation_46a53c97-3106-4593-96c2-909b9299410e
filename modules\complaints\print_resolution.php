<?php
session_start();
include '../../includes/config/database.php';
include '../../includes/functions/permission_functions.php';
include '../../includes/functions/utility.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check permission
if (!hasPermission('print_resolution')) {
    header("Location: ../../index.php");
    exit;
}

// Check if resolution ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    echo "Resolution ID is required!";
    exit;
}

$resolution_id = (int)$_GET['id'];

// Get resolution details with complaint, complainant, respondent, and officials
$query = "
    SELECT r.*, c.complaint_id, c.complaint_type, c.complaint_details, c.incident_date, c.incident_location,
        comp.first_name as complainant_first_name, comp.middle_name as complainant_middle_name, comp.last_name as complainant_last_name,
        resp.first_name as respondent_first_name, resp.middle_name as respondent_middle_name, resp.last_name as respondent_last_name,
        c.complainant_name as non_resident_complainant, c.respondent_name as non_resident_respondent,
        u.username as resolved_by_name
    FROM resolutions r
    JOIN complaints c ON r.complaint_id = c.complaint_id
    LEFT JOIN residents comp ON c.complainant_id = comp.resident_id
    LEFT JOIN residents resp ON c.respondent_id = resp.resident_id
    LEFT JOIN users u ON r.resolved_by = u.user_id
    WHERE r.resolution_id = :resolution_id
";

$stmt = $conn->prepare($query);
$stmt->bindParam(':resolution_id', $resolution_id, PDO::PARAM_INT);
$stmt->execute();

if ($stmt->rowCount() == 0) {
    echo "Resolution not found!";
    exit;
}

$resolution = $stmt->fetch(PDO::FETCH_ASSOC);

// Check if resolution_witnesses table exists before querying it
$tableExists = false;
try {
    $check = $conn->query("SHOW TABLES LIKE 'resolution_witnesses'");
    $tableExists = ($check->rowCount() > 0);
} catch (PDOException $e) {
    // Table doesn't exist or other error
    $tableExists = false;
}

$witnesses = [];
if ($tableExists) {
    // Get witnesses
    $witnesses_query = "
        SELECT o.official_id, r.first_name, r.last_name, p.position_name
        FROM resolution_witnesses rw
        JOIN officials o ON rw.official_id = o.official_id
        JOIN residents r ON o.resident_id = r.resident_id
        JOIN positions p ON o.position = p.position_id
        WHERE rw.resolution_id = :resolution_id
    ";
    $witnesses_stmt = $conn->prepare($witnesses_query);
    $witnesses_stmt->bindParam(':resolution_id', $resolution_id, PDO::PARAM_INT);
    $witnesses_stmt->execute();
    
    while ($witness = $witnesses_stmt->fetch(PDO::FETCH_ASSOC)) {
        $witnesses[] = $witness;
    }
}

// Get barangay information
$barangay_info = [];
try {
    $check = $conn->query("SHOW TABLES LIKE 'barangay_info'");
    if ($check->rowCount() > 0) {
        $barangay_query = "SELECT * FROM barangay_info LIMIT 1";
        $barangay_stmt = $conn->query($barangay_query);
        $barangay_info = $barangay_stmt->fetch(PDO::FETCH_ASSOC);
    }
} catch (PDOException $e) {
    // Table doesn't exist or other error
    $barangay_info = [];
}

// Get barangay officials for signature
$captain = null;
$secretary = null;

try {
    // Check if required tables exist
    $check_officials = $conn->query("SHOW TABLES LIKE 'officials'");
    $check_positions = $conn->query("SHOW TABLES LIKE 'positions'");
    
    if ($check_officials->rowCount() > 0 && $check_positions->rowCount() > 0) {
        $captain_query = "
            SELECT o.official_id, r.first_name, r.middle_name, r.last_name
            FROM officials o
            JOIN residents r ON o.resident_id = r.resident_id
            JOIN positions p ON o.position = p.position_id
            WHERE p.position_name = 'Barangay Captain' AND o.status = 'Active'
            LIMIT 1
        ";
        $captain_stmt = $conn->query($captain_query);
        $captain = $captain_stmt->fetch(PDO::FETCH_ASSOC);
        
        $secretary_query = "
            SELECT o.official_id, r.first_name, r.middle_name, r.last_name
            FROM officials o
            JOIN residents r ON o.resident_id = r.resident_id
            JOIN positions p ON o.position = p.position_id
            WHERE p.position_name = 'Secretary' AND o.status = 'Active'
            LIMIT 1
        ";
        $secretary_stmt = $conn->query($secretary_query);
        $secretary = $secretary_stmt->fetch(PDO::FETCH_ASSOC);
    }
} catch (PDOException $e) {
    // Error handling if query fails
    // Keep defaults as null
}

// Log activity
logActivity($_SESSION['user_id'], 'Print Resolution', "Printed resolution #$resolution_id for complaint #{$resolution['complaint_id']}");

// Format complainant and respondent names
if (!empty($resolution['complainant_last_name'])) {
    $complainant_name = $resolution['complainant_last_name'] . ', ' . 
                         $resolution['complainant_first_name'] . ' ' . 
                         $resolution['complainant_middle_name'];
} else {
    $complainant_name = $resolution['non_resident_complainant'];
}

if (!empty($resolution['respondent_last_name'])) {
    $respondent_name = $resolution['respondent_last_name'] . ', ' . 
                        $resolution['respondent_first_name'] . ' ' . 
                        $resolution['respondent_middle_name'];
} else {
    $respondent_name = $resolution['non_resident_respondent'];
}

// Generate resolution number
$year = date('Y', strtotime($resolution['resolution_date']));
$resolution_no = "Resolution-" . $year . "-" . str_pad($resolution_id, 4, '0', STR_PAD_LEFT);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resolution - <?php echo $resolution_no; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <style>
        body {
            font-family: 'Times New Roman', Times, serif;
            font-size: 12pt;
            line-height: 1.5;
            margin: 0;
            padding: 0;
        }
        
        .resolution-container {
            max-width: 8.5in;
            margin: 0 auto;
            padding: 0.5in;
        }
        
        .letterhead {
            text-align: center;
            margin-bottom: 1rem;
        }
        
        .letterhead img {
            max-height: 80px;
        }
        
        .resolution-title {
            text-align: center;
            font-weight: bold;
            margin: 1rem 0;
            text-transform: uppercase;
        }
        
        .resolution-subtitle {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .resolution-body {
            text-align: justify;
            margin-bottom: 1.5rem;
        }
        
        .resolution-body p {
            text-indent: 0.5in;
            margin-bottom: 0.8rem;
        }
        
        .resolution-body p.no-indent {
            text-indent: 0;
        }
        
        .resolution-footer {
            margin-top: 2rem;
        }
        
        .signature-line {
            width: 200px;
            border-bottom: 1px solid #000;
            margin: 3rem auto 0;
            text-align: center;
        }
        
        .signature-name {
            margin-top: 0.2rem;
            text-align: center;
            font-weight: bold;
        }
        
        .signature-position {
            text-align: center;
        }
        
        .signatures {
            display: flex;
            justify-content: space-between;
            margin-top: 2rem;
        }
        
        .signature-container {
            flex: 1;
            margin: 0 1rem;
            text-align: center;
        }
        
        .witnesses-section {
            margin-top: 2rem;
        }
        
        .witness-container {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            margin-top: 1rem;
        }
        
        .witness-signature {
            width: 45%;
            margin-bottom: 1.5rem;
        }
        
        @media print {
            .no-print {
                display: none;
            }
            
            body {
                padding: 0;
                margin: 0;
            }
            
            .resolution-container {
                padding: 0.5in;
                width: 100%;
                max-width: 100%;
                box-sizing: border-box;
            }
        }
    </style>
</head>
<body>
    <div class="no-print mb-3 p-3 bg-light">
        <div class="container">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3">Resolution Print Preview</h1>
                <div>
                    <button class="btn btn-primary" onclick="window.print()">
                        <i class="fas fa-print"></i> Print
                    </button>
                    <a href="view_complaint.php?id=<?php echo $resolution['complaint_id']; ?>" class="btn btn-secondary">
                        Back to Complaint
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="resolution-container">
        <div class="letterhead">
            <div class="row">
                <div class="col-2 text-end">
                    <?php if (!empty($barangay_info['logo'])): ?>
                    <img src="../../assets/img/<?php echo $barangay_info['logo']; ?>" alt="Barangay Logo" height="80">
                    <?php endif; ?>
                </div>
                <div class="col-8">
                    <h6 class="mb-0">Republic of the Philippines</h6>
                    <h6 class="mb-0">Province of <?php echo $barangay_info['province'] ?? 'Province'; ?></h6>
                    <h6 class="mb-0">Municipality of <?php echo $barangay_info['municipality'] ?? 'Municipality'; ?></h6>
                    <h5 class="mb-0"><strong>BARANGAY <?php echo strtoupper($barangay_info['barangay_name'] ?? 'BARANGAY NAME'); ?></strong></h5>
                    <h6 class="mb-0">Office of the Lupong Tagapamayapa</h6>
                </div>
                <div class="col-2">
                    <?php if (!empty($barangay_info['municipality_logo'])): ?>
                    <img src="../../assets/img/<?php echo $barangay_info['municipality_logo']; ?>" alt="Municipality Logo" height="80">
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="resolution-title">
            <?php 
            if ($resolution['resolution_type'] == 'Amicable Settlement') {
                echo "Certificate of Settlement";
            } elseif ($resolution['resolution_type'] == 'Mediation') {
                echo "Certificate of Mediation";
            } elseif ($resolution['resolution_type'] == 'Arbitration') {
                echo "Arbitration Award";
            } elseif ($resolution['resolution_type'] == 'Dismissed') {
                echo "Certificate of Dismissal";
            } else {
                echo "Resolution";
            }
            ?>
        </div>
        
        <div class="resolution-subtitle">
            <strong>Resolution No. <?php echo $resolution_no; ?></strong>
        </div>
        
        <div class="resolution-body">
            <p class="no-indent"><strong>COMPLAINANT:</strong> <?php echo strtoupper($complainant_name); ?></p>
            <p class="no-indent"><strong>RESPONDENT:</strong> <?php echo strtoupper($respondent_name); ?></p>
            <p class="no-indent"><strong>NATURE OF COMPLAINT:</strong> <?php echo strtoupper($resolution['complaint_type']); ?></p>
            <p class="no-indent"><strong>DATE FILED:</strong> <?php echo date('F d, Y', strtotime($resolution['resolution_date'])); ?></p>
            
            <hr>
            
            <?php if (in_array($resolution['resolution_type'], ['Amicable Settlement', 'Mediation', 'Conciliation', 'Arbitration'])): ?>
            <p>This is to certify that:</p>
            
            <p>The complaint filed by <strong><?php echo $complainant_name; ?></strong> against <strong><?php echo $respondent_name; ?></strong> 
            regarding <strong><?php echo $resolution['complaint_type']; ?></strong> 
            that occurred on <?php echo date('F d, Y', strtotime($resolution['incident_date'])); ?> 
            at <?php echo $resolution['incident_location']; ?> has been resolved through 
            <strong><?php echo $resolution['resolution_type']; ?></strong>.</p>
            
            <p><?php echo nl2br($resolution['resolution_content']); ?></p>
            
            <?php if (!empty($resolution['agreement_terms'])): ?>
            <p><strong>TERMS OF AGREEMENT:</strong></p>
            <p><?php echo nl2br($resolution['agreement_terms']); ?></p>
            <?php endif; ?>
            
            <p>This settlement/agreement is final and binding upon the parties involved.</p>
            
            <p>Executed on this <?php echo date('jS', strtotime($resolution['resolution_date'])); ?> day of 
            <?php echo date('F, Y', strtotime($resolution['resolution_date'])); ?> at Barangay 
            <?php echo $barangay_info['barangay_name'] ?? 'Barangay'; ?>, 
            <?php echo $barangay_info['municipality'] ?? 'Municipality'; ?>, 
            <?php echo $barangay_info['province'] ?? 'Province'; ?>.</p>
            
            <?php else: ?>
            
            <p>This is to certify that the complaint filed by <strong><?php echo $complainant_name; ?></strong> 
            against <strong><?php echo $respondent_name; ?></strong> regarding 
            <strong><?php echo $resolution['complaint_type']; ?></strong> has been 
            <?php 
            if ($resolution['resolution_type'] == 'Dismissed') {
                echo "dismissed";
            } elseif ($resolution['resolution_type'] == 'Referred to Higher Authority') {
                echo "referred to higher authority";
            } else {
                echo strtolower($resolution['resolution_type']);
            }
            ?>.</p>
            
            <p><?php echo nl2br($resolution['resolution_content']); ?></p>
            
            <p>Done this <?php echo date('jS', strtotime($resolution['resolution_date'])); ?> day of 
            <?php echo date('F, Y', strtotime($resolution['resolution_date'])); ?> at Barangay 
            <?php echo $barangay_info['barangay_name'] ?? 'Barangay'; ?>, 
            <?php echo $barangay_info['municipality'] ?? 'Municipality'; ?>, 
            <?php echo $barangay_info['province'] ?? 'Province'; ?>.</p>
            
            <?php endif; ?>
        </div>
        
        <?php if (in_array($resolution['resolution_type'], ['Amicable Settlement', 'Mediation', 'Conciliation', 'Arbitration'])): ?>
        <div class="signatures">
            <div class="signature-container">
                <div class="signature-line"></div>
                <div class="signature-name"><?php echo strtoupper($complainant_name); ?></div>
                <div class="signature-position">Complainant</div>
            </div>
            
            <div class="signature-container">
                <div class="signature-line"></div>
                <div class="signature-name"><?php echo strtoupper($respondent_name); ?></div>
                <div class="signature-position">Respondent</div>
            </div>
        </div>
        <?php endif; ?>
        
        <?php if (count($witnesses) > 0): ?>
        <div class="witnesses-section">
            <h6 class="text-center"><strong>WITNESSES:</strong></h6>
            <div class="witness-container">
                <?php foreach ($witnesses as $witness): ?>
                <div class="witness-signature">
                    <div class="signature-line"></div>
                    <div class="signature-name"><?php echo strtoupper($witness['first_name'] . ' ' . $witness['last_name']); ?></div>
                    <div class="signature-position"><?php echo $witness['position_name']; ?></div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
        
        <div class="resolution-footer mt-5">
            <div class="row">
                <div class="col-6 offset-3 text-center">
                    <div class="signature-line"></div>
                    <div class="signature-name">
                        <?php 
                        if (!empty($captain)) {
                            echo strtoupper($captain['first_name'] . ' ' . ($captain['middle_name'] ? substr($captain['middle_name'], 0, 1) . '. ' : '') . $captain['last_name']);
                        } else {
                            echo "BARANGAY CAPTAIN";
                        }
                        ?>
                    </div>
                    <div class="signature-position">Punong Barangay</div>
                </div>
            </div>
            
            <div class="mt-5 text-center">
                <p><small>Attested by:</small></p>
                <div class="signature-line mx-auto"></div>
                <div class="signature-name">
                    <?php 
                    if (!empty($secretary)) {
                        echo strtoupper($secretary['first_name'] . ' ' . ($secretary['middle_name'] ? substr($secretary['middle_name'], 0, 1) . '. ' : '') . $secretary['last_name']);
                    } else {
                        echo "BARANGAY SECRETARY";
                    }
                    ?>
                </div>
                <div class="signature-position">Barangay Secretary</div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 