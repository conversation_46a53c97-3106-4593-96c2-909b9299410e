<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
include '../../includes/config/database.php';
include '../../includes/functions/permission_functions.php';
include '../../includes/functions/utility.php';
include '../../includes/functions/log_function.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check permission
if (!hasPermission('edit_residents')) {
    header("Location: ../../index.php");
    exit;
}

// For debugging - log the POST data
error_log("Migration action received: " . json_encode($_POST));

// Validate that an action was provided
if (!isset($_POST['action'])) {
    error_log("No action provided in POST data");
    header("Location: migration_records.php?error=invalid_request");
    exit;
}

$action = $_POST['action'];

// Handle different actions
switch ($action) {
    case 'add':
        add_migration();
        break;
    case 'edit':
        edit_migration();
        break;
    case 'delete':
        delete_migration();
        break;
    case 'view_migration':
        view_migration();
        break;
    default:
        error_log("Invalid action provided: " . $action);
        header("Location: migration_records.php?error=invalid_action");
        exit;
}

// Function to add a new migration record
function add_migration() {
    global $conn;
    
    // Log the function call
    error_log("add_migration function called");
    
    // Validate form inputs
    $required_fields = ['resident_id', 'migration_type', 'origin_address', 'destination_address', 'migration_date'];
    $missing_fields = [];
    
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty($_POST[$field])) {
            $missing_fields[] = $field;
        }
    }
    
    if (!empty($missing_fields)) {
        error_log("Validation failed - missing fields: " . implode(', ', $missing_fields));
        header("Location: migration_records.php?error=validation&fields=" . implode(',', $missing_fields));
        exit;
    }
    
    $resident_id = (int)$_POST['resident_id'];
    $migration_type = $_POST['migration_type'];
    $origin_address = htmlspecialchars(trim($_POST['origin_address']));
    $destination_address = htmlspecialchars(trim($_POST['destination_address']));
    $migration_date = $_POST['migration_date'];
    $reason = isset($_POST['reason']) ? htmlspecialchars(trim($_POST['reason'])) : '';
    $remarks = isset($_POST['remarks']) ? htmlspecialchars(trim($_POST['remarks'])) : '';
    $update_resident_status = isset($_POST['update_resident_status']) ? true : false;
    
    try {
        // Begin transaction
        $conn->beginTransaction();
        error_log("Transaction started for adding migration record");
        
        // Get official_id for current user (if available)
        $official_id = null;
        try {
            $official_query = "SELECT official_id FROM officials 
                              WHERE resident_id IN (SELECT resident_id FROM users WHERE user_id = :user_id)
                              LIMIT 1";
            $official_stmt = $conn->prepare($official_query);
            $official_stmt->bindParam(':user_id', $_SESSION['user_id'], PDO::PARAM_INT);
            $official_stmt->execute();
            $official_result = $official_stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($official_result) {
                $official_id = $official_result['official_id'];
            }
        } catch (PDOException $e) {
            error_log("Error fetching official_id: " . $e->getMessage());
            // Continue without official_id
        }
        
        // Insert migration record
        $insert_query = "INSERT INTO migration_records (
            resident_id, migration_type, origin_address, destination_address, 
            migration_date, reason, remarks, recorded_by, date_recorded
        ) VALUES (
            :resident_id, :migration_type, :origin_address, :destination_address, 
            :migration_date, :reason, :remarks, :recorded_by, NOW()
        )";
        
        $insert_stmt = $conn->prepare($insert_query);
        $insert_stmt->bindParam(':resident_id', $resident_id, PDO::PARAM_INT);
        $insert_stmt->bindParam(':migration_type', $migration_type, PDO::PARAM_STR);
        $insert_stmt->bindParam(':origin_address', $origin_address, PDO::PARAM_STR);
        $insert_stmt->bindParam(':destination_address', $destination_address, PDO::PARAM_STR);
        $insert_stmt->bindParam(':migration_date', $migration_date, PDO::PARAM_STR);
        $insert_stmt->bindParam(':reason', $reason, PDO::PARAM_STR);
        $insert_stmt->bindParam(':remarks', $remarks, PDO::PARAM_STR);
        $insert_stmt->bindParam(':recorded_by', $official_id, PDO::PARAM_INT);
        
        $result = $insert_stmt->execute();
        if (!$result) {
            error_log("Failed to execute insert statement: " . implode(', ', $insert_stmt->errorInfo()));
            throw new PDOException("Failed to execute insert statement");
        }
        
        error_log("Migration record inserted successfully");
        $record_id = $conn->lastInsertId();
        error_log("New record ID: " . $record_id);
        
        // Update resident status if requested
        if ($update_resident_status) {
            $new_status = '';
            
            switch ($migration_type) {
                case 'Arrival':
                    $new_status = 'Active';
                    break;
                case 'Departure':
                    $new_status = 'Inactive';
                    break;
                // For 'Transfer', we don't change the status
            }
            
            if (!empty($new_status)) {
                error_log("Updating resident status to: " . $new_status);
                $update_query = "UPDATE residents SET status = :status WHERE resident_id = :resident_id";
                $update_stmt = $conn->prepare($update_query);
                $update_stmt->bindParam(':status', $new_status, PDO::PARAM_STR);
                $update_stmt->bindParam(':resident_id', $resident_id, PDO::PARAM_INT);
                $update_result = $update_stmt->execute();
                
                if (!$update_result) {
                    error_log("Failed to update resident status: " . implode(', ', $update_stmt->errorInfo()));
                }
            }
        }
        
        // Log activity
        try {
            log_activity_safe($conn, $_SESSION['user_id'], 'Add Migration Record', 'Added migration record for resident #' . $resident_id, 'migration_records', $record_id);
            error_log("Activity logged successfully");
        } catch (Exception $e) {
            error_log("Failed to log activity: " . $e->getMessage());
            // Continue execution - activity logging failure shouldn't stop the process
        }
        
        // Commit transaction
        $conn->commit();
        error_log("Transaction committed successfully");
        
        // Redirect with success message
        header("Location: migration_records.php?success=added");
        exit;
        
    } catch (PDOException $e) {
        // Rollback transaction on error
        $conn->rollBack();
        
        // Log detailed error
        error_log("Error adding migration record: " . $e->getMessage());
        error_log("SQL state: " . $e->getCode());
        
        // Check for MySQL error code for foreign key constraint failures
        if ($e->getCode() == '23000') {
            header("Location: migration_records.php?error=foreign_key");
        } else {
            // Redirect with error message
            header("Location: migration_records.php?error=db_error");
        }
        exit;
    } catch (Exception $e) {
        // Catch any other exceptions
        $conn->rollBack();
        error_log("General error: " . $e->getMessage());
        header("Location: migration_records.php?error=general_error");
        exit;
    }
}

// Function to edit a migration record
function edit_migration() {
    global $conn;
    
    // Validate form inputs
    $required_fields = ['record_id', 'resident_id', 'migration_type', 'origin_address', 'destination_address', 'migration_date'];
    foreach ($required_fields as $field) {
        if (!isset($_POST[$field]) || empty($_POST[$field])) {
            header("Location: migration_records.php?error=validation");
            exit;
        }
    }
    
    $record_id = (int)$_POST['record_id'];
    $resident_id = (int)$_POST['resident_id'];
    $migration_type = $_POST['migration_type'];
    $origin_address = htmlspecialchars(trim($_POST['origin_address']));
    $destination_address = htmlspecialchars(trim($_POST['destination_address']));
    $migration_date = $_POST['migration_date'];
    $reason = isset($_POST['reason']) ? htmlspecialchars(trim($_POST['reason'])) : '';
    $remarks = isset($_POST['remarks']) ? htmlspecialchars(trim($_POST['remarks'])) : '';
    $update_resident_status = isset($_POST['update_resident_status']) ? true : false;
    
    try {
        // Begin transaction
        $conn->beginTransaction();
        
        // First check if record exists
        $check_query = "SELECT * FROM migration_records WHERE record_id = :record_id";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->bindParam(':record_id', $record_id, PDO::PARAM_INT);
        $check_stmt->execute();
        
        if ($check_stmt->rowCount() == 0) {
            // Rollback and return error
            $conn->rollBack();
            header("Location: migration_records.php?error=not_found");
            exit;
        }
        
        // Update migration record
        $update_query = "UPDATE migration_records SET 
                        migration_type = :migration_type,
                        origin_address = :origin_address,
                        destination_address = :destination_address,
                        migration_date = :migration_date,
                        reason = :reason,
                        remarks = :remarks
                        WHERE record_id = :record_id";
        
        $update_stmt = $conn->prepare($update_query);
        $update_stmt->bindParam(':migration_type', $migration_type, PDO::PARAM_STR);
        $update_stmt->bindParam(':origin_address', $origin_address, PDO::PARAM_STR);
        $update_stmt->bindParam(':destination_address', $destination_address, PDO::PARAM_STR);
        $update_stmt->bindParam(':migration_date', $migration_date, PDO::PARAM_STR);
        $update_stmt->bindParam(':reason', $reason, PDO::PARAM_STR);
        $update_stmt->bindParam(':remarks', $remarks, PDO::PARAM_STR);
        $update_stmt->bindParam(':record_id', $record_id, PDO::PARAM_INT);
        $update_stmt->execute();
        
        // Update resident status if requested
        if ($update_resident_status) {
            $new_status = '';
            
            switch ($migration_type) {
                case 'Arrival':
                    $new_status = 'Active';
                    break;
                case 'Departure':
                    $new_status = 'Inactive';
                    break;
                // For 'Transfer', we don't change the status
            }
            
            if (!empty($new_status)) {
                $update_query = "UPDATE residents SET status = :status WHERE resident_id = :resident_id";
                $update_stmt = $conn->prepare($update_query);
                $update_stmt->bindParam(':status', $new_status, PDO::PARAM_STR);
                $update_stmt->bindParam(':resident_id', $resident_id, PDO::PARAM_INT);
                $update_stmt->execute();
            }
        }
        
        // Get official_id for current user (if available) for activity logging
        $official_id = null;
        try {
            $official_query = "SELECT official_id FROM officials 
                              WHERE resident_id IN (SELECT resident_id FROM users WHERE user_id = :user_id)
                              LIMIT 1";
            $official_stmt = $conn->prepare($official_query);
            $official_stmt->bindParam(':user_id', $_SESSION['user_id'], PDO::PARAM_INT);
            $official_stmt->execute();
            $official_result = $official_stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($official_result) {
                $official_id = $official_result['official_id'];
            }
        } catch (PDOException $e) {
            error_log("Error fetching official_id for logging: " . $e->getMessage());
            // Continue without official_id
        }
        
        // Log activity
        if (function_exists('logActivity')) {
            logActivity('Updated migration record', $_SESSION['user_id'], 'update', 'migration_records', $record_id);
        }
        
        // Commit transaction
        $conn->commit();
        
        // Redirect with success message
        header("Location: migration_records.php?success=updated");
        exit;
        
    } catch (PDOException $e) {
        // Rollback transaction on error
        $conn->rollBack();
        
        // Log error
        error_log("Error updating migration record: " . $e->getMessage());
        
        // Redirect with error message
        header("Location: migration_records.php?error=db_error");
        exit;
    }
}

// Function to delete a migration record
function delete_migration() {
    global $conn;
    
    // Validate record_id
    if (!isset($_POST['record_id']) || empty($_POST['record_id'])) {
        header("Location: migration_records.php?error=validation");
        exit;
    }
    
    $record_id = (int)$_POST['record_id'];
    
    try {
        // Begin transaction
        $conn->beginTransaction();
        
        // Get record details before deletion for logging
        $get_query = "SELECT mr.*, r.status as resident_status 
                     FROM migration_records mr
                     JOIN residents r ON mr.resident_id = r.resident_id
                     WHERE record_id = :record_id";
        $get_stmt = $conn->prepare($get_query);
        $get_stmt->bindParam(':record_id', $record_id, PDO::PARAM_INT);
        $get_stmt->execute();
        
        if ($get_stmt->rowCount() == 0) {
            // Record not found
            $conn->rollBack();
            header("Location: migration_records.php?error=not_found");
            exit;
        }
        
        $record = $get_stmt->fetch(PDO::FETCH_ASSOC);
        
        // Delete migration record
        $delete_query = "DELETE FROM migration_records WHERE record_id = :record_id";
        $delete_stmt = $conn->prepare($delete_query);
        $delete_stmt->bindParam(':record_id', $record_id, PDO::PARAM_INT);
        $delete_stmt->execute();
        
        // Get official_id for current user (if available) for activity logging
        $official_id = null;
        try {
            $official_query = "SELECT official_id FROM officials 
                              WHERE resident_id IN (SELECT resident_id FROM users WHERE user_id = :user_id)
                              LIMIT 1";
            $official_stmt = $conn->prepare($official_query);
            $official_stmt->bindParam(':user_id', $_SESSION['user_id'], PDO::PARAM_INT);
            $official_stmt->execute();
            $official_result = $official_stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($official_result) {
                $official_id = $official_result['official_id'];
            }
        } catch (PDOException $e) {
            error_log("Error fetching official_id for logging: " . $e->getMessage());
            // Continue without official_id
        }
        
        // Log activity
        if (function_exists('logActivity')) {
            logActivity('Deleted migration record', $_SESSION['user_id'], 'delete', 'migration_records', $record_id);
        }
        
        // Commit transaction
        $conn->commit();
        
        // Redirect with success message
        header("Location: migration_records.php?success=deleted");
        exit;
        
    } catch (PDOException $e) {
        // Rollback transaction on error
        $conn->rollBack();
        
        // Log error
        error_log("Error deleting migration record: " . $e->getMessage());
        
        // Redirect with error message
        header("Location: migration_records.php?error=db_error");
        exit;
    }
}

// Function to handle AJAX view of migration record
function view_migration() {
    global $conn;
    
    // For AJAX requests, set the content type
    header('Content-Type: application/json');
    
    // Validate the migration_id parameter
    if (!isset($_POST['migration_id']) || empty($_POST['migration_id'])) {
        echo json_encode([
            'status' => 'error',
            'message' => 'Missing migration ID'
        ]);
        exit;
    }
    
    $migration_id = (int)$_POST['migration_id'];
    
    try {
        // Query to get migration record with resident and recorder details
        $query = "SELECT mr.*, 
                 CONCAT(r.last_name, ', ', r.first_name, ' ', COALESCE(r.middle_name, '')) as resident_name,
                 r.status as resident_status,
                 o.official_id,
                 CONCAT(ro.last_name, ', ', ro.first_name) as recorder_name
                 FROM migration_records mr
                 JOIN residents r ON mr.resident_id = r.resident_id
                 LEFT JOIN officials o ON mr.recorded_by = o.official_id
                 LEFT JOIN residents ro ON o.resident_id = ro.resident_id
                 WHERE ";
        
        // Check if we're using record_id or migration_id field
        $column_exists_query = "SHOW COLUMNS FROM migration_records LIKE 'migration_id'";
        $column_exists_stmt = $conn->prepare($column_exists_query);
        $column_exists_stmt->execute();
        
        if ($column_exists_stmt->rowCount() > 0) {
            $query .= "mr.migration_id = :id";
        } else {
            $query .= "mr.record_id = :id";
        }
        
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':id', $migration_id, PDO::PARAM_INT);
        $stmt->execute();
        
        if ($stmt->rowCount() === 0) {
            echo json_encode([
                'status' => 'error',
                'message' => 'Migration record not found'
            ]);
            exit;
        }
        
        $migration = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Format the data for display
        $migration_date = new DateTime($migration['migration_date']);
        $formatted_date = $migration_date->format('F d, Y');
        
        // Determine status badge class
        $type_class = 'primary';
        $badge_class = 'bg-primary';
        $type_emoji = '📋';
        
        $migration_type = $migration['migration_type'];
        if ($migration_type === 'In' || $migration_type === 'Arrival') {
            $type_class = 'success';
            $badge_class = 'bg-success';
            $type_emoji = '📥';
        } elseif ($migration_type === 'Out' || $migration_type === 'Departure') {
            $type_class = 'danger';
            $badge_class = 'bg-danger';
            $type_emoji = '📤';
        } elseif ($migration_type === 'Temporary' || $migration_type === 'Transfer') {
            $type_class = 'warning';
            $badge_class = 'bg-warning';
            $type_emoji = '⏱️';
        }
        
        // Generate HTML for the modal content using a cleaner design
        $record_id = isset($migration['migration_id']) ? $migration['migration_id'] : $migration['record_id'];
        
        $html = '
        <div class="d-flex align-items-center mb-3">
            <div class="me-3">
                <i class="fas fa-user-circle text-primary fa-3x"></i>
            </div>
            <div>
                <h5 class="fw-bold mb-0">' . htmlspecialchars($migration['resident_name']) . '</h5>
                <p class="text-muted mb-0">ID: #' . str_pad($record_id, 4, '0', STR_PAD_LEFT) . '</p>
            </div>
            <span class="badge ' . $badge_class . ' ms-auto px-3 py-2">
                ' . $type_emoji . ' ' . htmlspecialchars($migration_type) . '
            </span>
        </div>
        
        <div class="row mt-4 mb-3">
            <div class="col-md-6 mb-3">
                <div class="d-flex align-items-start">
                    <div class="me-2 text-primary">
                        📅
                    </div>
                    <div>
                        <h6 class="fw-bold mb-1">Migration Date</h6>
                        <p class="mb-0">' . $formatted_date . '</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 mb-3">
                <div class="d-flex align-items-start">
                    <div class="me-2 text-' . $type_class . '">
                        ' . $type_emoji . '
                    </div>
                    <div>
                        <h6 class="fw-bold mb-1">Migration Type</h6>
                        <p class="mb-0">' . htmlspecialchars($migration_type) . '</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mb-3">
            <div class="col-md-6 mb-3">
                <div class="d-flex align-items-start">
                    <div class="me-2 text-danger">
                        🏠
                    </div>
                    <div>
                        <h6 class="fw-bold mb-1">Origin Address</h6>
                        <p class="mb-0">' . htmlspecialchars($migration['origin_address']) . '</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 mb-3">
                <div class="d-flex align-items-start">
                    <div class="me-2 text-success">
                        🏢
                    </div>
                    <div>
                        <h6 class="fw-bold mb-1">Destination Address</h6>
                        <p class="mb-0">' . htmlspecialchars($migration['destination_address']) . '</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mb-4">
            <div class="d-flex align-items-start">
                <div class="me-2 text-purple">
                    📝
                </div>
                <div>
                    <h6 class="fw-bold mb-1">Reason for Migration</h6>
                    <div class="p-3 bg-light rounded">' . 
                        (empty($migration['reason']) ? 'No reason specified' : htmlspecialchars($migration['reason'])) . 
                    '</div>
                </div>
            </div>
        </div>';
        
        if (!empty($migration['remarks'])) {
            $html .= '
            <div class="mb-3">
                <h6 class="fw-bold mb-2">
                    <i class="fas fa-sticky-note me-2 text-warning"></i> Remarks
                </h6>
                <div class="p-3 bg-light rounded">' . htmlspecialchars($migration['remarks']) . '</div>
            </div>';
        }
        
        $html .= '
        <div class="mb-3">
            <div class="d-flex align-items-start">
                <div class="me-2 text-info">
                    📋
                </div>
                <div class="w-100">
                    <h6 class="fw-bold mb-2">Additional Information</h6>
                    <table class="table table-sm table-striped bg-light rounded">
                        <tr>
                            <td width="140" class="fw-bold">Remarks:</td>
                            <td>' . (empty($migration['remarks']) ? 'No remarks' : htmlspecialchars($migration['remarks'])) . '</td>
                        </tr>
                        <tr>
                            <td class="fw-bold">Recorded By:</td>
                            <td>';
        
        if (!empty($migration['recorder_name'])) {
            $html .= htmlspecialchars($migration['recorder_name']);
        } elseif (!empty($migration['recorded_by'])) {
            $html .= 'Official ID: ' . htmlspecialchars($migration['recorded_by']);
        } else {
            $html .= 'System';
        }
        
        $html .= '</td>
                        </tr>';
        
        if (!empty($migration['date_recorded'])) {
            $date_recorded = new DateTime($migration['date_recorded']);
            $formatted_recorded_date = $date_recorded->format('F d, Y g:i A');
            
            $html .= '
                        <tr>
                            <td class="fw-bold">Date Recorded:</td>
                            <td>' . $formatted_recorded_date . '</td>
                        </tr>';
        }
        
        $html .= '
                    </table>
                </div>
            </div>
        </div>';
        
        // Return the formatted data
        echo json_encode([
            'status' => 'success',
            'html' => $html,
            'data' => $migration
        ]);
        
    } catch (Exception $e) {
        error_log("Error viewing migration record: " . $e->getMessage());
        echo json_encode([
            'status' => 'error',
            'message' => 'Failed to load migration details: ' . $e->getMessage()
        ]);
    }
    
    exit;
}
?> 