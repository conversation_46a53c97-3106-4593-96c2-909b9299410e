<?php
/**
 * Utility Functions
 * Barangay Management System
 */

// Note: sanitize() function is already defined in functions.php

/**
 * Generate CSRF token and store in session
 * @return string The generated token
 */
function generate_csrf_token() {
    if (!isset($_SESSION['csrf_token']) || empty($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time'])) {
        // If token doesn't exist or token time doesn't exist, generate a new one
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        $_SESSION['csrf_token_time'] = time();
    } else if (time() - $_SESSION['csrf_token_time'] > 3600) {
        // Regenerate token after 1 hour
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        $_SESSION['csrf_token_time'] = time();
    }
    return $_SESSION['csrf_token'];
}

/**
 * Validate CSRF token
 * @param string $token Token to validate
 * @return bool Whether the token is valid
 */
function validate_csrf_token($token) {
    if (!isset($_SESSION['csrf_token']) || empty($_SESSION['csrf_token'])) {
        return false;
    }

    // Check token validity
    return hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * Generate a CSRF token input field for forms
 * @return string HTML for the CSRF token field
 */
function csrf_token_field() {
    $token = generate_csrf_token();
    return '<input type="hidden" name="csrf_token" value="' . $token . '">';
}

// Note: formatDate() function is already defined in functions.php

// Note: hasPermission() function moved to includes/functions/permission_functions.php
// for better database-driven role-based permission checking

// Note: generateReferenceNumber() function is already defined in functions.php

/**
 * Generate a pagination
 * @param int $totalRecords
 * @param int $recordsPerPage
 * @param int $currentPage
 * @param string $url
 * @return string
 */
function generatePagination($totalRecords, $recordsPerPage, $currentPage, $url) {
    $totalPages = ceil($totalRecords / $recordsPerPage);

    if ($totalPages <= 1) {
        return '';
    }

    $pagination = '<nav aria-label="Page navigation"><ul class="pagination justify-content-center">';

    // Previous button
    if ($currentPage > 1) {
        $pagination .= '<li class="page-item"><a class="page-link" href="' . $url . '?page=' . ($currentPage - 1) . '">&laquo;</a></li>';
    } else {
        $pagination .= '<li class="page-item disabled"><a class="page-link" href="#">&laquo;</a></li>';
    }

    // Page numbers
    $startPage = max(1, $currentPage - 2);
    $endPage = min($totalPages, $currentPage + 2);

    if ($startPage > 1) {
        $pagination .= '<li class="page-item"><a class="page-link" href="' . $url . '?page=1">1</a></li>';
        if ($startPage > 2) {
            $pagination .= '<li class="page-item disabled"><a class="page-link" href="#">...</a></li>';
        }
    }

    for ($i = $startPage; $i <= $endPage; $i++) {
        if ($i == $currentPage) {
            $pagination .= '<li class="page-item active"><a class="page-link" href="#">' . $i . '</a></li>';
        } else {
            $pagination .= '<li class="page-item"><a class="page-link" href="' . $url . '?page=' . $i . '">' . $i . '</a></li>';
        }
    }

    if ($endPage < $totalPages) {
        if ($endPage < $totalPages - 1) {
            $pagination .= '<li class="page-item disabled"><a class="page-link" href="#">...</a></li>';
        }
        $pagination .= '<li class="page-item"><a class="page-link" href="' . $url . '?page=' . $totalPages . '">' . $totalPages . '</a></li>';
    }

    // Next button
    if ($currentPage < $totalPages) {
        $pagination .= '<li class="page-item"><a class="page-link" href="' . $url . '?page=' . ($currentPage + 1) . '">&raquo;</a></li>';
    } else {
        $pagination .= '<li class="page-item disabled"><a class="page-link" href="#">&raquo;</a></li>';
    }

    $pagination .= '</ul></nav>';

    return $pagination;
}

/**
 * Log an activity in the system
 *
 * @param PDO $conn Database connection
 * @param int $user_id User ID who performed the action
 * @param string $action_type Type of action (e.g., 'login', 'logout', 'create', 'update', 'delete')
 * @param string $action_details Details of the action
 * @param string $module The module where the action was performed
 * @param int|null $reference_id ID of the related record (optional)
 * @return bool True if logged successfully, false otherwise
 */
function log_activity($conn, $user_id, $action_type, $action_details, $module = 'system', $reference_id = null) {
    try {
        // Check if activity_logs table exists
        $check_table = $conn->prepare("SHOW TABLES LIKE 'activity_logs'");
        $check_table->execute();

        if ($check_table->rowCount() === 0) {
            // Create activity_logs table if it doesn't exist
            $create_table = "CREATE TABLE activity_logs (
                log_id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT,
                action_type VARCHAR(50) NOT NULL,
                action_details TEXT NOT NULL,
                module VARCHAR(50) NOT NULL,
                reference_id INT NULL,
                ip_address VARCHAR(50),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL
            )";
            $conn->exec($create_table);
        }

        $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';

        $query = "INSERT INTO activity_logs
                  (user_id, action_type, action_details, module, reference_id, ip_address, user_agent)
                  VALUES
                  (:user_id, :action_type, :action_details, :module, :reference_id, :ip_address, :user_agent)";

        $stmt = $conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id);
        $stmt->bindParam(':action_type', $action_type);
        $stmt->bindParam(':action_details', $action_details);
        $stmt->bindParam(':module', $module);
        $stmt->bindParam(':reference_id', $reference_id);
        $stmt->bindParam(':ip_address', $ip_address);
        $stmt->bindParam(':user_agent', $user_agent);

        return $stmt->execute();
    } catch (PDOException $e) {
        // Log the error but don't stop execution
        error_log("Error logging activity: " . $e->getMessage());
        return false;
    }
}

/**
 * Get status badge
 * @param string $status
 * @return string
 */
function getStatusBadge($status) {
    switch (strtolower($status)) {
        case 'active':
            return '<span class="badge badge-active">Active</span>';
        case 'inactive':
            return '<span class="badge badge-inactive">Inactive</span>';
        case 'pending':
            return '<span class="badge badge-pending">Pending</span>';
        case 'processing':
            return '<span class="badge badge-processing">Processing</span>';
        case 'completed':
        case 'released':
        case 'ready for pickup':
            return '<span class="badge badge-completed">' . ucfirst($status) . '</span>';
        case 'cancelled':
        case 'dismissed':
            return '<span class="badge badge-cancelled">' . ucfirst($status) . '</span>';
        default:
            return '<span class="badge bg-secondary">' . ucfirst($status) . '</span>';
    }
}

/**
 * Send SMS to phone number
 *
 * @param string $phoneNumber The recipient phone number
 * @param string $message The SMS message
 * @return bool True on success, false on failure
 */
function sendSMS($phoneNumber, $message) {
    // This is a placeholder function
    // Replace with actual SMS API integration

    // For now, just log the SMS
    global $conn;
    log_activity($conn, 0, 'SMS Notification', "SMS sent to $phoneNumber: $message", 'system');

    return true;
}

/**
 * Calculate age from birthdate
 * @param string $birthdate
 * @return int
 */
function calculateAge($birthdate) {
    $today = new DateTime();
    $birthDate = new DateTime($birthdate);
    $interval = $today->diff($birthDate);
    return $interval->y;
}

/**
 * Generate household code
 * @return string Unique household code
 */
function generateHouseholdCode() {
    global $conn;

    $prefix = "HH";
    $year = date('Y');

    try {
        $pattern = "$prefix-$year-%";
        $query = "SELECT MAX(SUBSTRING(household_code, 9)) as max_num FROM households WHERE household_code LIKE ?";
        $stmt = $conn->prepare($query);
        $stmt->execute([$pattern]);
        $row = $stmt->fetch();

        $max_num = intval($row['max_num'] ?? 0);
        $next_num = $max_num + 1;

        return $prefix . "-" . $year . "-" . str_pad($next_num, 4, '0', STR_PAD_LEFT);
    } catch (PDOException $e) {
        // Fallback if query fails
        return $prefix . "-" . $year . "-" . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    }
}

/**
 * Truncate text to a certain length
 * @param string $text
 * @param int $length
 * @param string $append
 * @return string
 */
function truncateText($text, $length = 100, $append = '...') {
    if (strlen($text) > $length) {
        $text = substr($text, 0, $length) . $append;
    }
    return $text;
}

/**
 * Calculate age from birthdate
 *
 * @param string $birthdate Date in Y-m-d format
 * @return int Age in years
 */
function calculate_age($birthdate) {
    $birth_date = new DateTime($birthdate);
    $today = new DateTime('today');
    $age = $birth_date->diff($today)->y;
    return $age;
}

/**
 * Generate a reference number (alternative version with different signature)
 *
 * @param string $prefix Prefix for the reference number
 * @param int $length Length of the numeric part
 * @return string Generated reference number
 */
function generate_reference_number($prefix, $length = 8) {
    $timestamp = time();
    $random = mt_rand(100, 999);
    $numeric_part = substr($timestamp . $random, -$length);
    return $prefix . date('Ymd') . $numeric_part;
}

/**
 * Format currency amount
 *
 * @param float $amount The amount to format
 * @param string $currency Currency symbol (default: ₱)
 * @return string Formatted currency
 */
function format_currency($amount, $currency = '₱') {
    return $currency . number_format($amount, 2);
}

/**
 * Get a setting value from the database
 * @param mixed $conn Database connection
 * @param string $setting_name Setting name to retrieve
 * @param string $default Default value if setting is not found
 * @return string Setting value
 */
function get_setting($conn, $setting_name, $default = '') {
    // First try to get from settings table
    try {
        $query = "SELECT setting_value FROM settings WHERE setting_name = :setting_name";
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':setting_name', $setting_name, PDO::PARAM_STR);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            return $row['setting_value'];
        }

        // Try to get from barangay_info table
        $query = "SELECT * FROM barangay_info LIMIT 1";
        $stmt = $conn->query($query);

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            if (isset($row[$setting_name])) {
                return $row[$setting_name];
            }
        }

        // Try to get from system_settings table
        $query = "SELECT value FROM system_settings WHERE setting = :setting_name";
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':setting_name', $setting_name, PDO::PARAM_STR);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            return $row['value'];
        }

        // Try to get from config table
        $query = "SELECT value FROM config WHERE name = :setting_name";
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':setting_name', $setting_name, PDO::PARAM_STR);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            return $row['value'];
        }
    } catch (PDOException $e) {
        // Log error or handle exception
        error_log("Error fetching setting: " . $e->getMessage());
    }

    return $default;
}

/**
 * Send system notification
 *
 * @param PDO $conn The database connection
 * @param int $recipient_id The recipient ID
 * @param string $recipient_type The recipient type ('Resident', 'Official', 'Group')
 * @param string $subject The notification subject
 * @param string $message The notification message
 * @param string $notification_type The notification type ('SMS', 'Email', 'System')
 * @param int $sent_by The user ID who sent the notification
 * @return bool True on success, false on failure
 */
function send_notification($conn, $recipient_id, $recipient_type, $subject, $message, $notification_type, $sent_by) {
    try {
        $query = "INSERT INTO notification_logs (recipient_id, recipient_type, notification_type, subject, message, sent_by, status)
                  VALUES (:recipient_id, :recipient_type, :notification_type, :subject, :message, :sent_by, 'Pending')";

        $stmt = $conn->prepare($query);
        $stmt->bindParam(':recipient_id', $recipient_id, PDO::PARAM_INT);
        $stmt->bindParam(':recipient_type', $recipient_type, PDO::PARAM_STR);
        $stmt->bindParam(':notification_type', $notification_type, PDO::PARAM_STR);
        $stmt->bindParam(':subject', $subject, PDO::PARAM_STR);
        $stmt->bindParam(':message', $message, PDO::PARAM_STR);
        $stmt->bindParam(':sent_by', $sent_by, PDO::PARAM_INT);

        if ($stmt->execute()) {
            $notification_id = $conn->lastInsertId();

            // Here you would add actual notification sending code based on the type
            // For example, call an SMS API or send an email

            // Update status to 'Sent'
            $update_query = "UPDATE notification_logs SET status = 'Sent' WHERE notification_id = :notification_id";
            $update_stmt = $conn->prepare($update_query);
            $update_stmt->bindParam(':notification_id', $notification_id, PDO::PARAM_INT);
            $update_stmt->execute();

            return true;
        }
    } catch (PDOException $e) {
        error_log("Error sending notification: " . $e->getMessage());
    }

    return false;
}

/**
 * Check if user has permission for an action
 *
 * @param PDO $conn The database connection
 * @param int $user_id The user ID
 * @param string $permission The permission to check
 * @return bool True if user has permission, false otherwise
 */
function has_permission($conn, $user_id, $permission) {
    try {
        $query = "SELECT permissions FROM users WHERE user_id = :user_id";
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch();
            if (empty($row['permissions'])) {
                return false;
            }

            $permissions = json_decode($row['permissions'], true);
            return in_array($permission, $permissions);
        }
    } catch (PDOException $e) {
        error_log("Error checking permissions: " . $e->getMessage());
    }

    return false;
}

/**
 * Format date in a user-friendly format
 *
 * @param string $date The date to format
 * @param string $format The format to use (default: 'F d, Y')
 * @return string Formatted date
 */
function format_date($date, $format = 'F d, Y') {
    return date($format, strtotime($date));
}

// Note: formatDate() function is defined in functions.php

/**
 * Get logo path from system settings with fallback
 *
 * @param PDO $conn Database connection
 * @param string $relative_path Relative path prefix (e.g., '../' for resident portal)
 * @return string Logo path
 */
function get_logo_path($conn, $relative_path = '') {
    $default_logo = $relative_path . "assets/images/logo-brgy-removebg.png";

    try {
        $query = "SELECT setting_value FROM system_settings WHERE setting_name = 'logo' LIMIT 1";
        $stmt = $conn->prepare($query);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            if (!empty($row['setting_value'])) {
                $uploaded_logo_path = $relative_path . "assets/img/logos/" . $row['setting_value'];
                if (file_exists($uploaded_logo_path)) {
                    return $uploaded_logo_path;
                }
            }
        }
    } catch (PDOException $e) {
        error_log("Error fetching logo from system settings: " . $e->getMessage());
    }

    // Fallback logic
    $fallback_paths = [
        $relative_path . "assets/img/logo.jpeg",
        $relative_path . "assets/images/logo.jpeg",
        $relative_path . "assets/images/IMG_0039-removebg-preview.png",
        $default_logo
    ];

    foreach ($fallback_paths as $path) {
        if (file_exists($path)) {
            return $path;
        }
    }

    return $default_logo;
}

/**
 * Get system name with proper handling of "Management System" suffix
 *
 * @param string $barangay_name The barangay name from settings
 * @param string $suffix The suffix to add if not already present (default: "Management System")
 * @return string Properly formatted system name
 */
function get_system_name($barangay_name, $suffix = "Management System") {
    // Check if the barangay name already contains the suffix
    if (stripos($barangay_name, $suffix) !== false) {
        return $barangay_name; // Use as is
    } else {
        return $barangay_name . " " . $suffix; // Add suffix
    }
}

/**
 * Get favicon path from system settings with fallback
 *
 * @param PDO $conn Database connection
 * @param string $relative_path Relative path prefix (e.g., '../' for resident portal)
 * @return string Favicon path
 */
function get_favicon_path($conn, $relative_path = '') {
    $default_favicon = $relative_path . "assets/images/logo.jpeg";

    try {
        $query = "SELECT setting_value FROM system_settings WHERE setting_name = 'favicon' LIMIT 1";
        $stmt = $conn->prepare($query);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            if (!empty($row['setting_value'])) {
                $uploaded_favicon_path = $relative_path . "assets/images/" . $row['setting_value'];
                if (file_exists($uploaded_favicon_path)) {
                    return $uploaded_favicon_path;
                }
            }
        }
    } catch (PDOException $e) {
        error_log("Error fetching favicon from system settings: " . $e->getMessage());
    }

    // Fallback logic
    $fallback_paths = [
        $relative_path . "assets/img/favicon.png",
        $relative_path . "assets/images/favicon.png",
        $relative_path . "assets/images/favicon.ico",
        $relative_path . "assets/images/logo.jpeg",
        $default_favicon
    ];

    foreach ($fallback_paths as $path) {
        if (file_exists($path)) {
            return $path;
        }
    }

    return $default_favicon;
}

/**
 * Get favicon URL for HTML head section
 *
 * @param PDO $conn Database connection
 * @param string $relative_path Relative path prefix (e.g., '../' for resident portal)
 * @return string Favicon URL with proper versioning
 */
function get_favicon_url($conn, $relative_path = '') {
    // Check if favicon is set in session (for immediate updates)
    if (isset($_SESSION['favicon']) && !empty($_SESSION['favicon'])) {
        $timestamp = isset($_SESSION['favicon_timestamp']) ? '?v=' . $_SESSION['favicon_timestamp'] : '';
        return $relative_path . "assets/images/" . $_SESSION['favicon'] . $timestamp;
    }

    try {
        $query = "SELECT setting_value FROM system_settings WHERE setting_name = 'favicon' LIMIT 1";
        $stmt = $conn->prepare($query);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            if (!empty($row['setting_value'])) {
                return $relative_path . "assets/images/" . $row['setting_value'] . "?v=1.1";
            }
        }
    } catch (PDOException $e) {
        error_log("Error fetching favicon from system settings: " . $e->getMessage());
    }

    // Return default favicon
    return $relative_path . "assets/images/logo.jpeg?v=1.1";
}

/**
 * Upload a file
 *
 * @param array $file The $_FILES array element
 * @param string $destination The destination directory
 * @param array $allowed_types Array of allowed MIME types
 * @param int $max_size Maximum file size in bytes
 * @return array Status and file path/error message
 */
function upload_file($file, $destination, $allowed_types = array(), $max_size = 5242880) {
    // Check if file was uploaded
    if (!isset($file) || $file['error'] != UPLOAD_ERR_OK) {
        return array('status' => false, 'message' => 'No file uploaded or upload error');
    }

    // Check file size
    if ($file['size'] > $max_size) {
        return array('status' => false, 'message' => 'File size exceeds the maximum limit');
    }

    // Check file type if specified
    if (!empty($allowed_types) && !in_array($file['type'], $allowed_types)) {
        return array('status' => false, 'message' => 'File type not allowed');
    }

    // Create destination directory if it doesn't exist
    if (!is_dir($destination)) {
        mkdir($destination, 0755, true);
    }

    // Generate a unique filename
    $filename = time() . '_' . basename($file['name']);
    $filepath = $destination . '/' . $filename;

    // Move the uploaded file
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return array('status' => true, 'filepath' => $filepath, 'filename' => $filename);
    } else {
        return array('status' => false, 'message' => 'Failed to move uploaded file');
    }
}

/**
 * Upload template file with specific validation for Word documents
 * @param array $file The uploaded file array
 * @param string $destination The destination directory
 * @param array $allowed_types Array of allowed MIME types
 * @param int $max_size Maximum file size in bytes
 * @return array Status and file path/error message
 */
function upload_template_file($file, $destination, $allowed_types = array(), $max_size = 10485760) {
    // Check if file was uploaded
    if (!isset($file) || $file['error'] != UPLOAD_ERR_OK) {
        return array('status' => false, 'message' => 'No template file uploaded or upload error');
    }

    // Check file size
    if ($file['size'] > $max_size) {
        return array('status' => false, 'message' => 'Template file is too large. Maximum size is ' . ($max_size / 1024 / 1024) . 'MB');
    }

    // Check file type
    if (!empty($allowed_types) && !in_array($file['type'], $allowed_types)) {
        return array('status' => false, 'message' => 'Invalid template file type. Only Word documents (.docx) are allowed');
    }

    // Check file extension
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if ($file_extension !== 'docx') {
        return array('status' => false, 'message' => 'Invalid template file extension. Only .docx files are allowed');
    }

    // Create destination directory if it doesn't exist
    if (!is_dir($destination)) {
        if (!mkdir($destination, 0755, true)) {
            return array('status' => false, 'message' => 'Failed to create template directory');
        }
    }

    // Generate unique filename
    $filename = time() . '_' . preg_replace('/[^a-zA-Z0-9._-]/', '_', $file['name']);
    $destination_path = $destination . '/' . $filename;

    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $destination_path)) {
        return array('status' => false, 'message' => 'Failed to save template file');
    }

    return array('status' => true, 'filename' => $filename, 'path' => $destination_path);
}

/**
 * Generate QR code for resident
 *
 * @param mixed $conn The database connection (PDO)
 * @param int $resident_id The resident ID
 * @return string|false Path to QR code image or false on failure
 */
function generate_qr_code($conn, $resident_id) {
    // Check if phpqrcode library exists
    if (!file_exists('includes/vendor/phpqrcode/qrlib.php')) {
        return false;
    }

    // Include phpqrcode
    require_once 'includes/vendor/phpqrcode/qrlib.php';

    // Get resident data
    $resident = get_resident($conn, $resident_id);

    if (!$resident) {
        return false;
    }

    // Create QR data
    $data = json_encode(array(
        'resident_id' => $resident['resident_id'],
        'name' => $resident['first_name'] . ' ' . $resident['last_name'],
        'barangay' => get_setting($conn, 'barangay_name', 'Unknown Barangay'),
        'timestamp' => time()
    ));

    // Create directory if not exists
    $dir = 'uploads/qrcodes/';
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }

    // Generate QR code image
    $filename = $dir . 'resident_' . $resident_id . '_' . time() . '.png';

    // Note: This requires the phpqrcode library to be properly installed
    // The QRcode class and QR_ECLEVEL_M constant come from that library
    // If you get linter errors, make sure the library is installed correctly
    if (class_exists('QRcode')) {
        QRcode::png($data, $filename, QR_ECLEVEL_M, 10);
    } else {
        // Fallback if QRcode class is not available
        file_put_contents($filename, 'QR placeholder');
        error_log("QRcode class not available. Please install phpqrcode library.");
    }

    // Save reference in database
    try {
        $query = "INSERT INTO resident_qrcodes (resident_id, qr_path, generated_on) VALUES (:resident_id, :qr_path, NOW())";
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':resident_id', $resident_id, PDO::PARAM_INT);
        $stmt->bindParam(':qr_path', $filename);
        $stmt->execute();

        $qr_id = $conn->lastInsertId();

        // Update resident record
        $update_query = "UPDATE residents SET qrcode_id = :qr_id WHERE resident_id = :resident_id";
        $stmt = $conn->prepare($update_query);
        $stmt->bindParam(':qr_id', $qr_id, PDO::PARAM_INT);
        $stmt->bindParam(':resident_id', $resident_id, PDO::PARAM_INT);
        $stmt->execute();

        return $filename;
    } catch (PDOException $e) {
        error_log("Error generating QR code: " . $e->getMessage());
        return false;
    }
}

/**
 * Sanitize input using PDO connection for extra safety
 * @param PDO $conn
 * @param string $data
 * @return string
 */
function sanitize_input($conn, $data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * Get the absolute path for a file based on the current script's location
 *
 * @param string $path The path to convert to an absolute path
 * @return string The absolute path
 */
function get_absolute_path($path) {
    global $base_url;

    // Get the document root
    $docRoot = $_SERVER['DOCUMENT_ROOT'];

    // Get the current script's directory
    $currentDir = dirname($_SERVER['SCRIPT_NAME']);

    // Make consistent forward slashes
    $path = str_replace('\\', '/', $path);
    $currentDir = str_replace('\\', '/', $currentDir);
    $docRoot = str_replace('\\', '/', $docRoot);

    // If using base_url is preferred and it's defined
    if (isset($base_url) && !empty($base_url)) {
        return $base_url . '/' . ltrim($path, '/');
    }

    // If path is already absolute, return it as is
    if (strpos($path, '/') === 0) {
        return $path;
    }

    // If this is the site root or a root-relative path
    if ($currentDir === '/' || strpos($path, '/') === 0) {
        return $path;
    }

    // Get the project root
    // (assumes the site is installed directly in document root or in a subdirectory)
    $projectRoot = '/';
    $projectName = explode('/', trim($currentDir, '/'))[0];
    if (!empty($projectName)) {
        $projectRoot = '/' . $projectName . '/';
    }

    // Build relative path to navigate from current directory to project root
    $relPathToRoot = '';
    $dirDepth = substr_count($currentDir, '/') - substr_count($projectRoot, '/') + 1;
    if ($dirDepth > 0) {
        $relPathToRoot = str_repeat('../', $dirDepth);
    }

    // Combine the path components
    return $relPathToRoot . ltrim($path, '/');
}

/**
 * Get user information
 * @param mixed $conn Database connection
 * @param int $user_id User ID
 * @return array|null User data or null if not found
 */
function get_user($conn, $user_id) {
    try {
        $query = "SELECT * FROM users WHERE user_id = :user_id";
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            return $stmt->fetch(PDO::FETCH_ASSOC);
        }
    } catch (PDOException $e) {
        // Log error or handle exception
        error_log("Error fetching user: " . $e->getMessage());
    }

    return null;
}

/**
 * Get resident information
 * @param mixed $conn Database connection
 * @param int $resident_id Resident ID
 * @return array|null Resident data or null if not found
 */
function get_resident($conn, $resident_id) {
    try {
        $query = "SELECT * FROM residents WHERE resident_id = :resident_id";
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':resident_id', $resident_id, PDO::PARAM_INT);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            return $stmt->fetch(PDO::FETCH_ASSOC);
        }
    } catch (PDOException $e) {
        // Log error or handle exception
        error_log("Error fetching resident: " . $e->getMessage());
    }

    return null;
}

/**
 * Get full name from resident data
 *
 * @param array $resident The resident data array
 * @param bool $last_name_first Whether to display last name first
 * @return string The full name
 */
function get_full_name($resident, $last_name_first = true) {
    if (empty($resident)) {
        return 'Unknown';
    }

    if ($last_name_first) {
        return $resident['last_name'] . ', ' . $resident['first_name'] . ' ' . $resident['middle_name'];
    } else {
        return $resident['first_name'] . ' ' . $resident['middle_name'] . ' ' . $resident['last_name'];
    }
}

/**
 * Convert mysqli query to PDO prepared statement
 * @param string $query MySQL query with mysqli placeholders (e.g. ?)
 * @param array $params Array of parameters to bind
 * @param object $conn PDO connection
 * @return PDOStatement
 */
function execute_pdo_query($query, $params = [], $conn = null) {
    if ($conn === null) {
        global $conn;
    }

    try {
        $stmt = $conn->prepare($query);
        $stmt->execute($params);
        return $stmt;
    } catch (PDOException $e) {
        error_log("PDO Query Error: " . $e->getMessage() . " - Query: " . $query);
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            echo "Database Error: " . $e->getMessage();
        }
        return false;
    }
}

/**
 * Begin a PDO transaction
 * @param object $conn PDO connection
 * @return bool
 */
function begin_transaction($conn = null) {
    if ($conn === null) {
        global $conn;
    }

    try {
        return $conn->beginTransaction();
    } catch (PDOException $e) {
        error_log("PDO Transaction Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Commit a PDO transaction
 * @param object $conn PDO connection
 * @return bool
 */
function commit_transaction($conn = null) {
    if ($conn === null) {
        global $conn;
    }

    try {
        return $conn->commit();
    } catch (PDOException $e) {
        error_log("PDO Commit Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Rollback a PDO transaction
 * @param object $conn PDO connection
 * @return bool
 */
function rollback_transaction($conn = null) {
    if ($conn === null) {
        global $conn;
    }

    try {
        return $conn->rollBack();
    } catch (PDOException $e) {
        error_log("PDO Rollback Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Add new document types to the system
 *
 * @param PDO $conn Database connection
 * @param array $document_types Array of document types to add
 * @return array Result with success/error messages
 */
function add_document_types($conn, $document_types) {
    $results = [];

    try {
        $conn->beginTransaction();

        // First, get current ENUM values from document_requests table
        $enum_query = "SHOW COLUMNS FROM document_requests LIKE 'document_type'";
        $enum_stmt = $conn->prepare($enum_query);
        $enum_stmt->execute();
        $enum_result = $enum_stmt->fetch(PDO::FETCH_ASSOC);

        if ($enum_result) {
            // Parse current ENUM values
            $enum_string = $enum_result['Type'];
            preg_match_all("/'([^']+)'/", $enum_string, $matches);
            $current_values = $matches[1];

            // Add new document types to ENUM if not already present
            $new_values = array_merge($current_values, array_keys($document_types));
            $new_values = array_unique($new_values);

            // Build new ENUM string
            $enum_values = "'" . implode("','", $new_values) . "'";

            // Update the ENUM column
            $alter_query = "ALTER TABLE document_requests MODIFY COLUMN document_type ENUM($enum_values) NOT NULL";
            $conn->prepare($alter_query)->execute();

            $results[] = "✅ Updated document_requests table ENUM values";
        }

        // Add entries to document_settings table
        foreach ($document_types as $doc_type => $settings) {
            // Check if document type already exists
            $check_query = "SELECT COUNT(*) FROM document_settings WHERE document_type = ?";
            $check_stmt = $conn->prepare($check_query);
            $check_stmt->execute([$doc_type]);

            if ($check_stmt->fetchColumn() == 0) {
                // Insert new document type
                $insert_query = "INSERT INTO document_settings (document_type, fee_amount, processing_days, requirements, is_active, created_at, updated_at)
                                VALUES (?, ?, ?, ?, 1, NOW(), NOW())";
                $insert_stmt = $conn->prepare($insert_query);
                $insert_stmt->execute([
                    $doc_type,
                    $settings['fee'],
                    $settings['processing_days'],
                    json_encode($settings['requirements'])
                ]);

                $results[] = "✅ Added '$doc_type' to document_settings table";
            } else {
                $results[] = "ℹ️ '$doc_type' already exists in document_settings table";
            }
        }

        $conn->commit();
        $results[] = "🎉 All document types added successfully!";

    } catch (PDOException $e) {
        $conn->rollBack();
        $results[] = "❌ Error: " . $e->getMessage();
    }

    return $results;
}

// Note: logActivity() function is already defined in functions.php