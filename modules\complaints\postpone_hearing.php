<?php
session_start();
include '../../includes/config/database.php';
include '../../includes/functions/utility.php';
include '../../includes/functions/permission_functions.php';
include '../../includes/functions/functions.php';
include '../../includes/functions/log_function.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check permission
if (!hasPermission('update_hearing')) {
    header("Location: ../../index.php");
    exit;
}

// Initialize variables
$success_message = '';
$error_message = '';
$hearing = null;

// Get hearing ID from URL
$hearing_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($hearing_id <= 0) {
    $_SESSION['error'] = "Invalid hearing ID";
    header("Location: hearings.php");
    exit;
}

// Fetch hearing details
try {
    $query = "SELECT h.*, c.complaint_type, c.complainant_name, c.respondent_name 
              FROM hearings h 
              LEFT JOIN complaints c ON h.complaint_id = c.complaint_id
              WHERE h.hearing_id = :hearing_id";
    
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':hearing_id', $hearing_id, PDO::PARAM_INT);
    $stmt->execute();
    
    if ($stmt->rowCount() == 0) {
        $_SESSION['error'] = "Hearing not found";
        header("Location: hearings.php");
        exit;
    }
    
    $hearing = $stmt->fetch();
    
    // Check if hearing can be postponed
    if ($hearing['status'] != 'Scheduled') {
        $_SESSION['error'] = "Only scheduled hearings can be postponed";
        header("Location: view_hearing.php?id=" . $hearing_id);
        exit;
    }
    
} catch (PDOException $e) {
    $_SESSION['error'] = "Error fetching hearing details: " . $e->getMessage();
    header("Location: hearings.php");
    exit;
}

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get form data
    $new_date = sanitize($_POST['new_date']);
    $new_time = sanitize($_POST['new_time']);
    $reason = sanitize($_POST['reason']);
    
    // Validate required fields
    if (empty($new_date) || empty($reason)) {
        $error_message = "New date and reason are required";
    } else {
        try {
            // Begin transaction
            $conn->beginTransaction();
            
            // Update hearing status to Postponed
            $update_query = "UPDATE hearings SET 
                            status = 'Postponed',
                            notes = CONCAT(IFNULL(notes, ''), '\n\nPostponed on ', NOW(), '\nReason: ', :reason, '\nOriginal date: ', hearing_date, ' ', IFNULL(hearing_time, '')),
                            hearing_date = :new_date,
                            hearing_time = :new_time,
                            updated_at = NOW()
                            WHERE hearing_id = :hearing_id";
            
            $update_stmt = $conn->prepare($update_query);
            $update_stmt->bindParam(':reason', $reason);
            $update_stmt->bindParam(':new_date', $new_date);
            $update_stmt->bindParam(':new_time', $new_time);
            $update_stmt->bindParam(':hearing_id', $hearing_id, PDO::PARAM_INT);
            $update_stmt->execute();
            
            // Log activity
            log_activity_safe($conn, $_SESSION['user_id'], 'Postpone Hearing', "Postponed hearing #$hearing_id to $new_date", 'complaints', $hearing_id);
            
            // Commit transaction
            $conn->commit();
            
            $_SESSION['message'] = "Hearing has been postponed successfully";
            $_SESSION['message_type'] = "success";
            
            // Redirect to view hearing page
            header("Location: view_hearing.php?id=" . $hearing_id);
            exit;
        } catch (PDOException $e) {
            // Rollback transaction on error
            if ($conn->inTransaction()) {
                $conn->rollBack();
            }
            
            $error_message = "Error postponing hearing: " . $e->getMessage();
        }
    }
}

// Page title
$page_title = "Postpone Hearing - Barangay Management System";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border: none;
            margin-bottom: 24px;
        }
        
        .card-header {
            background-color: rgba(246, 194, 62, 0.1);
            border-bottom: none;
            padding: 15px 20px;
        }
        
        .form-label {
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .required-indicator {
            color: #dc3545;
            font-weight: bold;
        }
        
        .btn-warning {
            background-color: #f6c23e;
            border-color: #f6c23e;
        }
        
        .btn-warning:hover {
            background-color: #e0a800;
            border-color: #e0a800;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="fas fa-calendar-alt me-2 text-warning"></i> Postpone Hearing</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="../../index.php">Home</a></li>
                            <li class="breadcrumb-item"><a href="hearings.php">Hearings</a></li>
                            <li class="breadcrumb-item"><a href="view_hearing.php?id=<?php echo $hearing_id; ?>">View Hearing</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Postpone Hearing</li>
                        </ol>
                    </nav>
                </div>
                
                <?php if (!empty($success_message)): ?>
                <div class="alert alert-success" role="alert">
                    <i class="fas fa-check-circle me-2"></i> <?php echo $success_message; ?>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i> <?php echo $error_message; ?>
                </div>
                <?php endif; ?>
                
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-calendar-alt me-2 text-warning"></i> Postpone Hearing
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="alert alert-warning mb-4">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Warning:</strong> You are about to postpone this hearing. This action will change the status of the hearing to "Postponed" and reschedule it to a new date.
                                </div>
                                
                                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]) . '?id=' . $hearing_id; ?>" method="POST">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="new_date" class="form-label">New Hearing Date <span class="required-indicator">*</span></label>
                                            <input type="date" class="form-control" id="new_date" name="new_date" required min="<?php echo date('Y-m-d'); ?>">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="new_time" class="form-label">New Hearing Time</label>
                                            <input type="time" class="form-control" id="new_time" name="new_time">
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="reason" class="form-label">Reason for Postponement <span class="required-indicator">*</span></label>
                                        <textarea class="form-control" id="reason" name="reason" rows="4" required></textarea>
                                        <small class="text-muted">Provide a detailed reason why this hearing needs to be postponed.</small>
                                    </div>
                                    
                                    <div class="d-flex justify-content-end mt-4">
                                        <a href="view_hearing.php?id=<?php echo $hearing_id; ?>" class="btn btn-secondary me-2">
                                            <i class="fas fa-times me-1"></i> Cancel
                                        </a>
                                        <button type="submit" class="btn btn-warning">
                                            <i class="fas fa-calendar-alt me-1"></i> Postpone Hearing
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-info-circle me-2"></i> Current Hearing Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <p><strong>Hearing ID:</strong> #<?php echo str_pad($hearing['hearing_id'], 4, '0', STR_PAD_LEFT); ?></p>
                                <p><strong>Status:</strong> <?php echo htmlspecialchars($hearing['status']); ?></p>
                                <p><strong>Current Date:</strong> <?php echo date('F d, Y', strtotime($hearing['hearing_date'])); ?></p>
                                <p><strong>Current Time:</strong> <?php echo isset($hearing['hearing_time']) ? date('h:i A', strtotime($hearing['hearing_time'])) : 'Not specified'; ?></p>
                                <p><strong>Complaint:</strong> #<?php echo str_pad($hearing['complaint_id'], 5, '0', STR_PAD_LEFT); ?> - <?php echo htmlspecialchars($hearing['complaint_type']); ?></p>
                                <p><strong>Complainant:</strong> <?php echo htmlspecialchars($hearing['complainant_name']); ?></p>
                                <p><strong>Respondent:</strong> <?php echo htmlspecialchars($hearing['respondent_name']); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</body>
</html>
