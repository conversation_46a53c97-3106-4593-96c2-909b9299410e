<?php
session_start();
include '../../includes/functions/permission_functions.php';
require_once '../../includes/config/database.php';
require_once '../../includes/functions/functions.php';
require_once '../../includes/functions/utility.php';

// Fallback function definition if not loaded from functions.php
if (!function_exists('formatDate')) {
    function formatDate($date, $format = 'M d, Y') {
        if (empty($date) || $date === '0000-00-00' || $date === '0000-00-00 00:00:00') {
            return 'N/A';
        }
        return date($format, strtotime($date));
    }
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check permission
if (!hasPermission('view_complaints')) {
    header("Location: ../../index.php");
    exit;
}

// Page title
$page_title = "Complaints and Blotter - Barangay Management System";

// Process search
$search = isset($_GET['search']) ? sanitize($_GET['search']) : '';
$search_condition = '';
$params = [];

if (!empty($search)) {
    $search_condition = " AND (
        c.complaint_type LIKE :search OR 
        c.complaint_details LIKE :search OR 
        c.incident_location LIKE :search OR 
        comp.first_name LIKE :search OR 
        comp.last_name LIKE :search OR 
        resp.first_name LIKE :search OR 
        resp.last_name LIKE :search
    )";
    $params[':search'] = '%' . $search . '%';
}

// Process status filter
$status_filter = isset($_GET['status']) ? sanitize($_GET['status']) : '';
$status_condition = '';
if (!empty($status_filter)) {
    $status_condition = " AND c.status = :status";
    $params[':status'] = $status_filter;
}

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$records_per_page = 10;
$offset = ($page - 1) * $records_per_page;

// Get total records
$count_query = "SELECT COUNT(*) as total FROM complaints c 
                LEFT JOIN residents comp ON c.complainant_id = comp.resident_id
                LEFT JOIN residents resp ON c.respondent_id = resp.resident_id
                WHERE 1=1 $search_condition $status_condition";
try {
    $count_stmt = $conn->prepare($count_query);
    foreach ($params as $key => $value) {
        $count_stmt->bindValue($key, $value);
    }
    $count_stmt->execute();
    $total_records = $count_stmt->fetchColumn();
    $total_pages = ceil($total_records / $records_per_page);
} catch (PDOException $e) {
    error_log("Error in count query: " . $e->getMessage());
    $total_records = 0;
    $total_pages = 1;
}

// Get complaints list
$query = "SELECT c.*, 
          comp.first_name as complainant_fname, comp.middle_name as complainant_mname, comp.last_name as complainant_lname,
          resp.first_name as respondent_fname, resp.middle_name as respondent_mname, resp.last_name as respondent_lname,
          o.position as official_position, 
          CONCAT(oRes.first_name, ' ', oRes.last_name) as official_name
          FROM complaints c 
          LEFT JOIN residents comp ON c.complainant_id = comp.resident_id
          LEFT JOIN residents resp ON c.respondent_id = resp.resident_id
          LEFT JOIN officials o ON c.assigned_official_id = o.official_id
          LEFT JOIN residents oRes ON o.resident_id = oRes.resident_id
          WHERE 1=1 $search_condition $status_condition
          ORDER BY c.date_filed DESC 
          LIMIT :offset, :per_page";
try {
    $stmt = $conn->prepare($query);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->bindValue(':per_page', $records_per_page, PDO::PARAM_INT);
    $stmt->execute();
    $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error in complaints query: " . $e->getMessage());
    $result = [];
}

// Get status counts
$status_query = "SELECT status, COUNT(*) as count FROM complaints GROUP BY status";
try {
    $status_stmt = $conn->prepare($status_query);
    $status_stmt->execute();
    $status_counts = [];
    while ($row = $status_stmt->fetch(PDO::FETCH_ASSOC)) {
        $status_counts[$row['status']] = $row['count'];
    }
} catch (PDOException $e) {
    error_log("Error in status count query: " . $e->getMessage());
    $status_counts = [];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>

    <!-- Favicon -->
    <link rel="icon" href="<?php echo get_favicon_url($conn, '../../'); ?>" type="image/png">

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        /* Custom styling for enhanced visual appearance */
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border: none;
            margin-bottom: 24px;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }
        
        /* Border left styling */
        .border-left-primary {
            border-left: 4px solid #4e73df;
        }
        
        .border-left-success {
            border-left: 4px solid #1cc88a;
        }
        
        .border-left-info {
            border-left: 4px solid #36b9cc;
        }
        
        .border-left-warning {
            border-left: 4px solid #f6c23e;
        }
        
        .border-left-danger {
            border-left: 4px solid #e74a3b;
        }
        
        /* Improved badge styling */
        .badge {
            font-size: 0.85em;
            padding: 0.5em 0.8em;
            border-radius: 30px;
        }
        
        /* Enhanced button styling */
        .btn {
            border-radius: 5px;
            transition: all 0.2s;
        }
        
        .btn:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        /* Table styling */
        .table {
            border-radius: 8px;
            overflow: hidden;
        }
        
        .table thead th {
            background-color: #f8f9fc;
            border-bottom: 2px solid #e3e6f0;
            font-weight: 600;
        }
        
        .table-hover tbody tr:hover {
            background-color: rgba(78, 115, 223, 0.05);
        }
        
        /* Table styles */
        .table-bordered-columns th,
        .table-bordered-columns td {
            border-right: 1px solid #dee2e6;
            border-bottom: 1px solid #dee2e6;
        }
        .table-bordered-columns th:last-child,
        .table-bordered-columns td:last-child {
            border-right: none;
        }
        .table-bordered-columns {
            border-left: 1px solid #dee2e6;
            border-right: 1px solid #dee2e6;
            border-top: 1px solid #dee2e6;
        }
        .table-bordered-columns thead th {
            border-bottom: 2px solid #4e73df;
            background-color: #f8f9fc;
        }
        
        /* Action buttons styling */
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 5px;
        }
        
        .action-column {
            text-align: center;
            min-width: 120px;
            white-space: nowrap;
        }
        
        /* Custom tab styling */
        .nav-tabs .nav-link {
            border: none;
            color: #5a5c69;
            font-weight: 500;
            padding: 10px 15px;
        }
        
        .nav-tabs .nav-link.active {
            color: #4e73df;
            border-bottom: 3px solid #4e73df;
            background-color: transparent;
        }
        
        /* Search form styling */
        .search-form {
            border-radius: 30px;
            overflow: hidden;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }
        
        .search-form .form-control {
            border-radius: 30px 0 0 30px;
            border: none;
            padding-left: 20px;
        }
        
        .search-form .btn {
            border-radius: 0 30px 30px 0;
            padding-right: 20px;
        }
        
        /* Stat Card Styles */
        .stat-card {
            border-radius: 0.75rem;
            overflow: hidden;
            height: 100%;
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            position: relative;
            z-index: 1;
        }
        .stat-card:hover {
            transform: translateY(-5px) !important;
            box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2) !important;
            z-index: 2;
            cursor: pointer;
        }
        .stat-card:hover .stat-icon {
            transform: scale(1.1);
        }
        .stat-icon {
            font-size: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 60px;
            width: 60px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }
        .bg-primary-soft {
            background-color: rgba(78, 115, 223, 0.1);
        }
        .bg-success-soft {
            background-color: rgba(28, 200, 138, 0.1);
        }
        .bg-warning-soft {
            background-color: rgba(246, 194, 62, 0.1);
        }
        .bg-danger-soft {
            background-color: rgba(231, 74, 59, 0.1);
        }
        .bg-info-soft {
            background-color: rgba(54, 185, 204, 0.1);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">📋 Complaints & Blotter Management</h1>
                    <?php if (hasPermission('add_complaint')): ?>
                    <a href="<?php echo get_absolute_path('modules/complaints/add_complaint.php'); ?>" class="btn btn-primary">
                        ➕ File New Complaint
                    </a>
                    <?php endif; ?>
                </div>

                <!-- Quick Statistics -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card border-primary">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-primary-soft text-primary">
                                            📋
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1"><?php echo number_format($total_records); ?></h4>
                                        <p class="mb-0 text-muted">Total Complaints</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card border-warning">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-warning-soft text-warning">
                                            ⏳
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1"><?php echo number_format($status_counts['Pending'] ?? 0); ?></h4>
                                        <p class="mb-0 text-muted">Pending</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card border-info">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-info-soft text-info">
                                            🔍
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1"><?php echo number_format($status_counts['Under Investigation'] ?? 0); ?></h4>
                                        <p class="mb-0 text-muted">Under Investigation</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card border-success">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-success-soft text-success">
                                            ✅
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1"><?php echo number_format($status_counts['Resolved'] ?? 0); ?></h4>
                                        <p class="mb-0 text-muted">Resolved</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Status Tabs -->
                <div class="card border-left-primary shadow mb-4">
                    <div class="card-header bg-white">
                        <ul class="nav nav-tabs card-header-tabs">
                            <li class="nav-item">
                                <a class="nav-link <?php echo empty($status_filter) ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/complaints/complaints.php'); ?>">
                                    🗂️ All Complaints <span class="badge bg-secondary"><?php echo $total_records; ?></span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo $status_filter == 'Pending' ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/complaints/complaints.php'); ?>?status=Pending">
                                    ⏳ Pending <span class="badge bg-warning"><?php echo $status_counts['Pending'] ?? 0; ?></span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo $status_filter == 'Under Investigation' ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/complaints/complaints.php'); ?>?status=Under%20Investigation">
                                    🔍 Under Investigation <span class="badge bg-info"><?php echo $status_counts['Under Investigation'] ?? 0; ?></span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo $status_filter == 'Resolved' ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/complaints/complaints.php'); ?>?status=Resolved">
                                    ✅ Resolved <span class="badge bg-success"><?php echo $status_counts['Resolved'] ?? 0; ?></span>
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link <?php echo $status_filter == 'Dismissed' ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/complaints/complaints.php'); ?>?status=Dismissed">
                                    ❌ Dismissed <span class="badge bg-danger"><?php echo $status_counts['Dismissed'] ?? 0; ?></span>
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="card-body">
                        <!-- Search & Filter Row -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <form action="<?php echo get_absolute_path('modules/complaints/complaints.php'); ?>" method="get" class="d-flex search-form">
                                    <input type="text" name="search" class="form-control me-2" placeholder="🔎 Search complaints..." value="<?php echo htmlspecialchars($search); ?>">
                                    <?php if (!empty($status_filter)): ?>
                                    <input type="hidden" name="status" value="<?php echo htmlspecialchars($status_filter); ?>">
                                    <?php endif; ?>
                                    <button type="submit" class="btn btn-primary">Search</button>
                                </form>
                            </div>
                            <div class="col-md-6 text-end">
                                <button class="btn btn-outline-secondary ms-2">
                                    🖨️ Print
                                </button>
                                <button class="btn btn-outline-success ms-2" onclick="exportToExcel()">
                                    📊 Export
                                </button>
                            </div>
                        </div>
                        
                        <!-- Complaints Table -->
                        <div class="table-responsive">
                            <table class="table table-hover table-bordered-columns">
                                <thead class="table-light">
                                    <tr>
                                        <th>📝 Ref #</th>
                                        <th>🔖 Type</th>
                                        <th>👤 Complainant</th>
                                        <th>👥 Respondent</th>
                                        <th>📆 Incident Date</th>
                                        <th>📅 Date Filed</th>
                                        <th>🚦 Status</th>
                                        <th>👮‍♂️ Assigned To</th>
                                        <th class="action-column">⚙️ Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    if (count($result) > 0) {
                                        foreach ($result as $complaint) {
                                            // Get complainant name
                                            $complainant_name = 'N/A';
                                            if (!empty($complaint['complainant_id'])) {
                                                $complainant_name = $complaint['complainant_lname'] . ', ' . $complaint['complainant_fname'] . ' ' . $complaint['complainant_mname'];
                                            } elseif (!empty($complaint['complainant_name'])) {
                                                $complainant_name = $complaint['complainant_name'];
                                            }
                                            
                                            // Get respondent name
                                            $respondent_name = 'N/A';
                                            if (!empty($complaint['respondent_id'])) {
                                                $respondent_name = $complaint['respondent_lname'] . ', ' . $complaint['respondent_fname'] . ' ' . $complaint['respondent_mname'];
                                            } elseif (!empty($complaint['respondent_name'])) {
                                                $respondent_name = $complaint['respondent_name'];
                                            }
                                            
                                            // Get status badge class and emoji
                                            $status_class = '';
                                            $status_emoji = '';
                                            switch ($complaint['status']) {
                                                case 'Pending':
                                                    $status_class = 'bg-warning';
                                                    $status_emoji = '⏳ ';
                                                    break;
                                                case 'Under Investigation':
                                                    $status_class = 'bg-info';
                                                    $status_emoji = '🔍 ';
                                                    break;
                                                case 'Resolved':
                                                    $status_class = 'bg-success';
                                                    $status_emoji = '✅ ';
                                                    break;
                                                case 'Dismissed':
                                                    $status_class = 'bg-danger';
                                                    $status_emoji = '❌ ';
                                                    break;
                                                default:
                                                    $status_class = 'bg-secondary';
                                                    $status_emoji = '🔄 ';
                                            }
                                            
                                            echo "<tr>";
                                            echo "<td>" . $complaint['complaint_id'] . "</td>";
                                            echo "<td>" . $complaint['complaint_type'] . "</td>";
                                            echo "<td>" . $complainant_name . "</td>";
                                            echo "<td>" . $respondent_name . "</td>";
                                            echo "<td>" . formatDate($complaint['incident_date']) . "</td>";
                                            echo "<td>" . formatDate($complaint['date_filed']) . "</td>";
                                            echo "<td><span class='badge " . $status_class . "'>" . $status_emoji . $complaint['status'] . "</span></td>";
                                            echo "<td>" . ($complaint['official_name'] ? $complaint['official_name'] . '<br><small>(' . $complaint['official_position'] . ')</small>' : 'Unassigned') . "</td>";
                                            echo "<td>";
                                            echo "<div class='action-buttons'>";
                                            echo "<a href='" . get_absolute_path('modules/complaints/view_complaint.php') . "?id=" . $complaint['complaint_id'] . "' class='btn btn-sm btn-info' data-bs-toggle='tooltip' title='View'><i class='fas fa-eye'></i></a> ";
                                            
                                            if (hasPermission('edit_complaint') && $complaint['status'] != 'Resolved' && $complaint['status'] != 'Dismissed') {
                                                echo "<a href='" . get_absolute_path('modules/complaints/edit_complaint.php') . "?id=" . $complaint['complaint_id'] . "' class='btn btn-sm btn-primary' data-bs-toggle='tooltip' title='Edit'><i class='fas fa-edit'></i></a> ";
                                            }
                                            
                                            if (hasPermission('edit_complaint') && ($complaint['status'] == 'Pending' || $complaint['status'] == 'Under Investigation')) {
                                                echo "<a href='" . get_absolute_path('modules/complaints/update_status.php') . "?id=" . $complaint['complaint_id'] . "' class='btn btn-sm btn-warning' data-bs-toggle='tooltip' title='Update Status'><i class='fas fa-sync-alt'></i></a> ";
                                            }
                                            
                                            if (hasPermission('delete_complaint') && $complaint['status'] == 'Pending') {
                                                echo "<a href='" . get_absolute_path('modules/complaints/delete_complaint.php') . "?id=" . $complaint['complaint_id'] . "' class='btn btn-sm btn-danger btn-delete' data-bs-toggle='tooltip' title='Delete'><i class='fas fa-trash'></i></a>";
                                            }
                                            echo "</div>";
                                            echo "</td>";
                                            echo "</tr>";
                                        }
                                    } else {
                                        echo "<tr><td colspan='9' class='text-center'>No complaints found</td></tr>";
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <?php
                        $url = get_absolute_path('modules/complaints/complaints.php');
                        if (!empty($search)) {
                            $url .= "?search=" . urlencode($search);
                            if (!empty($status_filter)) {
                                $url .= "&status=" . urlencode($status_filter);
                            }
                        } elseif (!empty($status_filter)) {
                            $url .= "?status=" . urlencode($status_filter);
                        }
                        
                        echo generatePagination($total_records, $records_per_page, $page, $url);
                        ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
    <script src="../../assets/js/main.js"></script>
    
    <script>
        // Function to show toast notifications
        function showToast(message, type = 'success') {
            // Create toast element
            const toast = document.createElement('div');
            toast.className = 'toast align-items-center text-white bg-' + type + ' border-0';
            toast.setAttribute('role', 'alert');
            toast.setAttribute('aria-live', 'assertive');
            toast.setAttribute('aria-atomic', 'true');
            
            // Set position with CSS
            toast.style.position = 'fixed';
            toast.style.top = '1rem';
            toast.style.right = '1rem';
            toast.style.zIndex = '9999';
            
            // Create toast content with emoji based on type
            let emoji = '✅';
            if (type === 'danger') emoji = '❌';
            if (type === 'info') emoji = 'ℹ️';
            if (type === 'warning') emoji = '⚠️';
            
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        ${emoji} ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            `;
            
            // Append to body
            document.body.appendChild(toast);
            
            // Initialize and show toast
            const bsToast = new bootstrap.Toast(toast, {
                autohide: true,
                delay: 5000
            });
            bsToast.show();
            
            // Remove from DOM after hidden
            toast.addEventListener('hidden.bs.toast', function () {
                document.body.removeChild(toast);
            });
        }
        
        // Check for session messages
        document.addEventListener('DOMContentLoaded', function() {
            <?php if(isset($_SESSION['success'])): ?>
                showToast("<?php echo $_SESSION['success']; ?>", "success");
                <?php unset($_SESSION['success']); ?>
            <?php endif; ?>
            
            <?php if(isset($_SESSION['error'])): ?>
                showToast("<?php echo $_SESSION['error']; ?>", "danger");
                <?php unset($_SESSION['error']); ?>
            <?php endif; ?>
            
            <?php if(isset($_SESSION['info'])): ?>
                showToast("<?php echo $_SESSION['info']; ?>", "info");
                <?php unset($_SESSION['info']); ?>
            <?php endif; ?>
        });
        
        function exportToExcel() {
            // Placeholder for Excel export functionality
            alert('Export to Excel functionality will be implemented here');
        }
        
        // Initialize tooltips
        document.addEventListener('DOMContentLoaded', function() {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl)
            });
        });
    </script>
</body>
</html> 