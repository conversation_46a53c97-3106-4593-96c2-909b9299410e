<?php
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

session_start();
include '../../includes/config/database.php';
include '../../includes/functions/permission_functions.php';
include '../../includes/functions/utility.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check permission
if (!hasPermission('view_residents')) {
    header("Location: ../../index.php");
    exit;
}

// Set page title
$page_title = "Migration Records - Barangay Management System";

// Initialize variables
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;
$offset = ($page - 1) * $limit;
$search = isset($_GET['search']) ? $_GET['search'] : '';
$type_filter = isset($_GET['type']) ? $_GET['type'] : '';
$error_message = '';
$success_message = '';
$show_debug = false; // Set to true to show debug information

// Check for success messages
if (isset($_GET['success'])) {
    switch ($_GET['success']) {
        case 'added':
            $success_message = "Migration record has been added successfully.";
            break;
        case 'deleted':
            $success_message = "Migration record has been deleted successfully.";
            break;
        case 'updated':
            $success_message = "Migration record has been updated successfully.";
            break;
    }
}

// Check for error messages
if (isset($_GET['error'])) {
    switch ($_GET['error']) {
        case 'not_found':
            $error_message = "Migration record not found.";
            break;
        case 'db_error':
            $error_message = "A database error occurred. Please try again.";
            break;
        case 'validation':
            $error_message = "Please fill in all required fields.";
            if (isset($_GET['fields'])) {
                $missing_fields = explode(',', $_GET['fields']);
                $error_message .= " Missing: " . implode(', ', $missing_fields);
            }
            break;
        case 'foreign_key':
            $error_message = "This resident cannot be used for a migration record. There may be a database constraint issue.";
            break;
        case 'general_error':
            $error_message = "An unexpected error occurred. Please try again or contact support.";
                break;
        case 'invalid_request':
            $error_message = "Invalid request. Please try again.";
                break;
        case 'invalid_action':
            $error_message = "Invalid action requested. Please try again.";
                break;
        default:
            $error_message = "An error occurred: " . $_GET['error'];
    }
}

// Load residents for dropdowns
try {
    $residents_query = "SELECT resident_id, 
                      CONCAT(last_name, ', ', first_name, ' ', COALESCE(middle_name, '')) as full_name,
                      status
                      FROM residents 
                      ORDER BY last_name, first_name";
    $residents_stmt = $conn->prepare($residents_query);
    $residents_stmt->execute();
    $residents = $residents_stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
    error_log("Error fetching residents: " . $e->getMessage());
    $residents = [];
}

// Load migration records with pagination
try {
    // Build the search condition
    $search_condition = "";
    $params = [];
    
    if (!empty($search)) {
        $search_condition = " WHERE (
            r.first_name LIKE :search1 OR 
            r.last_name LIKE :search2 OR 
            mr.origin_address LIKE :search3 OR 
            mr.destination_address LIKE :search4 OR
            mr.reason LIKE :search5
        )";
        $search_param = "%$search%";
        $params['search1'] = $search_param;
        $params['search2'] = $search_param;
        $params['search3'] = $search_param;
        $params['search4'] = $search_param;
        $params['search5'] = $search_param;
    }
    
    if (!empty($type_filter)) {
        if (empty($search_condition)) {
            $search_condition = " WHERE mr.migration_type = :type";
        } else {
            $search_condition .= " AND mr.migration_type = :type";
        }
        $params['type'] = $type_filter;
    }
    
    // Count total records for pagination
    $count_query = "SELECT COUNT(*) as total FROM migration_records mr
                  JOIN residents r ON mr.resident_id = r.resident_id" . $search_condition;
    
    $count_stmt = $conn->prepare($count_query);
    foreach ($params as $key => $value) {
        $count_stmt->bindValue(":$key", $value);
    }
    $count_stmt->execute();
    $total_records = $count_stmt->fetchColumn();
    $total_pages = ceil($total_records / $limit);
    
    // Get migration records with resident names
    $migrations_query = "SELECT mr.*, 
                        CONCAT(r.last_name, ', ', r.first_name, ' ', COALESCE(r.middle_name, '')) as resident_name,
                        r.status as resident_status,
                        o.official_id,
                        CONCAT(ro.last_name, ', ', ro.first_name) as recorder_name
           FROM migration_records mr
           JOIN residents r ON mr.resident_id = r.resident_id
           LEFT JOIN officials o ON mr.recorded_by = o.official_id
           LEFT JOIN residents ro ON o.resident_id = ro.resident_id" . $search_condition . "
           ORDER BY mr.migration_date DESC, r.last_name, r.first_name
           LIMIT :offset, :limit";
    
    $migrations_stmt = $conn->prepare($migrations_query);
    foreach ($params as $key => $value) {
        $migrations_stmt->bindValue(":$key", $value);
    }
    $migrations_stmt->bindValue(":offset", $offset, PDO::PARAM_INT);
    $migrations_stmt->bindValue(":limit", $limit, PDO::PARAM_INT);
    $migrations_stmt->execute();
    $migrations = $migrations_stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    error_log("Error fetching migration records: " . $e->getMessage());
    $migrations = [];
    $total_pages = 0;
}

// For debugging
if ($show_debug && isset($_GET['error'])) {
    error_log("Error in migration_records.php: " . $_GET['error']);
    if (isset($_GET['fields'])) {
        error_log("Missing fields: " . $_GET['fields']);
    }
}

// Stats Cards
$in_count = 0;
$out_count = 0;
$temp_count = 0;

try {
    // Count migrations by type
    $stats_query = "SELECT migration_type, COUNT(*) as count FROM migration_records GROUP BY migration_type";
    $stats_stmt = $conn->prepare($stats_query);
    $stats_stmt->execute();
    $stats = $stats_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($stats as $stat) {
        $type = isset($stat['migration_type']) ? $stat['migration_type'] : '';
        $count = isset($stat['count']) ? $stat['count'] : 0;
        
        // Handle different naming conventions
        if ($type === 'In' || $type === 'Arrival') {
            $in_count += $count;
        } elseif ($type === 'Out' || $type === 'Departure') {
            $out_count += $count;
        } elseif ($type === 'Temporary' || $type === 'Transfer') {
            $temp_count += $count;
        }
    }
} catch (PDOException $e) {
    // Silently fail, stats will show 0
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        body { 
            padding: 20px; 
            background-color: #f8f9fa;
        }
        .container-fluid { 
            max-width: 1800px;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 1.5rem;
            border-radius: 0.5rem;
            overflow: hidden;
        }
        .card-header {
            font-weight: 600;
        }
        
        /* Left border colors */
        .border-left-primary { border-left: 4px solid #4e73df !important; }
        .border-left-success { border-left: 4px solid #1cc88a !important; }
        .border-left-info { border-left: 4px solid #36b9cc !important; }
        .border-left-warning { border-left: 4px solid #f6c23e !important; }
        .border-left-danger { border-left: 4px solid #e74a3b !important; }
        .border-left-secondary { border-left: 4px solid #858796 !important; }
        .border-left-purple { border-left: 4px solid #6f42c1 !important; }
        .border-left-orange { border-left: 4px solid #fd7e14 !important; }
        .border-left-teal { border-left: 4px solid #20c997 !important; }
        
        /* Text colors */
        .text-primary { color: #4e73df !important; }
        .text-success { color: #1cc88a !important; }
        .text-info { color: #36b9cc !important; }
        .text-warning { color: #f6c23e !important; }
        .text-danger { color: #e74a3b !important; }
        .text-purple { color: #6f42c1 !important; }
        .text-orange { color: #fd7e14 !important; }
        .text-teal { color: #20c997 !important; }
        
        /* Background colors with opacity */
        .bg-primary-soft { background-color: rgba(78, 115, 223, 0.1) !important; }
        .bg-success-soft { background-color: rgba(28, 200, 138, 0.1) !important; }
        .bg-info-soft { background-color: rgba(54, 185, 204, 0.1) !important; }
        .bg-warning-soft { background-color: rgba(246, 194, 62, 0.1) !important; }
        .bg-danger-soft { background-color: rgba(231, 74, 59, 0.1) !important; }
        .bg-purple-soft { background-color: rgba(111, 66, 193, 0.1) !important; }
        .bg-orange-soft { background-color: rgba(253, 126, 20, 0.1) !important; }
        .bg-teal-soft { background-color: rgba(32, 201, 151, 0.1) !important; }
        
        /* Button styles */
        .btn {
            border-radius: 0.35rem;
            padding: 0.375rem 1rem;
            transition: all 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
        }
        
        /* Badge styles */
        .badge {
            font-weight: 600;
            padding: 0.4em 0.8em;
            border-radius: 0.35rem;
        }
        
        /* Icon boxes */
        .icon-box {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
        }
        .icon-box i {
            width: 28px;
            text-align: center;
        }
        
        /* Card stats */
        .stat-card {
            border-radius: 0.5rem;
            overflow: hidden;
            height: 100%;
        }
        .stat-card i {
            font-size: 2rem;
            opacity: 0.3;
        }
        
        /* Table styles */
        .table {
            border-radius: 0.5rem;
            overflow: hidden;
        }
        .table th {
            background-color: #f8f9fc;
            border-bottom-width: 1px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.85rem;
            color: #4e73df;
        }
        
        /* Type badges */
        .badge-in { background-color: #4e73df; color: white; }
        .badge-out { background-color: #e74a3b; color: white; }
        .badge-temporary { background-color: #f6c23e; color: #444; }
        
        /* Select2 custom styling */
        .select2-container--bootstrap-5 .select2-selection {
            border-radius: 0.35rem;
            padding: 0.25rem 0.5rem;
            border-color: #d1d3e2;
        }
        .select2-container--bootstrap-5.select2-container--focus .select2-selection {
            border-color: #bac8f3;
            box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
        }
        
        /* Print styles */
        @media print {
            body {
                padding: 0;
                background: white;
            }
            .container-fluid {
                width: 100%;
                max-width: 100%;
            }
            .no-print, .modal-footer, .sidebar, .navbar, header, footer,
            nav, .card-header, .btn-toolbar, .alert {
                display: none !important;
            }
            .modal {
                position: absolute;
                left: 0;
                top: 0;
                margin: 0;
                padding: 0;
                min-height: auto;
                height: auto;
                overflow: visible !important;
            }
            .modal-dialog {
                max-width: 100%;
                margin: 0;
            }
            .modal-content {
                border: none;
                box-shadow: none;
            }
            .modal-body {
                padding: 0;
            }
            .modal.fade .modal-dialog {
                transform: none !important;
            }
            .modal.show .modal-dialog {
                transform: none !important;
            }
            .card {
                border: 1px solid #dee2e6;
                box-shadow: none;
                break-inside: avoid;
            }
            .row {
                display: flex;
                flex-wrap: wrap;
            }
            
            /* Add a title at the top of the print */
            #viewMigrationModal .modal-body::before {
                content: "MIGRATION RECORD DETAILS";
                display: block;
                font-size: 24px;
                font-weight: bold;
                text-align: center;
                margin-bottom: 20px;
                border-bottom: 2px solid #000;
                padding-bottom: 10px;
            }
            
            /* Add barangay logo and info at the top for official document feel */
            #viewMigrationModal .modal-body::after {
                content: "Barangay Management System\nOfficial Document";
                display: block;
                font-size: 14px;
                text-align: center;
                margin-top: 30px;
                font-style: italic;
            }
        }
        
        /* Status badges */
        .badge-in {
            background-color: #28a745;
            color: white;
        }
        .badge-out {
            background-color: #dc3545;
            color: white;
        }
        .badge-temporary {
            background-color: #ffc107;
            color: black;
        }
        
        /* Action buttons */
        .action-buttons .btn {
            margin: 0 2px;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.2s ease;
        }
        
        .action-buttons .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
        
        /* Stat Card Styles */
        .stat-card {
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
        }
        .stat-icon {
            font-size: 2.5rem;
            opacity: 0.8;
        }
        
        /* Border left colors */
        .border-left-primary {
            border-left: 4px solid #4e73df;
        }
        .border-left-success {
            border-left: 4px solid #1cc88a;
        }
        .border-left-info {
            border-left: 4px solid #36b9cc;
        }
        .border-left-warning {
            border-left: 4px solid #f6c23e;
        }
        .border-left-danger {
            border-left: 4px solid #e74a3b;
        }
    </style>
</head>
<body>
            <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <main class="col-md-8 ms-sm-auto col-lg-10 px-md-0">
                <!-- Page Title and Actions -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3">
                    <h1 class="h3 fw-bold">
                        <span class="text-primary">🔄</span> Migration Records
                    </h1>
                    <?php if (hasPermission('add_migration')): ?>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addMigrationModal">
                            ➕ Add Migration Record
                        </button>
                    </div>
                    <?php endif; ?>
                </div>
                
                <!-- Alert Messages -->
                <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    ✅ <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    ❌ <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <!-- Stats Cards -->
                <div class="row mb-4">
                    <!-- In-Migration -->
                    <div class="col-xl-4 col-md-6 mb-4">
                        <div class="card stat-card border-left-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                            In-Migration (Move-in)</div>
                                        <div class="h3 mb-0 font-weight-bold"><?php echo $in_count; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <span class="stat-icon text-primary">➡️</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Out-Migration -->
                    <div class="col-xl-4 col-md-6 mb-4">
                        <div class="card stat-card border-left-danger shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                            Out-Migration (Move-out)</div>
                                        <div class="h3 mb-0 font-weight-bold"><?php echo $out_count; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <span class="stat-icon text-danger">⬅️</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Temporary Migration -->
                    <div class="col-xl-4 col-md-6 mb-4">
                        <div class="card stat-card border-left-warning shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row no-gutters align-items-center">
                                    <div class="col mr-2">
                                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                            Temporary Migration</div>
                                        <div class="h3 mb-0 font-weight-bold"><?php echo $temp_count; ?></div>
                                    </div>
                                    <div class="col-auto">
                                        <span class="stat-icon text-warning">↔️</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Search and Filters -->
                <div class="card bg-white shadow-sm rounded-3 border-0 mb-4">
                    <div class="card-body p-4">
                        <h6 class="mb-3 fw-bold">🔍 Search & Filters</h6>
                        <form method="GET" action="" class="row g-3">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input type="text" class="form-control" placeholder="Search by name, address, reason..." name="search" value="<?php echo htmlspecialchars($search); ?>">
                                    <button class="btn btn-primary" type="submit">
                                        🔍
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <select class="form-select" name="type">
                                    <option value="">All Migration Types</option>
                                    <option value="In" <?php echo ($type_filter === 'In') ? 'selected' : ''; ?>>In-Migration (Move-in)</option>
                                    <option value="Out" <?php echo ($type_filter === 'Out') ? 'selected' : ''; ?>>Out-Migration (Move-out)</option>
                                    <option value="Temporary" <?php echo ($type_filter === 'Temporary') ? 'selected' : ''; ?>>Temporary</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <?php if (!empty($search) || !empty($type_filter)): ?>
                                <a href="migration_records.php" class="btn btn-secondary w-100">🔄 Clear</a>
                                <?php endif; ?>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Migration Records Table -->
                <div class="card bg-white shadow-sm rounded-3 border-0 mb-4">
                    <div class="card-body p-4">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0 fw-bold">📋 Migration Records</h6>
                            <span class="badge bg-info"><?php echo $total_records; ?> total records</span>
                        </div>
                        <?php if (count($migrations) > 0): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>RESIDENT NAME</th>
                                        <th>TYPE</th>
                                        <th>DATE</th>
                                        <th>ORIGIN ADDRESS</th>
                                        <th>DESTINATION ADDRESS</th>
                                        <th>ACTIONS</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($migrations as $migration): ?>
                                    <tr>
                                        <td>#<?php 
                                        $id_value = isset($migration['migration_id']) ? $migration['migration_id'] : (isset($migration['record_id']) ? $migration['record_id'] : '0');
                                        echo str_pad((string)$id_value, 4, '0', STR_PAD_LEFT); 
                                        ?></td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                👤 
                                            <?php echo htmlspecialchars($migration['resident_name']); ?>
                                                <?php if (isset($migration['resident_status']) && $migration['resident_status'] !== 'Active'): ?>
                                                <span class="badge bg-warning text-dark ms-2">
                                                    <?php echo htmlspecialchars($migration['resident_status']); ?>
                                            </span>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <?php 
                                            $badgeClass = 'badge-in';
                                            $icon = '➡️';
                                            
                                            $migrationType = isset($migration['migration_type']) ? $migration['migration_type'] : '';
                                            
                                            // Check for 'Out' or 'Departure' types
                                            if ($migrationType === 'Out' || $migrationType === 'Departure') {
                                                $badgeClass = 'badge-out';
                                                $icon = '⬅️';
                                            // Check for 'Temporary' or 'Transfer' types
                                            } elseif ($migrationType === 'Temporary' || $migrationType === 'Transfer') {
                                                $badgeClass = 'badge-temporary';
                                                $icon = '↔️';
                                            }
                                            ?>
                                            <span class="badge <?php echo $badgeClass; ?>">
                                                <?php echo $icon; ?> 
                                                <?php echo htmlspecialchars($migrationType); ?>
                                            </span>
                                        </td>
                                        <td>
                                            📅 <?php echo date('M d, Y', strtotime($migration['migration_date'])); ?>
                                        </td>
                                        <td>
                                            <div class="text-wrap" style="max-width: 200px;">
                                                🏠 <?php echo htmlspecialchars($migration['origin_address']); ?>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="text-wrap" style="max-width: 200px;">
                                                🏢 <?php echo htmlspecialchars($migration['destination_address']); ?>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <button class="btn btn-sm btn-info view-migration-btn" data-id="<?php echo isset($migration['migration_id']) ? $migration['migration_id'] : $migration['record_id']; ?>">
                                                👁️
                                            </button>
                                            <?php if (hasPermission('edit_migration')): ?>
                                            <button class="btn btn-sm btn-primary edit-migration-btn" data-id="<?php echo isset($migration['migration_id']) ? $migration['migration_id'] : $migration['record_id']; ?>">
                                                ✏️
                                            </button>
                                            <?php endif; ?>
                                            <?php if (hasPermission('delete_migration')): ?>
                                            <button class="btn btn-sm btn-danger" onclick="confirmDelete(<?php echo isset($migration['migration_id']) ? $migration['migration_id'] : $migration['record_id']; ?>, '<?php echo addslashes($migration['resident_name']); ?>', '<?php echo addslashes($migrationType); ?>')">
                                                🗑️
                                            </button>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <?php if ($total_pages > 1): ?>
                        <div class="d-flex justify-content-center mt-4">
                            <nav aria-label="Migration records pagination">
                                <ul class="pagination">
                                    <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1<?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($type_filter) ? '&type=' . urlencode($type_filter) : ''; ?>" aria-label="First">
                                            <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page - 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($type_filter) ? '&type=' . urlencode($type_filter) : ''; ?>" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                    
                                    <?php
                                    // Determine range of pages to show
                                    $range = 2; // Number of pages to show on each side of current page
                                    $start_page = max(1, $page - $range);
                                    $end_page = min($total_pages, $page + $range);
                                    
                                    // Always show first page
                                    if ($start_page > 1) {
                                        echo '<li class="page-item"><a class="page-link" href="?page=1' . (!empty($search) ? '&search=' . urlencode($search) : '') . (!empty($type_filter) ? '&type=' . urlencode($type_filter) : '') . '">1</a></li>';
                                        if ($start_page > 2) {
                                            echo '<li class="page-item disabled"><a class="page-link" href="#">...</a></li>';
                                        }
                                    }
                                    
                                    // Pages in range
                                    for ($i = $start_page; $i <= $end_page; $i++) {
                                        echo '<li class="page-item ' . ($i == $page ? 'active' : '') . '"><a class="page-link" href="?page=' . $i . (!empty($search) ? '&search=' . urlencode($search) : '') . (!empty($type_filter) ? '&type=' . urlencode($type_filter) : '') . '">' . $i . '</a></li>';
                                    }
                                    
                                    // Always show last page
                                    if ($end_page < $total_pages) {
                                        if ($end_page < $total_pages - 1) {
                                            echo '<li class="page-item disabled"><a class="page-link" href="#">...</a></li>';
                                        }
                                        echo '<li class="page-item"><a class="page-link" href="?page=' . $total_pages . (!empty($search) ? '&search=' . urlencode($search) : '') . (!empty($type_filter) ? '&type=' . urlencode($type_filter) : '') . '">' . $total_pages . '</a></li>';
                                    }
                                    ?>
                                    
                                    <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page + 1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($type_filter) ? '&type=' . urlencode($type_filter) : ''; ?>" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $total_pages; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($type_filter) ? '&type=' . urlencode($type_filter) : ''; ?>" aria-label="Last">
                                            <span aria-hidden="true">&raquo;&raquo;</span>
                                        </a>
                                    </li>
                                    <?php endif; ?>
                            </ul>
                        </nav>
                        </div>
                        <?php endif; ?>
                        
                        <?php else: ?>
                        <div class="text-center py-4">
                            <div class="mb-3">
                                <i class="fas fa-search fa-3x text-gray-300"></i>
                            </div>
                            <h5 class="text-muted">No migration records found</h5>
                            <?php if (!empty($search) || !empty($type_filter)): ?>
                            <p>Try adjusting your search criteria or clear filters</p>
                            <a href="migration_records.php" class="btn btn-outline-primary">
                                <i class="fas fa-sync-alt me-1"></i> Show All Records
                            </a>
                            <?php else: ?>
                            <p>No migration records have been added yet</p>
                            <?php if (hasPermission('add_migration')): ?>
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addMigrationModal">
                                <i class="fas fa-plus me-1"></i> Add New Migration Record
                            </button>
                        <?php endif; ?>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
            </div>
    </div>

<!-- Add Migration Modal -->
    <div class="modal fade" id="addMigrationModal" tabindex="-1" aria-labelledby="addMigrationModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addMigrationModalLabel">➕ Add Migration Record</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
                <!-- Simplified form with direct POST to migration_actions.php -->
                <form action="migration_actions.php" method="POST" class="needs-validation" novalidate>
            <div class="modal-body">
                        <div class="mb-3">
                            <label for="resident_id" class="form-label">👤 Resident <span class="text-danger">*</span></label>
                            <select class="form-select" id="resident_id" name="resident_id" required>
                                <option value="">Select Resident</option>
                                <?php foreach ($residents as $resident): ?>
                                <option value="<?php echo $resident['id']; ?>">
                                    <?php echo htmlspecialchars($resident['last_name'] . ', ' . $resident['first_name'] . ' ' . $resident['middle_name']); ?>
                                    <?php if ($resident['status'] !== 'Active'): ?>
                                    (<?php echo htmlspecialchars($resident['status']); ?>)
                                    <?php endif; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                            <div class="invalid-feedback">Please select a resident</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="migration_type" class="form-label">🔄 Migration Type <span class="text-danger">*</span></label>
                            <select class="form-select" id="migration_type" name="migration_type" required>
                                <option value="">Select Type</option>
                                <option value="In">In (Arrival)</option>
                                <option value="Out">Out (Departure)</option>
                                <option value="Temporary">Temporary</option>
                            </select>
                            <div class="invalid-feedback">Please select a migration type</div>
                            <div class="form-text">
                                <strong>Arrival:</strong> Resident moved into the barangay<br>
                                <strong>Departure:</strong> Resident moved out of the barangay<br>
                                <strong>Temporary:</strong> Resident moved within the barangay
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="origin_address" class="form-label">🏠 Origin Address <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="origin_address" name="origin_address" required>
                                <div class="invalid-feedback">Please enter the origin address</div>
                            </div>
                            <div class="col-md-6">
                                <label for="destination_address" class="form-label">🏢 Destination Address <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="destination_address" name="destination_address" required>
                                <div class="invalid-feedback">Please enter the destination address</div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="migration_date" class="form-label">📅 Migration Date <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="migration_date" name="migration_date" required>
                            <div class="invalid-feedback">Please select a migration date</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="reason" class="form-label">Reason for Migration</label>
                            <textarea class="form-control" id="reason" name="reason" rows="2"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="remarks" class="form-label">📝 Remarks</label>
                            <textarea class="form-control" id="remarks" name="remarks" rows="2"></textarea>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="update_resident_status" name="update_resident_status" value="1" checked>
                            <label class="form-check-label" for="update_resident_status">
                                Update resident status based on migration type
                            </label>
                            <div class="form-text text-muted">
                                If checked, resident status will be automatically updated (Active for Arrivals, Inactive for Departures).
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <!-- Make sure we have the action field -->
                        <input type="hidden" name="action" value="add">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Add Migration Record</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Migration Modal -->
    <div class="modal fade" id="editMigrationModal" tabindex="-1" aria-labelledby="editMigrationModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="editMigrationModalLabel">✏️ Edit Migration Record</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                    <div class="modal-body">
                    <form id="editMigrationForm">
                        <input type="hidden" id="edit_migration_id" name="migration_id">
                        <div class="mb-3">
                            <label for="edit_resident_id" class="form-label">👤 Resident <span class="text-danger">*</span></label>
                            <select class="form-select" id="edit_resident_id" name="resident_id" required>
                                <option value="">Select Resident</option>
                                <?php foreach ($residents as $resident): ?>
                                    <option value="<?php echo $resident['id']; ?>">
                                        <?php echo htmlspecialchars($resident['last_name'] . ', ' . $resident['first_name'] . ' ' . $resident['middle_name']); ?>
                                        <?php if ($resident['status'] !== 'Active'): ?>
                                            (<?php echo htmlspecialchars($resident['status']); ?>)
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                    </div>
                        <div class="mb-3">
                            <label for="edit_migration_type" class="form-label">🔄 Migration Type <span class="text-danger">*</span></label>
                            <select class="form-select" id="edit_migration_type" name="migration_type" required>
                                <option value="">Select Type</option>
                                <option value="In">In (Arrival)</option>
                                <option value="Out">Out (Departure)</option>
                                <option value="Temporary">Temporary</option>
                        </select>
                    </div>
                        <div class="mb-3">
                            <label for="edit_migration_date" class="form-label">📅 Migration Date <span class="text-danger">*</span></label>
                            <input type="date" class="form-control" id="edit_migration_date" name="migration_date" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_origin_destination" class="form-label">🌍 Origin/Destination <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="edit_origin_destination" name="origin_destination" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_remarks" class="form-label">📝 Remarks</label>
                            <textarea class="form-control" id="edit_remarks" name="remarks" rows="3"></textarea>
                        </div>
                    </form>
                    </div>
                    <div class="modal-footer">
                        <input type="hidden" name="action" value="edit">
                        <input type="hidden" name="record_id" id="edit_record_id">
                        <input type="hidden" name="resident_id" id="edit_resident_id">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update Migration Record</button>
                    </div>
            </div>
        </div>
                    </div>
                    
    <!-- View Migration Modal -->
    <div class="modal fade" id="viewMigrationModal" tabindex="-1" aria-labelledby="viewMigrationModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="viewMigrationModalLabel">
                        Migration Record Details
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4" id="viewMigrationDetails">
                    <!-- Content will be loaded dynamically -->
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        Close
                    </button>
                    <button type="button" class="btn btn-primary" id="printRecordBtn">
                        <i class="fas fa-print me-1"></i> Print
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteModalLabel">⚠️ Confirm Delete</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                    <p>Are you sure you want to delete this migration record?</p>
                    <p id="deleteConfirmText" class="fw-bold"></p>
                    <p>This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                    <form action="migration_actions.php" method="POST">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="record_id" id="delete_record_id">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

    <!-- Add simple scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
        // Function to show toast notifications
        function showToast(message, type = 'success') {
            // Create toast container if it doesn't exist
            let toastContainer = document.querySelector('.toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
                toastContainer.style.zIndex = '9999';
                document.body.appendChild(toastContainer);
            }
            
            // Create toast element
            const toastId = 'toast-' + Date.now();
            const toastElement = document.createElement('div');
            toastElement.id = toastId;
            toastElement.className = `toast align-items-center text-white bg-${type} border-0`;
            toastElement.setAttribute('role', 'alert');
            toastElement.setAttribute('aria-live', 'assertive');
            toastElement.setAttribute('aria-atomic', 'true');
            
            // Create toast content
            toastElement.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        ${type === 'success' ? '✅' : type === 'danger' ? '❌' : type === 'warning' ? '⚠️' : '📝'} 
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            `;
            
            // Add toast to container
            toastContainer.appendChild(toastElement);
            
            // Initialize and show toast
            const toast = new bootstrap.Toast(toastElement, {
                autohide: true,
                delay: 5000
            });
            toast.show();
            
            // Remove toast after it's hidden
            toastElement.addEventListener('hidden.bs.toast', function() {
                toastElement.remove();
                // Remove container if empty
                if (toastContainer.children.length === 0) {
                    toastContainer.remove();
                }
            });
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            // Show messages as toasts
            <?php if (!empty($error_message)): ?>
            showToast("<?php echo addslashes($error_message); ?>", "danger");
            <?php endif; ?>
            
            <?php if (!empty($success_message)): ?>
            showToast("<?php echo addslashes($success_message); ?>", "success");
            <?php endif; ?>
            
            // Set default migration date to today
            if (document.getElementById('migration_date')) {
                document.getElementById('migration_date').valueAsDate = new Date();
            }
            
            // Bootstrap form validation
            const forms = document.querySelectorAll('.needs-validation');
            
            Array.from(forms).forEach(form => {
                form.addEventListener('submit', event => {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                        console.log('Form validation failed');
                    } else {
                        console.log('Form validation passed, submitting...');
                    }
                    
                    form.classList.add('was-validated');
                });
            });
            
            // View migration details via AJAX
            $('.view-migration-btn').on('click', function() {
                const migrationId = $(this).data('id');
                console.log("Viewing migration ID:", migrationId); // Debug
                
                $("#viewMigrationDetails").html(`
                    <div class="text-center my-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading migration details...</p>
                    </div>
                `);
                
                $('#viewMigrationModal').modal('show');
                
                // Load migration details via AJAX
                $.ajax({
                    url: 'migration_actions.php',  // Direct path (same directory)
                    type: 'POST',
                    data: {
                        action: 'view_migration',
                        migration_id: migrationId
                    },
                    success: function(response) {
                        console.log("AJAX response received:", response); // Debug
                        try {
                            const data = typeof response === 'object' ? response : JSON.parse(response);
                            if (data.status === 'success') {
                                $("#viewMigrationDetails").html(data.html);
                            } else {
                                $("#viewMigrationDetails").html(`
                                    <div class="alert alert-danger">
                                        <i class="fas fa-exclamation-circle me-2"></i> ${data.message || 'Error loading data'}
                                    </div>
                                `);
                            }
                        } catch (e) {
                            console.error("Error parsing AJAX response:", e);
                            $("#viewMigrationDetails").html(`
                                <div class="alert alert-danger">
                                    <i class="fas fa-exclamation-circle me-2"></i> Error parsing response
                                </div>
                            `);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("AJAX error:", error);
                        $("#viewMigrationDetails").html(`
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i> Failed to load migration details: ${error}
                            </div>
                        `);
                    }
                });
            });
            
            // Edit migration via AJAX
            $('.edit-migration-btn').on('click', function() {
                const migrationId = $(this).data('id');
                console.log("Editing migration ID:", migrationId); // Debug
                
                // Show loading spinner in button
                const $button = $(this);
                const originalContent = $button.html();
                $button.html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>');
                $button.prop('disabled', true);
                
                // Load migration details via AJAX
                $.ajax({
                    url: 'migration_actions.php',  // Direct path (same directory)
                    type: 'POST',
                    data: {
                        action: 'view_migration',
                        migration_id: migrationId
                    },
                    success: function(response) {
                        console.log("AJAX response received for edit:", response); // Debug
                        // Restore button state
                        $button.html(originalContent);
                        $button.prop('disabled', false);
                        
                        try {
                            const data = typeof response === 'object' ? response : JSON.parse(response);
                            if (data.status === 'success') {
                                // Populate edit form
                                const migration = data.data;
                                
                                // Handle different field naming (record_id vs migration_id)
                                const recordId = migration.migration_id || migration.record_id || '';
                                document.getElementById('edit_record_id').value = recordId;
                                document.getElementById('edit_resident_id').value = migration.resident_id || '';
                                document.getElementById('edit_resident_name').value = migration.resident_name || '';
                                
                                // Handle potentially different migration type values
                                const migrationType = migration.migration_type || '';
                                const editMigrationType = document.getElementById('edit_migration_type');
                                
                                // Map non-standard types to standard ones if needed
                                let standardType = migrationType;
                                if (migrationType === 'In') standardType = 'Arrival';
                                if (migrationType === 'Out') standardType = 'Departure';
                                if (migrationType === 'Temporary') standardType = 'Transfer';
                                
                                // Set the value or select first option if not found
                                if (Array.from(editMigrationType.options).some(opt => opt.value === standardType)) {
                                    editMigrationType.value = standardType;
                                } else if (Array.from(editMigrationType.options).some(opt => opt.value === migrationType)) {
                                    editMigrationType.value = migrationType;
                                } else {
                                    console.warn("Migration type not found in options:", migrationType);
                                }
                                
                                document.getElementById('edit_origin_destination').value = migration.origin_address || '';
                                document.getElementById('edit_remarks').value = migration.remarks || '';
                                
                                // Show the edit modal
                                $('#editMigrationModal').modal('show');
                            } else {
                                showToast(data.message || 'Error loading data for editing', 'danger');
                            }
                        } catch (e) {
                            console.error("Error parsing AJAX response for edit:", e);
                            showToast('Error parsing server response', 'danger');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("AJAX error for edit:", error);
                        // Restore button state
                        $button.html(originalContent);
                        $button.prop('disabled', false);
                        showToast('Failed to load migration details: ' + error, 'danger');
                    }
                });
            });
            
            // Print record button
            $('#printRecordBtn').on('click', function() {
                window.print();
            });
            
            // Change address hints based on migration type
            if (document.getElementById('migration_type')) {
                document.getElementById('migration_type').addEventListener('change', function() {
                    const originLabel = document.querySelector('label[for="origin_address"]');
                    const destinationLabel = document.querySelector('label[for="destination_address"]');
                    
                    switch (this.value) {
                        case 'Arrival':
                            originLabel.textContent = 'Previous Address (from) *';
                            destinationLabel.textContent = 'Barangay Address (to) *';
                            break;
                        case 'Departure':
                            originLabel.textContent = 'Barangay Address (from) *';
                            destinationLabel.textContent = 'New Address (to) *';
                            break;
                        case 'Transfer':
                            originLabel.textContent = 'Previous Barangay Address (from) *';
                            destinationLabel.textContent = 'New Barangay Address (to) *';
                            break;
                        default:
                            originLabel.textContent = 'Origin Address *';
                            destinationLabel.textContent = 'Destination Address *';
                    }
                });
            }
        });
        
        // Handle delete confirmation
        function confirmDelete(recordId, residentName, migrationType) {
            document.getElementById('delete_record_id').value = recordId;
            document.getElementById('deleteConfirmText').textContent = `${migrationType || 'Migration'} record for ${residentName || 'resident'}`;
            
            const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            deleteModal.show();
        }

        // Make sure modals clean up properly when hidden
        document.addEventListener('DOMContentLoaded', function() {
            const modalElements = document.querySelectorAll('.modal');
            modalElements.forEach(modalElement => {
                modalElement.addEventListener('hidden.bs.modal', function() {
                    // Clean up any potential issues when modal is hidden
                    const backdrop = document.querySelector('.modal-backdrop');
                    if (backdrop) {
                        backdrop.remove();
                    }
                    document.body.classList.remove('modal-open');
                    document.body.style.overflow = '';
                    document.body.style.paddingRight = '';
                });
    });
});
</script>
</body>
</html> 