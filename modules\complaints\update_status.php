<?php
session_start();
include '../../includes/config/database.php';
include '../../includes/functions/permission_functions.php';
include '../../includes/functions/utility.php';
include '../../includes/functions/functions.php';
include '../../includes/functions/log_function.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check permission
if (!hasPermission('update_complaint_status')) {
    header("Location: ../../index.php");
    exit;
}

// Check if complaint ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: complaints.php");
    exit;
}

$complaint_id = (int)$_GET['id'];

// Get complaint details
try {
    $query = "
        SELECT c.*, 
            r1.first_name as complainant_first_name, r1.last_name as complainant_last_name,
            r2.first_name as respondent_first_name, r2.last_name as respondent_last_name
        FROM complaints c
        LEFT JOIN residents r1 ON c.complainant_id = r1.resident_id
        LEFT JOIN residents r2 ON c.respondent_id = r2.resident_id
        WHERE c.complaint_id = :complaint_id
    ";

    $stmt = $conn->prepare($query);
    $stmt->bindValue(':complaint_id', $complaint_id, PDO::PARAM_INT);
    $stmt->execute();

    if ($stmt->rowCount() == 0) {
        header("Location: complaints.php");
        exit;
    }

    $complaint = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error fetching complaint details: " . $e->getMessage());
    header("Location: complaints.php");
    exit;
}

// Get officials for dropdown
try {
    $officials_query = "SELECT o.official_id, r.first_name, r.last_name, o.position FROM officials o 
                    JOIN residents r ON o.resident_id = r.resident_id 
                    WHERE o.status = 'Active' ORDER BY o.position";
    $officials_stmt = $conn->prepare($officials_query);
    $officials_stmt->execute();
    $officials = $officials_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error fetching officials: " . $e->getMessage());
    $officials = [];
}

// Process form submission
$success_message = '';
$error_message = '';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get form data
    $status = sanitize($_POST['status']);
    $assigned_official_id = !empty($_POST['assigned_official_id']) ? (int)$_POST['assigned_official_id'] : NULL;
    $notes = sanitize($_POST['notes']);
    
    try {
        // Start transaction
        $conn->beginTransaction();
        
        // Update complaint status
        $update_query = "UPDATE complaints SET 
                        status = :status,
                        assigned_official_id = :assigned_official_id
                        WHERE complaint_id = :complaint_id";
        
        $update_stmt = $conn->prepare($update_query);
        $update_stmt->bindValue(':status', $status);
        $update_stmt->bindValue(':assigned_official_id', $assigned_official_id, $assigned_official_id ? PDO::PARAM_INT : PDO::PARAM_NULL);
        $update_stmt->bindValue(':complaint_id', $complaint_id, PDO::PARAM_INT);
        $update_stmt->execute();
        
        // Insert into complaint_updates table
        $insert_update_query = "INSERT INTO complaint_updates (
                                complaint_id, status, notes, updated_by, update_date
                            ) VALUES (
                                :complaint_id, :status, :notes, :updated_by, NOW()
                            )";
        
        $insert_stmt = $conn->prepare($insert_update_query);
        $insert_stmt->bindValue(':complaint_id', $complaint_id, PDO::PARAM_INT);
        $insert_stmt->bindValue(':status', $status);
        $insert_stmt->bindValue(':notes', $notes);
        $insert_stmt->bindValue(':updated_by', $_SESSION['user_id'], PDO::PARAM_INT);
        $insert_stmt->execute();
        
        // Log activity
        log_activity_safe($conn, $_SESSION['user_id'], 'Update Complaint Status', "Updated complaint #$complaint_id status to $status", 'complaints', $complaint_id);
        
        // Commit transaction
        $conn->commit();
        
        $success_message = "Complaint status updated successfully!";
        
        // Redirect to view complaint page
        header("Location: view_complaint.php?id=$complaint_id&success=updated");
        exit;
    } catch (PDOException $e) {
        // Rollback transaction on error
        if ($conn->inTransaction()) {
            $conn->rollBack();
        }
        error_log("Error updating complaint status: " . $e->getMessage());
        $error_message = "Error updating status: " . $e->getMessage();
    }
}

// Page title
$page_title = "Update Complaint Status - Barangay Management System";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        /* Custom card header styles */
        .card-header-custom {
            background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
            color: white;
            padding: 16px;
            border-radius: 8px 8px 0 0;
        }
        
        /* Status badge styling */
        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }
        
        /* Button styling */
        .btn-action {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.2s;
        }
        
        .btn-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        
        /* Page header styling */
        .page-header {
            background-color: #f8f9fa;
            padding: 16px;
            border-radius: 8px;
            margin-bottom: 24px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .page-header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        /* Form control with icons */
        .form-icon-label {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
        }
        
        /* Information card styling */
        .info-item {
            margin-bottom: 8px;
            display: flex;
            align-items: baseline;
        }
        
        .info-label {
            font-weight: 600;
            min-width: 120px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .info-value {
            flex: 1;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
                <div class="page-header">
                    <h1>📝 Update Complaint Status</h1>
                    <a href="view_complaint.php?id=<?php echo $complaint_id; ?>" class="btn btn-action btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Complaint
                    </a>
                </div>
                
                <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-2"></i> <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i> <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <div class="card mb-4 shadow-sm">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0">📋 Complaint Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-item">
                                    <span class="info-label">🔢 Complaint ID:</span>
                                    <span class="info-value"><?php echo $complaint['complaint_id']; ?></span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">🔖 Type:</span>
                                    <span class="info-value"><?php echo $complaint['complaint_type']; ?></span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">📊 Current Status:</span>
                                    <span class="info-value">
                                        <span class="badge <?php 
                                            if ($complaint['status'] == 'Pending') echo 'bg-warning';
                                            elseif ($complaint['status'] == 'Under Investigation') echo 'bg-info';
                                            elseif ($complaint['status'] == 'Resolved') echo 'bg-success';
                                            elseif ($complaint['status'] == 'Dismissed') echo 'bg-danger';
                                            else echo 'bg-secondary';
                                        ?>"><?php echo $complaint['status']; ?></span>
                                    </span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-item">
                                    <span class="info-label">👤 Complainant:</span>
                                    <span class="info-value">
                                        <?php 
                                        if (!empty($complaint['complainant_id'])) {
                                            echo $complaint['complainant_last_name'] . ', ' . $complaint['complainant_first_name'];
                                        } else {
                                            echo $complaint['complainant_name'] ?? 'Not specified';
                                        }
                                        ?>
                                    </span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">👥 Respondent:</span>
                                    <span class="info-value">
                                        <?php 
                                        if (!empty($complaint['respondent_id'])) {
                                            echo $complaint['respondent_last_name'] . ', ' . $complaint['respondent_first_name'];
                                        } else {
                                            echo $complaint['respondent_name'] ?? 'Not specified';
                                        }
                                        ?>
                                    </span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">📅 Date Filed:</span>
                                    <span class="info-value"><?php echo date('F d, Y', strtotime($complaint['date_filed'])); ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card shadow-sm">
                    <div class="card-header-custom">
                        <h5 class="card-title mb-0">🔄 Update Status</h5>
                    </div>
                    <div class="card-body">
                        <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . "?id=" . $complaint_id); ?>" method="POST" class="needs-validation" novalidate>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="status" class="form-icon-label"><span>📊</span> Status <span class="text-danger">*</span></label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="">Select Status</option>
                                        <option value="Pending" <?php if ($complaint['status'] == 'Pending') echo 'selected'; ?>>⏳ Pending</option>
                                        <option value="Under Investigation" <?php if ($complaint['status'] == 'Under Investigation') echo 'selected'; ?>>🔍 Under Investigation</option>
                                        <option value="Resolved" <?php if ($complaint['status'] == 'Resolved') echo 'selected'; ?>>✅ Resolved</option>
                                        <option value="Dismissed" <?php if ($complaint['status'] == 'Dismissed') echo 'selected'; ?>>❌ Dismissed</option>
                                    </select>
                                    <div class="invalid-feedback">Please select a status</div>
                                </div>
                                <div class="col-md-6">
                                    <label for="assigned_official_id" class="form-icon-label"><span>👮</span> Assigned Official</label>
                                    <select class="form-select" id="assigned_official_id" name="assigned_official_id">
                                        <option value="">Unassigned</option>
                                        <?php foreach ($officials as $official): ?>
                                            <?php $selected = ($official['official_id'] == $complaint['assigned_official_id']) ? 'selected' : ''; ?>
                                            <option value="<?php echo $official['official_id']; ?>" <?php echo $selected; ?>>
                                                <?php echo $official['position'] . ': ' . $official['first_name'] . ' ' . $official['last_name']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="notes" class="form-icon-label"><span>📝</span> Notes <span class="text-danger">*</span></label>
                                <textarea class="form-control" id="notes" name="notes" rows="5" required placeholder="Provide details about this status update..."></textarea>
                                <div class="invalid-feedback">Please provide notes about this status update</div>
                                <div class="form-text">
                                    Provide details about this status update, including actions taken, 
                                    decisions made, or next steps.
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-4">
                                <a href="view_complaint.php?id=<?php echo $complaint_id; ?>" class="btn btn-action btn-secondary me-md-2">
                                    ❌ Cancel
                                </a>
                                <button type="submit" class="btn btn-action btn-primary">
                                    💾 Update Status
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="../../assets/js/main.js"></script>
    
    <script>
        // Form validation
        (function() {
            'use strict';
            
            // Fetch all forms we want to apply validation to
            var forms = document.querySelectorAll('.needs-validation');
            
            // Loop over them and prevent submission
            Array.prototype.slice.call(forms).forEach(function(form) {
                form.addEventListener('submit', function(event) {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    
                    form.classList.add('was-validated');
                }, false);
            });
        })();
    </script>
</body>
</html> 