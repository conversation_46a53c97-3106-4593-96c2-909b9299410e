<?php
session_start();
include '../../includes/config/database.php';
include '../../includes/functions/permission_functions.php';
include '../../includes/functions/utility.php';
include '../../includes/functions/functions.php';
include '../../includes/functions/log_function.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check permission
if (!hasPermission('schedule_hearing')) {
    header("Location: ../../index.php");
    exit;
}

// Initialize variables
$success_message = '';
$error_message = '';
$complaints = [];

// Check if complaint ID is provided (optional)
$complaint_id = isset($_GET['complaint_id']) ? (int)$_GET['complaint_id'] : 0;

// Get pending complaints for dropdown
try {
    $complaints_query = "SELECT c.complaint_id, c.complaint_type, 
                        CONCAT(r1.last_name, ' vs. ', COALESCE(r2.last_name, c.respondent_name)) as parties,
                        c.complainant_id, c.respondent_id, c.complainant_name, c.respondent_name,
                        r1.first_name as complainant_fname, r1.last_name as complainant_lname,
                        r2.first_name as respondent_fname, r2.last_name as respondent_lname
                        FROM complaints c
                        LEFT JOIN residents r1 ON c.complainant_id = r1.resident_id
                        LEFT JOIN residents r2 ON c.respondent_id = r2.resident_id
                        WHERE c.status IN ('Pending', 'Under Investigation')
                        ORDER BY c.date_filed DESC";
    $complaints_stmt = $conn->prepare($complaints_query);
    $complaints_stmt->execute();
    $complaints = $complaints_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error fetching complaints: " . $e->getMessage());
}

// Get complaint details if ID is provided
$complaint_data = null;
if ($complaint_id > 0) {
    try {
        $complaint_query = "SELECT c.*, 
                          r1.first_name as complainant_first_name, r1.last_name as complainant_last_name,
                          r2.first_name as respondent_first_name, r2.last_name as respondent_last_name
                          FROM complaints c
                          LEFT JOIN residents r1 ON c.complainant_id = r1.resident_id
                          LEFT JOIN residents r2 ON c.respondent_id = r2.resident_id
                          WHERE c.complaint_id = :complaint_id";
        $complaint_stmt = $conn->prepare($complaint_query);
        $complaint_stmt->bindValue(':complaint_id', $complaint_id, PDO::PARAM_INT);
        $complaint_stmt->execute();
        
        if ($complaint_stmt->rowCount() > 0) {
            $complaint_data = $complaint_stmt->fetch(PDO::FETCH_ASSOC);
        }
    } catch (PDOException $e) {
        error_log("Error fetching complaint details: " . $e->getMessage());
    }
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $complaint_id = isset($_POST['complaint_id']) ? (int)$_POST['complaint_id'] : 0;
    $hearing_date = sanitize($_POST['hearing_date']);
    $hearing_time = sanitize($_POST['hearing_time']);
    $hearing_location = sanitize($_POST['hearing_location']);
    $purpose = sanitize($_POST['purpose']);
    $notes = sanitize($_POST['notes']);
    
    // Validate inputs
    if (empty($complaint_id) || empty($hearing_date) || empty($hearing_time) || empty($hearing_location) || empty($purpose)) {
        $error_message = "All fields marked with * are required";
    } else {
        try {
            // Combine date and time
            $hearing_datetime = $hearing_date . ' ' . $hearing_time . ':00';
            
            // Begin transaction
            $conn->beginTransaction();
            
            // Check if there's already a scheduled hearing for this complaint that is not cancelled or completed
            $check_query = "SELECT COUNT(*) FROM hearings 
                          WHERE complaint_id = :complaint_id 
                          AND status = 'Scheduled'";
            $check_stmt = $conn->prepare($check_query);
            $check_stmt->bindValue(':complaint_id', $complaint_id, PDO::PARAM_INT);
            $check_stmt->execute();
            
            if ($check_stmt->fetchColumn() > 0) {
                throw new Exception("There is already a scheduled hearing for this complaint");
            }
            
            // Insert hearing record
            $insert_query = "INSERT INTO hearings (
                           complaint_id, hearing_date, hearing_location, purpose, notes, 
                           status, scheduled_by, created_at
                       ) VALUES (
                           :complaint_id, :hearing_date, :hearing_location, :purpose, :notes, 
                           'Scheduled', :scheduled_by, NOW()
                       )";
            
            $insert_stmt = $conn->prepare($insert_query);
            $insert_stmt->bindValue(':complaint_id', $complaint_id, PDO::PARAM_INT);
            $insert_stmt->bindValue(':hearing_date', $hearing_datetime);
            $insert_stmt->bindValue(':hearing_location', $hearing_location);
            $insert_stmt->bindValue(':purpose', $purpose);
            $insert_stmt->bindValue(':notes', $notes);
            $insert_stmt->bindValue(':scheduled_by', $_SESSION['user_id'], PDO::PARAM_INT);
            $insert_stmt->execute();
            
            $hearing_id = $conn->lastInsertId();
            
            // Update complaint status to "Under Investigation" if it's "Pending"
            $update_query = "UPDATE complaints SET 
                          status = CASE WHEN status = 'Pending' THEN 'Under Investigation' ELSE status END
                          WHERE complaint_id = :complaint_id";
            $update_stmt = $conn->prepare($update_query);
            $update_stmt->bindValue(':complaint_id', $complaint_id, PDO::PARAM_INT);
            $update_stmt->execute();
            
            // Add to complaint updates
            $update_notes = "Hearing scheduled on " . date('F d, Y h:i A', strtotime($hearing_datetime));
            $insert_update = "INSERT INTO complaint_updates (
                            complaint_id, status, notes, updated_by, update_date
                        ) VALUES (
                            :complaint_id, 'Under Investigation', :notes, :updated_by, NOW()
                        )";
            $update_stmt = $conn->prepare($insert_update);
            $update_stmt->bindValue(':complaint_id', $complaint_id, PDO::PARAM_INT);
            $update_stmt->bindValue(':notes', $update_notes);
            $update_stmt->bindValue(':updated_by', $_SESSION['user_id'], PDO::PARAM_INT);
            $update_stmt->execute();
            
            // Log activity
            log_activity_safe($conn, $_SESSION['user_id'], 'Schedule Hearing', "Scheduled a hearing for complaint #$complaint_id", 'complaints', $complaint_id);
            
            // Commit transaction
            $conn->commit();
            
            $success_message = "Hearing scheduled successfully for " . date('F d, Y h:i A', strtotime($hearing_datetime));
            
            // Redirect after successful scheduling
            header("Location: view_hearing.php?id=$hearing_id&success=scheduled");
            exit;
        } catch (Exception $e) {
            // Rollback transaction on error
            if ($conn->inTransaction()) {
                $conn->rollBack();
            }
            
            error_log("Error scheduling hearing: " . $e->getMessage());
            $error_message = $e->getMessage();
        }
    }
}

// Page title
$page_title = "Schedule Hearing - Barangay Management System";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        /* Card styling with clean modern design */
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border: none;
            margin-bottom: 24px;
        }
        
        /* Main content container */
        .content-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        /* Page header */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }
        
        .page-header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
        }
        
        /* Section Cards */
        .section-card {
            border-radius: 8px;
            padding: 0;
            margin-bottom: 24px;
            overflow: hidden;
        }
        
        .section-header {
            padding: 12px 16px;
            font-weight: 600;
            color: white;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .section-header .icon {
            font-size: 1.2rem;
        }
        
        .section-body {
            padding: 20px;
            background-color: white;
        }
        
        /* Primary (blue) section */
        .section-primary .section-header {
            background-color: #0d6efd;
        }
        
        /* Warning (yellow) section */
        .section-warning .section-header {
            background-color: #ffc107;
            color: #000;
        }
        
        /* Success (green) section */
        .section-success .section-header {
            background-color: #198754;
        }
        
        /* Form elements */
        .form-label {
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
        }
        
        .form-control, .form-select {
            padding: 10px 12px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            font-size: 1rem;
        }
        
        .form-select {
            background-position: right 10px center;
        }
        
        .form-section {
            background-color: #f8f9fc;
            border-radius: 6px;
            padding: 16px;
            margin-bottom: 16px;
        }
        
        /* Button styling */
        .btn {
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: 500;
        }
        
        .btn-back {
            background-color: #f0f0f0;
            color: #333;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px 16px;
            border-radius: 6px;
        }
        
        .btn-back:hover {
            background-color: #e0e0e0;
        }
        
        /* Alert styling */
        .alert {
            border-radius: 6px;
            padding: 12px 16px;
            margin-bottom: 24px;
        }
        
        /* Badge styling */
        .badge {
            padding: 5px 10px;
            font-weight: 500;
            border-radius: 30px;
        }
        
        /* Grid spacing */
        .mb-4 {
            margin-bottom: 24px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        /* Form row spacing */
        .row {
            margin-bottom: 0;
        }
        
        .row > * {
            margin-bottom: 16px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
                <div class="content-container">
                    <div class="page-header">
                        <h1>📅 Schedule Hearing</h1>
                        <a href="hearings.php" class="btn-back">
                            ⬅️ Back to Hearings
                        </a>
                    </div>
                    
                    <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success" role="alert">
                        ✅ <?php echo $success_message; ?>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger" role="alert">
                        ❌ <?php echo $error_message; ?>
                    </div>
                    <?php endif; ?>
                    
                    <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="POST" class="needs-validation" novalidate>
                        <!-- Hearing Details Section (Blue) -->
                        <div class="section-card section-primary shadow">
                            <div class="section-header">
                                <span class="icon">🗓️</span>
                                <span>Hearing Details</span>
                            </div>
                            <div class="section-body">
                                <div class="form-group">
                                    <label for="complaint_id" class="form-label">📋 Select Case/Complaint <span class="text-danger">*</span></label>
                                    <select class="form-select" id="complaint_id" name="complaint_id" required <?php echo $complaint_id ? 'disabled' : ''; ?>>
                                        <option value="">Select a complaint</option>
                                        <?php foreach ($complaints as $complaint): ?>
                                        <option value="<?php echo $complaint['complaint_id']; ?>" <?php echo ($complaint_id == $complaint['complaint_id']) ? 'selected' : ''; ?>>
                                            #<?php echo $complaint['complaint_id']; ?> - <?php echo $complaint['complaint_type']; ?> (<?php echo $complaint['parties']; ?>)
                                        </option>
                                        <?php endforeach; ?>
                                    </select>
                                    <?php if ($complaint_id): ?>
                                    <input type="hidden" name="complaint_id" value="<?php echo $complaint_id; ?>">
                                    <?php endif; ?>
                                    <div class="invalid-feedback">Please select a complaint</div>
                                </div>
                                
                                <?php if ($complaint_data): ?>
                                <div class="card mb-4">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">📄 Complaint Information</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p class="mb-2"><strong>🔍 Complaint Type:</strong> <span class="badge bg-secondary"><?php echo $complaint_data['complaint_type']; ?></span></p>
                                                <p class="mb-0"><strong>📊 Status:</strong> 
                                                    <?php 
                                                    $status_class = '';
                                                    $status_emoji = '';
                                                    switch ($complaint_data['status']) {
                                                        case 'Pending':
                                                            $status_class = 'bg-warning';
                                                            $status_emoji = '⏳ ';
                                                            break;
                                                        case 'Under Investigation':
                                                            $status_class = 'bg-info';
                                                            $status_emoji = '🔍 ';
                                                            break;
                                                        case 'Resolved':
                                                            $status_class = 'bg-success';
                                                            $status_emoji = '✅ ';
                                                            break;
                                                        case 'Dismissed':
                                                            $status_class = 'bg-danger';
                                                            $status_emoji = '❌ ';
                                                            break;
                                                        default:
                                                            $status_class = 'bg-secondary';
                                                            $status_emoji = '🔄 ';
                                                    }
                                                    echo '<span class="badge ' . $status_class . '">' . $status_emoji . $complaint_data['status'] . '</span>';
                                                    ?>
                                                </p>
                                            </div>
                                            <div class="col-md-6">
                                                <p class="mb-2"><strong>👤 Complainant:</strong> 
                                                    <span class="text-primary">
                                                    <?php 
                                                    if (!empty($complaint_data['complainant_id'])) {
                                                        echo $complaint_data['complainant_last_name'] . ', ' . $complaint_data['complainant_first_name'];
                                                    } elseif (!empty($complaint_data['complainant_name'])) {
                                                        echo $complaint_data['complainant_name'];
                                                    } else {
                                                        echo 'N/A';
                                                    }
                                                    ?>
                                                    </span>
                                                </p>
                                                <p class="mb-0"><strong>👥 Respondent:</strong> 
                                                    <span class="text-danger">
                                                    <?php 
                                                    if (!empty($complaint_data['respondent_id'])) {
                                                        echo $complaint_data['respondent_last_name'] . ', ' . $complaint_data['respondent_first_name'];
                                                    } elseif (!empty($complaint_data['respondent_name'])) {
                                                        echo $complaint_data['respondent_name'];
                                                    } else {
                                                        echo 'N/A';
                                                    }
                                                    ?>
                                                    </span>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <!-- Hearing Schedule Section (Yellow) -->
                        <div class="section-card section-warning shadow">
                            <div class="section-header">
                                <span class="icon">⏰</span>
                                <span>Hearing Schedule</span>
                            </div>
                            <div class="section-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="hearing_date" class="form-label">📆 Hearing Date <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control" id="hearing_date" name="hearing_date" required min="<?php echo date('Y-m-d'); ?>">
                                            <div class="invalid-feedback">Please select a future date</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="hearing_time" class="form-label">⏰ Hearing Time <span class="text-danger">*</span></label>
                                            <input type="time" class="form-control" id="hearing_time" name="hearing_time" required>
                                            <div class="invalid-feedback">Please select a time</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="hearing_location" class="form-label">📍 Hearing Location <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="hearing_location" name="hearing_location" required 
                                           placeholder="e.g., Barangay Hall Conference Room">
                                    <div class="invalid-feedback">Please provide a location</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Purpose & Notes Section (Green) -->
                        <div class="section-card section-success shadow">
                            <div class="section-header">
                                <span class="icon">📝</span>
                                <span>Purpose & Notes</span>
                            </div>
                            <div class="section-body">
                                <div class="form-group">
                                    <label for="purpose" class="form-label">🎯 Purpose <span class="text-danger">*</span></label>
                                    <select class="form-select" id="purpose" name="purpose" required>
                                        <option value="">Select purpose</option>
                                        <option value="Initial Hearing">Initial Hearing</option>
                                        <option value="Mediation">Mediation</option>
                                        <option value="Conciliation">Conciliation</option>
                                        <option value="Arbitration">Arbitration</option>
                                        <option value="Final Hearing">Final Hearing</option>
                                        <option value="Other">Other</option>
                                    </select>
                                    <div class="invalid-feedback">Please select a purpose</div>
                                </div>
                                
                                <div class="form-group mb-0">
                                    <label for="notes" class="form-label">📝 Notes</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="4" 
                                              placeholder="Additional notes or instructions for the hearing"></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end mt-4">
                            <a href="hearings.php" class="btn btn-secondary me-2">
                                ❌ Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                ✅ Schedule Hearing
                            </button>
                        </div>
                    </form>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    
    <script>
        // Initialize form validation
        (function () {
            'use strict'
            
            // Fetch all forms we want to apply custom validation to
            var forms = document.querySelectorAll('.needs-validation')
            
            // Loop over them and prevent submission
            Array.prototype.slice.call(forms)
                .forEach(function (form) {
                    form.addEventListener('submit', function (event) {
                        if (!form.checkValidity()) {
                            event.preventDefault()
                            event.stopPropagation()
                        }
                        
                        form.classList.add('was-validated')
                    }, false)
                })
        })()
        
        // Initialize date picker to only allow future dates
        document.addEventListener('DOMContentLoaded', function() {
            // Set minimum date for hearing date input
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('hearing_date').setAttribute('min', today);
            
            // Set default time to 9:00 AM
            if (document.getElementById('hearing_time').value === '') {
                document.getElementById('hearing_time').value = '09:00';
            }
            
            // Set default location if empty
            if (document.getElementById('hearing_location').value === '') {
                document.getElementById('hearing_location').value = 'Barangay Hall Conference Room';
            }
        });
    </script>
</body>
</html> 