<?php
session_start();
include '../../includes/config/database.php';
include '../../includes/functions/permission_functions.php';
include '../../includes/functions/utility.php';

// Bypass permission check for testing
// if (!isset($_SESSION['user_id']) || !hasPermission('admin_complaints')) {
//     die("Access denied");
// }

try {
    // Begin transaction
    $conn->beginTransaction();
    
    // Check if resolutions table exists
    $check_table = $conn->prepare("SHOW TABLES LIKE 'resolutions'");
    $check_table->execute();
    
    if ($check_table->rowCount() == 0) {
        // Create resolutions table if it doesn't exist
        $create_table_sql = "CREATE TABLE resolutions (
            resolution_id INT AUTO_INCREMENT PRIMARY KEY,
            complaint_id INT NOT NULL,
            resolution_type VARCHAR(50) NOT NULL,
            resolution_content TEXT NOT NULL,
            resolution_date DATE NOT NULL,
            resolved_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            agreement_terms TEXT,
            resolution_files TEXT,
            FOREIGN KEY (complaint_id) REFERENCES complaints(complaint_id) ON DELETE CASCADE,
            FOREIGN KEY (resolved_by) REFERENCES users(user_id) ON DELETE SET NULL
        )";
        $conn->exec($create_table_sql);
        echo "Created resolutions table<br>";
    } else {
        echo "Resolutions table already exists<br>";
    }
    
    // Get all resolved complaints that don't have an entry in resolutions table
    $query = "SELECT c.* 
              FROM complaints c 
              LEFT JOIN resolutions r ON c.complaint_id = r.complaint_id
              WHERE c.status = 'Resolved' 
              AND r.resolution_id IS NULL";
    
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $resolved_complaints = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "Found " . count($resolved_complaints) . " resolved complaints to migrate<br>";
    
    // Insert each resolved complaint into resolutions table
    foreach ($resolved_complaints as $complaint) {
        echo "Processing complaint #" . $complaint['complaint_id'] . "<br>";
        
        // Make sure we have values for required fields
        $resolution_content = !empty($complaint['resolution']) ? $complaint['resolution'] : 'No resolution content provided';
        $resolution_date = !empty($complaint['settlement_date']) ? $complaint['settlement_date'] : $complaint['resolved_date'];
        if (empty($resolution_date)) {
            $resolution_date = date('Y-m-d');
        }
        
        $resolution_insert = "INSERT INTO resolutions (
            complaint_id,
            resolution_type,
            resolution_details,
            resolution_date,
            resolved_by,
            resolution_files
        ) VALUES (
            :complaint_id,
            :resolution_type,
            :resolution_details,
            :resolution_date,
            :resolved_by,
            :resolution_files
        )";
        
        $res_stmt = $conn->prepare($resolution_insert);
        $res_stmt->bindValue(':complaint_id', $complaint['complaint_id'], PDO::PARAM_INT);
        $res_stmt->bindValue(':resolution_type', $complaint['status']);
        $res_stmt->bindValue(':resolution_details', $resolution_content);
        $res_stmt->bindValue(':resolution_date', $resolution_date);
        $res_stmt->bindValue(':resolved_by', $complaint['resolved_by'], PDO::PARAM_INT);
        $res_stmt->bindValue(':resolution_files', $complaint['resolution_files'] ?? null);
        
        if ($res_stmt->execute()) {
            echo "Migrated resolution for complaint #" . $complaint['complaint_id'] . "<br>";
        } else {
            $error = $res_stmt->errorInfo();
            throw new Exception("Failed to migrate complaint #" . $complaint['complaint_id'] . ": " . $error[2]);
        }
    }
    
    // Commit transaction
    $conn->commit();
    echo "<br>Migration completed successfully!";
    
} catch (Exception $e) {
    // Rollback transaction on error
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    die("Error during migration: " . $e->getMessage());
}
?> 