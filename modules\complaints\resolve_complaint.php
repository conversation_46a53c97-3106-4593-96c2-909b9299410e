<?php
session_start();
include '../../includes/config/database.php';
include '../../includes/functions/permission_functions.php';
include '../../includes/functions/utility.php';
include '../../includes/functions/log_function.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check permission
if (!hasPermission('update_complaint')) {
    header("Location: complaints.php?error=permission");
    exit;
}

// Check if form is submitted
if ($_SERVER["REQUEST_METHOD"] != "POST") {
    header("Location: complaints.php");
    exit;
}

// Get form data
$complaint_id = isset($_POST['complaint_id']) ? (int)$_POST['complaint_id'] : 0;
$resolution = sanitize($_POST['resolution'] ?? '');
$settlement_date = sanitize($_POST['settlement_date'] ?? '');
$new_status = sanitize($_POST['new_status'] ?? '');

// Validate data
if (empty($complaint_id) || empty($resolution) || empty($settlement_date) || empty($new_status)) {
    header("Location: view_complaint.php?id=$complaint_id&error=incomplete");
    exit;
}

try {
    // Begin transaction
    $conn->beginTransaction();
    
    // Debug logging
    error_log("Starting complaint resolution process for complaint ID: $complaint_id");
    
    // Check if resolutions table exists, create if it doesn't
    $check_resolutions_table = $conn->prepare("SHOW TABLES LIKE 'resolutions'");
    $check_resolutions_table->execute();
    if ($check_resolutions_table->rowCount() == 0) {
        error_log("Resolutions table does not exist, creating it now");
        // Create resolutions table
        $create_table_sql = "CREATE TABLE resolutions (
            resolution_id INT AUTO_INCREMENT PRIMARY KEY,
            complaint_id INT NOT NULL,
            resolution_type VARCHAR(50) NOT NULL,
            resolution_details TEXT NOT NULL,
            resolution_date DATE NOT NULL,
            resolved_by INT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            agreement_terms TEXT,
            resolution_files TEXT,
            FOREIGN KEY (complaint_id) REFERENCES complaints(complaint_id) ON DELETE CASCADE,
            FOREIGN KEY (resolved_by) REFERENCES users(user_id) ON DELETE SET NULL
        )";
        $conn->exec($create_table_sql);
        error_log("Resolutions table created successfully");
    } else {
        error_log("Resolutions table already exists");
    }
    
    // Check if complaint exists and user has permission to update it
    $check_query = "SELECT c.*, o.resident_id 
                   FROM complaints c 
                   LEFT JOIN officials o ON c.assigned_official_id = o.official_id 
                   WHERE c.complaint_id = :complaint_id";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bindValue(':complaint_id', $complaint_id, PDO::PARAM_INT);
    $check_stmt->execute();
    $complaint = $check_stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$complaint) {
        throw new Exception("Complaint not found.");
    }
    
    // Check if user is assigned to this complaint or has admin permission
    $user_official_id = isset($_SESSION['official_id']) ? $_SESSION['official_id'] : null;
    $is_assigned = !empty($user_official_id) && ($complaint['assigned_official_id'] == $user_official_id);
    if (!$is_assigned && !hasPermission('admin_complaints')) {
        throw new Exception("You don't have permission to resolve this complaint.");
    }
    
    // Process resolution files
    $resolution_files = [];
    if (isset($_FILES['resolution_files']) && !empty($_FILES['resolution_files']['name'][0])) {
        // Create directory if it doesn't exist
        $upload_dir = "../../uploads/complaints/" . $complaint_id . "/resolution/";
        if (!file_exists($upload_dir)) {
            mkdir($upload_dir, 0777, true);
        }
        
        // Upload each file
        for ($i = 0; $i < count($_FILES['resolution_files']['name']); $i++) {
            $file_name = $_FILES['resolution_files']['name'][$i];
            $file_tmp = $_FILES['resolution_files']['tmp_name'][$i];
            $file_size = $_FILES['resolution_files']['size'][$i];
            $file_error = $_FILES['resolution_files']['error'][$i];
            
            // Check for errors
            if ($file_error === 0) {
                // Generate unique filename
                $file_ext = pathinfo($file_name, PATHINFO_EXTENSION);
                $safe_filename = 'resolution_' . uniqid() . '.' . $file_ext;
                $target_file = $upload_dir . $safe_filename;
                
                // Move uploaded file
                if (move_uploaded_file($file_tmp, $target_file)) {
                    $resolution_files[] = 'uploads/complaints/' . $complaint_id . '/resolution/' . $safe_filename;
                }
            }
        }
    }
    
    // Update complaint resolution
    $update_query = "UPDATE complaints SET 
                    resolution = :resolution,
                    settlement_date = :settlement_date,
                    status = :new_status,
                    resolved_by = :user_id,
                    resolved_date = NOW()";
    
    // Add resolution files if any
    if (!empty($resolution_files)) {
        $update_query .= ", resolution_files = :resolution_files";
    }
    
    $update_query .= " WHERE complaint_id = :complaint_id";
    
    $update_stmt = $conn->prepare($update_query);
    $update_stmt->bindValue(':resolution', $resolution);
    $update_stmt->bindValue(':settlement_date', $settlement_date);
    $update_stmt->bindValue(':new_status', $new_status);
    $update_stmt->bindValue(':user_id', $_SESSION['user_id'], PDO::PARAM_INT);
    $update_stmt->bindValue(':complaint_id', $complaint_id, PDO::PARAM_INT);
    
    if (!empty($resolution_files)) {
        $update_stmt->bindValue(':resolution_files', json_encode($resolution_files));
    }
    
    $update_stmt->execute();
    
    // Add entry to complaint_updates table if it exists
    try {
        $check_table = $conn->prepare("SHOW TABLES LIKE 'complaint_updates'");
        $check_table->execute();
        
        if ($check_table->rowCount() > 0) {
            $update_note = "Complaint resolved: $new_status";
            $update_query = "INSERT INTO complaint_updates (complaint_id, status, notes, updated_by, update_date)
                          VALUES (:complaint_id, :status, :notes, :updated_by, NOW())";
            $update_log_stmt = $conn->prepare($update_query);
            $update_log_stmt->bindValue(':complaint_id', $complaint_id, PDO::PARAM_INT);
            $update_log_stmt->bindValue(':status', $new_status);
            $update_log_stmt->bindValue(':notes', $update_note);
            $update_log_stmt->bindValue(':updated_by', $_SESSION['user_id'], PDO::PARAM_INT);
            $update_log_stmt->execute();
        }
    } catch (PDOException $e) {
        // Just log the error but continue with the process
        error_log("Error adding to complaint_updates: " . $e->getMessage());
    }
    
    // Add entry to resolutions table
    try {
        $check_table = $conn->prepare("SHOW TABLES LIKE 'resolutions'");
        $check_table->execute();
        
        if ($check_table->rowCount() > 0) {
            error_log("Attempting to insert resolution for complaint ID: $complaint_id");
            $resolution_type = $new_status;
            $resolution_insert = "INSERT INTO resolutions (
                complaint_id, 
                resolution_type, 
                resolution_details,
                resolution_date, 
                resolved_by, 
                resolution_files
            ) VALUES (
                :complaint_id, 
                :resolution_type, 
                :resolution_details,
                :resolution_date, 
                :resolved_by, 
                :resolution_files
            )";
            
            $res_stmt = $conn->prepare($resolution_insert);
            $res_stmt->bindValue(':complaint_id', $complaint_id, PDO::PARAM_INT);
            $res_stmt->bindValue(':resolution_type', $resolution_type);
            $res_stmt->bindValue(':resolution_details', $resolution);
            $res_stmt->bindValue(':resolution_date', $settlement_date);
            $res_stmt->bindValue(':resolved_by', $_SESSION['user_id'], PDO::PARAM_INT);
            $res_stmt->bindValue(':resolution_files', !empty($resolution_files) ? json_encode($resolution_files) : null);
            
            if (!$res_stmt->execute()) {
                $error = $res_stmt->errorInfo();
                throw new Exception("Failed to insert resolution: " . $error[2]);
            }
            error_log("Successfully inserted resolution for complaint ID: $complaint_id");
        } else {
            throw new Exception("Resolutions table does not exist");
        }
    } catch (PDOException $e) {
        // Log the error and rethrow it to be handled by the main try-catch block
        error_log("Error adding to resolutions table: " . $e->getMessage());
        throw new Exception("Failed to save resolution: " . $e->getMessage());
    }
    
    // Log activity
    log_activity_safe($conn, $_SESSION['user_id'], 'Resolve Complaint', "Resolved complaint #$complaint_id: $new_status", 'complaints', $complaint_id);
    
    // Commit transaction
    $conn->commit();
    
    // Redirect to complaint view with success message
    header("Location: view_complaint.php?id=$complaint_id&success=resolved");
    exit;

} catch (Exception $e) {
    // Rollback transaction on error
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    
    // Log error and redirect
    error_log("Error resolving complaint: " . $e->getMessage());
    header("Location: view_complaint.php?id=$complaint_id&error=" . urlencode($e->getMessage()));
    exit;
}
?> 