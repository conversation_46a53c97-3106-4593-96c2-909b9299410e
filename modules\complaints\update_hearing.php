<?php
session_start();
include '../../includes/config/database.php';
include '../../includes/functions/permission_functions.php';
include '../../includes/functions/utility.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check permission
if (!hasPermission('update_hearing')) {
    header("Location: ../../index.php");
    exit;
}

// Initialize variables
$success_message = '';
$error_message = '';
$hearing = null;

// Get hearing ID from URL
$hearing_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($hearing_id <= 0) {
    header("Location: hearings.php");
    exit;
}

// Process actions from URL
if (isset($_GET['action'])) {
    $action = $_GET['action'];
    
    try {
        switch ($action) {
            case 'complete':
                $status = 'Completed';
                $message = 'Hearing has been marked as completed';
                break;
            case 'postpone':
                $status = 'Postponed';
                $message = 'Hearing has been postponed';
                break;
            case 'cancel':
                $status = 'Cancelled';
                $message = 'Hearing has been cancelled';
                break;
            default:
                header("Location: update_hearing.php?id=$hearing_id");
                exit;
        }
        
        $update_query = "UPDATE hearings SET status = :status WHERE hearing_id = :hearing_id";
        $update_stmt = $conn->prepare($update_query);
        $update_stmt->bindParam(':status', $status);
        $update_stmt->bindParam(':hearing_id', $hearing_id);
        $update_stmt->execute();
        
        // Redirect back to view page
        header("Location: view_hearing.php?id=$hearing_id&success=updated&message=" . urlencode($message));
        exit;
    } catch (PDOException $e) {
        $error_message = "Error updating hearing status: " . $e->getMessage();
    }
}

// Fetch hearing details
try {
    $query = "SELECT h.*, c.complaint_type, c.status as complaint_status
             FROM hearings h 
             LEFT JOIN complaints c ON h.complaint_id = c.complaint_id
             WHERE h.hearing_id = :hearing_id";
    
    $stmt = $conn->prepare($query);
    $stmt->bindParam(':hearing_id', $hearing_id);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        $hearing = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Check if hearing can be updated (only scheduled hearings)
        if ($hearing['status'] != 'Scheduled') {
            header("Location: view_hearing.php?id=$hearing_id&error=cannot_update");
            exit;
        }
    } else {
        header("Location: hearings.php");
        exit;
    }
} catch (PDOException $e) {
    $error_message = "Error fetching hearing details: " . $e->getMessage();
}

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $hearing_date = $_POST['hearing_date'] ?? '';
    $hearing_time = $_POST['hearing_time'] ?? '';
    $hearing_location = $_POST['hearing_location'] ?? '';
    $purpose = $_POST['purpose'] ?? '';
    $notes = $_POST['notes'] ?? '';
    $status = $_POST['status'] ?? 'Scheduled';
    
    // Validate inputs
    if (empty($hearing_date) || empty($hearing_time) || empty($hearing_location) || empty($purpose)) {
        $error_message = "All required fields must be filled out";
    } else {
        try {
            // Combine date and time
            $hearing_datetime = $hearing_date . ' ' . $hearing_time . ':00';
            
            // Update hearing record
            $update_query = "UPDATE hearings SET 
                           hearing_date = :hearing_date,
                           hearing_location = :hearing_location,
                           purpose = :purpose,
                           notes = :notes,
                           status = :status
                           WHERE hearing_id = :hearing_id";
            
            $update_stmt = $conn->prepare($update_query);
            $update_stmt->bindParam(':hearing_date', $hearing_datetime);
            $update_stmt->bindParam(':hearing_location', $hearing_location);
            $update_stmt->bindParam(':purpose', $purpose);
            $update_stmt->bindParam(':notes', $notes);
            $update_stmt->bindParam(':status', $status);
            $update_stmt->bindParam(':hearing_id', $hearing_id);
            $update_stmt->execute();
            
            // Redirect after successful update
            header("Location: view_hearing.php?id=$hearing_id&success=updated");
            exit;
        } catch (PDOException $e) {
            $error_message = "Error updating hearing: " . $e->getMessage();
        }
    }
}

// Page title
$page_title = "Update Hearing - Barangay Management System";

// Extract date and time from hearing date
$hearing_date = date('Y-m-d', strtotime($hearing['hearing_date']));
$hearing_time = date('H:i', strtotime($hearing['hearing_date']));
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        /* Card styling with clean modern design */
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border: none;
            margin-bottom: 24px;
        }
        
        /* Main content container */
        .content-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        /* Page header */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }
        
        .page-header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
        }
        
        /* Section Cards */
        .section-card {
            border-radius: 8px;
            padding: 0;
            margin-bottom: 24px;
            overflow: hidden;
        }
        
        .section-header {
            padding: 12px 16px;
            font-weight: 600;
            color: white;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .section-header .icon {
            font-size: 1.2rem;
        }
        
        .section-body {
            padding: 20px;
            background-color: white;
        }
        
        /* Primary (blue) section */
        .section-primary .section-header {
            background-color: #0d6efd;
        }
        
        /* Warning (yellow) section */
        .section-warning .section-header {
            background-color: #ffc107;
            color: #000;
        }
        
        /* Success (green) section */
        .section-success .section-header {
            background-color: #198754;
        }
        
        /* Info (cyan) section */
        .section-info .section-header {
            background-color: #0dcaf0;
            color: #000;
        }
        
        /* Danger (red) section */
        .section-danger .section-header {
            background-color: #dc3545;
        }
        
        /* Form elements */
        .form-label {
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
        }
        
        .form-control, .form-select {
            padding: 10px 12px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            font-size: 1rem;
        }
        
        .form-select {
            background-position: right 10px center;
        }
        
        .form-section {
            background-color: #f8f9fc;
            border-radius: 6px;
            padding: 16px;
            margin-bottom: 16px;
        }
        
        /* Button styling */
        .btn {
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: 500;
        }
        
        .btn-back {
            background-color: #f0f0f0;
            color: #333;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px 16px;
            border-radius: 6px;
        }
        
        .btn-back:hover {
            background-color: #e0e0e0;
        }
        
        /* Alert styling */
        .alert {
            border-radius: 6px;
            padding: 12px 16px;
            margin-bottom: 24px;
        }
        
        /* Badge styling */
        .badge {
            padding: 5px 10px;
            font-weight: 500;
            border-radius: 30px;
        }
        
        /* Grid spacing */
        .mb-4 {
            margin-bottom: 24px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        /* Form row spacing */
        .row {
            margin-bottom: 0;
        }
        
        .row > * {
            margin-bottom: 16px;
        }
        
        /* Quick action buttons */
        .quick-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .quick-actions .btn {
            flex: 1;
            text-align: center;
            min-width: 150px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
        }
        
        @media (max-width: 768px) {
            .quick-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
                <div class="content-container">
                    <div class="page-header">
                        <h1>📝 Update Hearing</h1>
                        <a href="view_hearing.php?id=<?php echo $hearing_id; ?>" class="btn-back">
                            ⬅️ Back to Hearing
                        </a>
                    </div>
                    
                    <?php if ($success_message): ?>
                    <div class="alert alert-success" role="alert">
                        ✅ <?php echo $success_message; ?>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($error_message): ?>
                    <div class="alert alert-danger" role="alert">
                        ❌ <?php echo $error_message; ?>
                    </div>
                    <?php endif; ?>
                    
                    <form action="" method="POST" class="needs-validation" novalidate>
                        <!-- Hearing Details Section (Blue) -->
                        <div class="section-card section-primary shadow">
                            <div class="section-header">
                                <span class="icon">🗓️</span>
                                <span>Hearing Details</span>
                            </div>
                            <div class="section-body">
                                <div class="form-group">
                                    <label class="form-label">📋 Complaint</label>
                                    <input type="text" class="form-control" value="<?php echo htmlspecialchars($hearing['complaint_type'] ?? 'Complaint'); ?> (ID: <?php echo $hearing['complaint_id']; ?>)" readonly>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Hearing Schedule Section (Yellow) -->
                        <div class="section-card section-warning shadow">
                            <div class="section-header">
                                <span class="icon">⏰</span>
                                <span>Hearing Schedule</span>
                            </div>
                            <div class="section-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="hearing_date" class="form-label">📆 Hearing Date <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control" id="hearing_date" name="hearing_date" value="<?php echo $hearing_date; ?>" required>
                                            <div class="invalid-feedback">Please select a date.</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="hearing_time" class="form-label">⏰ Hearing Time <span class="text-danger">*</span></label>
                                            <input type="time" class="form-control" id="hearing_time" name="hearing_time" value="<?php echo $hearing_time; ?>" required>
                                            <div class="invalid-feedback">Please select a time.</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="hearing_location" class="form-label">📍 Hearing Location <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="hearing_location" name="hearing_location" 
                                        value="<?php echo htmlspecialchars($hearing['hearing_location']); ?>" required>
                                    <div class="invalid-feedback">Please provide a location.</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Purpose & Notes Section (Green) -->
                        <div class="section-card section-success shadow">
                            <div class="section-header">
                                <span class="icon">📝</span>
                                <span>Purpose & Notes</span>
                            </div>
                            <div class="section-body">
                                <div class="form-group">
                                    <label for="purpose" class="form-label">🎯 Purpose <span class="text-danger">*</span></label>
                                    <select class="form-select" id="purpose" name="purpose" required>
                                        <option value="">Select purpose</option>
                                        <option value="Initial Hearing" <?php echo ($hearing['purpose'] == 'Initial Hearing') ? 'selected' : ''; ?>>Initial Hearing</option>
                                        <option value="Mediation" <?php echo ($hearing['purpose'] == 'Mediation') ? 'selected' : ''; ?>>Mediation</option>
                                        <option value="Conciliation" <?php echo ($hearing['purpose'] == 'Conciliation') ? 'selected' : ''; ?>>Conciliation</option>
                                        <option value="Arbitration" <?php echo ($hearing['purpose'] == 'Arbitration') ? 'selected' : ''; ?>>Arbitration</option>
                                        <option value="Final Hearing" <?php echo ($hearing['purpose'] == 'Final Hearing') ? 'selected' : ''; ?>>Final Hearing</option>
                                        <option value="Other" <?php echo ($hearing['purpose'] == 'Other') ? 'selected' : ''; ?>>Other</option>
                                    </select>
                                    <div class="invalid-feedback">Please select a purpose.</div>
                                </div>
                                
                                <div class="form-group mb-0">
                                    <label for="notes" class="form-label">📝 Notes</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="4"><?php echo htmlspecialchars($hearing['notes'] ?? ''); ?></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Status Section (Cyan) -->
                        <div class="section-card section-info shadow">
                            <div class="section-header">
                                <span class="icon">📊</span>
                                <span>Status</span>
                            </div>
                            <div class="section-body">
                                <div class="form-group mb-0">
                                    <label for="status" class="form-label">Current Status</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="Scheduled" <?php echo ($hearing['status'] == 'Scheduled') ? 'selected' : ''; ?>>✅ Scheduled</option>
                                        <option value="Completed" <?php echo ($hearing['status'] == 'Completed') ? 'selected' : ''; ?>>✓ Completed</option>
                                        <option value="Postponed" <?php echo ($hearing['status'] == 'Postponed') ? 'selected' : ''; ?>>⏳ Postponed</option>
                                        <option value="Cancelled" <?php echo ($hearing['status'] == 'Cancelled') ? 'selected' : ''; ?>>❌ Cancelled</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end mt-4">
                            <a href="view_hearing.php?id=<?php echo $hearing_id; ?>" class="btn btn-secondary me-2">❌ Cancel</a>
                            <button type="submit" class="btn btn-primary">💾 Update Hearing</button>
                        </div>
                    </form>
                    
                    <!-- Quick Actions Section (Red) -->
                    <div class="section-card section-danger shadow">
                        <div class="section-header">
                            <span class="icon">⚡</span>
                            <span>Quick Actions</span>
                        </div>
                        <div class="section-body">
                            <div class="quick-actions">
                                <a href="?id=<?php echo $hearing_id; ?>&action=complete" class="btn btn-success" 
                                   onclick="return confirm('Are you sure you want to mark this hearing as completed?')">
                                    ✅ Mark as Completed
                                </a>
                                <a href="?id=<?php echo $hearing_id; ?>&action=postpone" class="btn btn-warning"
                                   onclick="return confirm('Are you sure you want to postpone this hearing?')">
                                    ⏳ Postpone Hearing
                                </a>
                                <a href="?id=<?php echo $hearing_id; ?>&action=cancel" class="btn btn-danger"
                                   onclick="return confirm('Are you sure you want to cancel this hearing? This action cannot be undone.')">
                                    ❌ Cancel Hearing
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script>
        // Initialize form validation
        (function () {
            'use strict'
            
            // Fetch all forms we want to apply custom validation to
            var forms = document.querySelectorAll('.needs-validation')
            
            // Loop over them and prevent submission
            Array.prototype.slice.call(forms)
                .forEach(function (form) {
                    form.addEventListener('submit', function (event) {
                        if (!form.checkValidity()) {
                            event.preventDefault()
                            event.stopPropagation()
                        }
                        
                        form.classList.add('was-validated')
                    }, false)
                })
        })()
    </script>
</body>
</html> 