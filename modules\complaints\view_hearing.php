<?php
session_start();
include '../../includes/config/database.php';
include '../../includes/functions/permission_functions.php';
include '../../includes/functions/utility.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check permission
if (!hasPermission('view_hearings')) {
    header("Location: ../../index.php");
    exit;
}

// Initialize variables
$error_message = '';
$hearing = null;
$complaint = null;

// Get hearing ID from URL
$hearing_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($hearing_id <= 0) {
    $error_message = "Invalid hearing ID";
} else {
    try {
        // Fetch hearing details
        $query = "SELECT h.*, c.complaint_type, c.complaint_details, c.status as complaint_status,
                 c.complainant_name, c.respondent_name, c.complainant_id, c.respondent_id
                 FROM hearings h 
                 LEFT JOIN complaints c ON h.complaint_id = c.complaint_id
                 WHERE h.hearing_id = :hearing_id";
        
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':hearing_id', $hearing_id, PDO::PARAM_INT);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $hearing = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // For debugging - uncomment to check array contents
            // echo '<pre>'; print_r($hearing); echo '</pre>';
            
            // Get complainant and respondent details if IDs exist
            if (!empty($hearing['complainant_id'])) {
                $resident_query = "SELECT first_name, last_name, contact_number FROM residents WHERE resident_id = :resident_id";
                $resident_stmt = $conn->prepare($resident_query);
                $resident_stmt->bindParam(':resident_id', $hearing['complainant_id'], PDO::PARAM_INT);
                $resident_stmt->execute();
                
                if ($resident_stmt->rowCount() > 0) {
                    $complainant = $resident_stmt->fetch(PDO::FETCH_ASSOC);
                    $hearing['complainant_name'] = $complainant['first_name'] . ' ' . $complainant['last_name'];
                    $hearing['complainant_contact'] = $complainant['contact_number'];
                }
            }
            
            if (!empty($hearing['respondent_id'])) {
                $resident_query = "SELECT first_name, last_name, contact_number FROM residents WHERE resident_id = :resident_id";
                $resident_stmt = $conn->prepare($resident_query);
                $resident_stmt->bindParam(':resident_id', $hearing['respondent_id'], PDO::PARAM_INT);
                $resident_stmt->execute();
                
                if ($resident_stmt->rowCount() > 0) {
                    $respondent = $resident_stmt->fetch(PDO::FETCH_ASSOC);
                    $hearing['respondent_name'] = $respondent['first_name'] . ' ' . $respondent['last_name'];
                    $hearing['respondent_contact'] = $respondent['contact_number'];
                }
            }
        } else {
            $error_message = "Hearing not found";
        }
    } catch (PDOException $e) {
        $error_message = "Error fetching hearing details: " . $e->getMessage();
    }
}

// Handle mark as completed request
if (isset($_GET['action']) && $_GET['action'] == 'complete' && hasPermission('update_hearing')) {
    try {
        $update_query = "UPDATE hearings SET status = 'Completed' WHERE hearing_id = :hearing_id";
        $update_stmt = $conn->prepare($update_query);
        $update_stmt->bindParam(':hearing_id', $hearing_id, PDO::PARAM_INT);
        $update_stmt->execute();
        
        // Redirect to avoid resubmission
        header("Location: view_hearing.php?id=$hearing_id&success=completed");
        exit;
    } catch (PDOException $e) {
        $error_message = "Error updating hearing status: " . $e->getMessage();
    }
}

// Function to get badge class based on status
function getHearingStatusBadgeClass($status) {
    switch ($status) {
        case 'Scheduled':
            return 'bg-primary text-white';
        case 'Completed':
            return 'bg-success text-white';
        case 'Postponed':
            return 'bg-warning text-dark';
        case 'Cancelled':
            return 'bg-danger text-white';
        default:
            return 'bg-secondary text-white';
    }
}

// Function to get color for complaint status
function getStatusColor($status) {
    switch ($status) {
        case 'Pending':
            return 'warning';
        case 'In Progress':
            return 'primary';
        case 'Resolved':
            return 'success';
        case 'Dismissed':
            return 'danger';
        case 'Withdrawn':
            return 'secondary';
        default:
            return 'info';
    }
}

// Helper function to safely display fields
function displayField($array, $key, $default = 'Not specified') {
    if (isset($array[$key]) && !empty($array[$key])) {
        return htmlspecialchars($array[$key]);
    }
    return $default;
}

// Page title
$page_title = "View Hearing - Barangay Management System";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        body { 
            padding: 20px; 
            background-color: #f8f9fa;
        }
        .container-fluid { 
            max-width: 1600px;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 1.5rem;
            border-radius: 0.5rem;
            overflow: hidden;
        }
        .card-header {
            font-weight: 600;
        }
        
        /* Left border colors */
        .border-left-primary { border-left: 4px solid #4e73df !important; }
        .border-left-success { border-left: 4px solid #1cc88a !important; }
        .border-left-info { border-left: 4px solid #36b9cc !important; }
        .border-left-warning { border-left: 4px solid #f6c23e !important; }
        .border-left-danger { border-left: 4px solid #e74a3b !important; }
        .border-left-secondary { border-left: 4px solid #858796 !important; }
        .border-left-purple { border-left: 4px solid #6f42c1 !important; }
        .border-left-orange { border-left: 4px solid #fd7e14 !important; }
        .border-left-teal { border-left: 4px solid #20c997 !important; }
        
        /* Text colors */
        .text-primary { color: #4e73df !important; }
        .text-success { color: #1cc88a !important; }
        .text-info { color: #36b9cc !important; }
        .text-warning { color: #f6c23e !important; }
        .text-danger { color: #e74a3b !important; }
        .text-purple { color: #6f42c1 !important; }
        .text-orange { color: #fd7e14 !important; }
        .text-teal { color: #20c997 !important; }
        
        /* Background colors with opacity */
        .bg-primary-soft { background-color: rgba(78, 115, 223, 0.1) !important; }
        .bg-success-soft { background-color: rgba(28, 200, 138, 0.1) !important; }
        .bg-info-soft { background-color: rgba(54, 185, 204, 0.1) !important; }
        .bg-warning-soft { background-color: rgba(246, 194, 62, 0.1) !important; }
        .bg-danger-soft { background-color: rgba(231, 74, 59, 0.1) !important; }
        .bg-purple-soft { background-color: rgba(111, 66, 193, 0.1) !important; }
        .bg-orange-soft { background-color: rgba(253, 126, 20, 0.1) !important; }
        .bg-teal-soft { background-color: rgba(32, 201, 151, 0.1) !important; }
        
        /* Button styles */
        .btn {
            border-radius: 0.35rem;
            padding: 0.375rem 1rem;
            transition: all 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
        }
        
        /* Badge styles */
        .badge {
            font-weight: 600;
            padding: 0.4em 0.8em;
            border-radius: 0.35rem;
        }
        
        /* Icon boxes */
        .icon-box {
            display: flex;
            align-items: center;
            margin-bottom: 0.75rem;
        }
        .icon-box i {
            width: 28px;
            text-align: center;
        }
        
        @media print {
            .sidebar, .navbar, .no-print {
                display: none !important;
            }
            .card {
                break-inside: avoid;
                border: 1px solid #dee2e6;
                box-shadow: none;
            }
            .main-content {
                width: 100% !important;
                margin-left: 0 !important;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <!-- Page Title and Breadcrumb -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="fas fa-gavel me-2 text-info"></i> Hearing Details</h1>
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="../../index.php">Home</a></li>
                        <li class="breadcrumb-item"><a href="hearings.php">Hearings</a></li>
                        <li class="breadcrumb-item active">View Details</li>
                    </ol>
                </div>
                
                <!-- Alert Messages -->
                <?php if (isset($_GET['success']) && $_GET['success'] == 'completed'): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle me-1"></i> Hearing has been marked as completed successfully.
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle me-1"></i> <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <?php if ($hearing): ?>
                <!-- Main Content Area -->
                <div class="row">
                    <!-- Hearing Information -->
                    <div class="col-md-7">
                        <div class="card border-left-info shadow-sm mb-4">
                            <div class="card-header bg-info-soft py-3">
                                <h5 class="card-title mb-0 fw-bold">
                                    <i class="fas fa-gavel me-2 text-info"></i> Hearing Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <div class="icon-box">
                                            <i class="fas fa-hashtag text-info"></i>
                                            <div class="ms-2">
                                                <span class="fw-bold">Hearing ID:</span><br>
                                                <span class="ps-1">#<?php echo str_pad($hearing['hearing_id'], 4, '0', STR_PAD_LEFT); ?></span>
                                            </div>
                                        </div>
                                        
                                        <div class="icon-box">
                                            <i class="fas fa-calendar-alt text-teal"></i>
                                            <div class="ms-2">
                                                <span class="fw-bold">Date:</span><br>
                                                <span class="ps-1"><?php echo date('F d, Y', strtotime($hearing['hearing_date'])); ?></span>
                                            </div>
                                        </div>
                                        
                                        <div class="icon-box">
                                            <i class="fas fa-clock text-info"></i>
                                            <div class="ms-2">
                                                <span class="fw-bold">Time:</span><br>
                                                <span class="ps-1"><?php echo isset($hearing['hearing_time']) ? date('h:i A', strtotime($hearing['hearing_time'])) : 'Not specified'; ?></span>
                                            </div>
                                        </div>
                                        
                                        <div class="icon-box">
                                            <i class="fas fa-map-marker-alt text-danger"></i>
                                            <div class="ms-2">
                                                <span class="fw-bold">Location:</span><br>
                                                <span class="ps-1">
                                                <?php 
                                                // Directly check and display the location with a fallback
                                                if (isset($hearing['location']) && !empty($hearing['location'])) {
                                                    echo htmlspecialchars($hearing['location']);
                                                } else {
                                                    echo 'Barangay Hall Conference Room';
                                                }
                                                ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="col-md-6">
                                        <div class="icon-box">
                                            <i class="fas fa-tag text-warning"></i>
                                            <div class="ms-2">
                                                <span class="fw-bold">Status:</span><br>
                                                <span class="ps-1 badge <?php echo getHearingStatusBadgeClass($hearing['status']); ?> fs-6 mt-1">
                                                    <?php echo htmlspecialchars($hearing['status']); ?>
                                                </span>
                                            </div>
                                        </div>
                                        
                                        <div class="icon-box">
                                            <i class="fas fa-user-tie text-info"></i>
                                            <div class="ms-2">
                                                <span class="fw-bold">Mediator/Officer:</span><br>
                                                <span class="ps-1"><?php echo isset($hearing['mediator']) ? htmlspecialchars($hearing['mediator']) : 'Not assigned'; ?></span>
                                            </div>
                                        </div>
                                        
                                        <div class="icon-box">
                                            <i class="fas fa-folder-open text-primary"></i>
                                            <div class="ms-2">
                                                <span class="fw-bold">Case Type:</span><br>
                                                <span class="ps-1 badge bg-primary-soft text-primary">
                                                    <?php echo htmlspecialchars($hearing['complaint_type']); ?>
                                                </span>
                                            </div>
                                        </div>
                                        
                                        <div class="icon-box">
                                            <i class="fas fa-file-alt text-orange"></i>
                                            <div class="ms-2">
                                                <span class="fw-bold">Case Status:</span><br>
                                                <span class="ps-1 badge bg-<?php 
                                                    // Directly check and get status color with a fallback
                                                    $status = 'Under Investigation';
                                                    if (isset($hearing['complaint_status']) && !empty($hearing['complaint_status'])) {
                                                        $status = $hearing['complaint_status'];
                                                    }
                                                    echo getStatusColor($status);
                                                ?>">
                                                    <?php 
                                                    // Directly display status
                                                    echo $status;
                                                    ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <?php if (!empty($hearing['notes'])): ?>
                                <div class="card bg-light mb-3">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0 fw-bold"><i class="fas fa-sticky-note me-2 text-info"></i> Hearing Notes</h6>
                                    </div>
                                    <div class="card-body">
                                        <p><?php echo nl2br(htmlspecialchars($hearing['notes'])); ?></p>
                                    </div>
                                </div>
                                <?php endif; ?>
                                
                                <?php if (!empty($hearing['outcome']) && $hearing['status'] == 'Completed'): ?>
                                <div class="card bg-light mb-0">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0 fw-bold"><i class="fas fa-clipboard-check me-2 text-success"></i> Outcome</h6>
                                    </div>
                                    <div class="card-body">
                                        <p><?php echo nl2br(htmlspecialchars($hearing['outcome'])); ?></p>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        
                        <div class="card border-left-primary shadow-sm mb-4">
                            <div class="card-header bg-primary-soft py-3">
                                <h5 class="card-title mb-0 fw-bold">
                                    <i class="fas fa-file-alt me-2 text-primary"></i> Case Summary
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="icon-box">
                                    <i class="fas fa-hashtag text-primary"></i>
                                    <div class="ms-2">
                                        <span class="fw-bold">Case #:</span><br>
                                        <span class="ps-1">
                                                <a href="view_complaint.php?id=<?php echo $hearing['complaint_id']; ?>">
                                                #<?php echo str_pad($hearing['complaint_id'], 5, '0', STR_PAD_LEFT); ?>
                                            </a>
                                        </span>
                                    </div>
                                </div>
                                
                                <div class="card bg-light mb-0">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0 fw-bold"><i class="fas fa-align-left me-2 text-primary"></i> Case Details</h6>
                                    </div>
                                    <div class="card-body">
                                        <p><?php echo nl2br(htmlspecialchars($hearing['complaint_details'])); ?></p>
                                            </div>
                                        </div>
                                        </div>
                                            </div>
                                        </div>
                    
                    <!-- Parties and Actions -->
                    <div class="col-md-5">
                        <!-- Parties Information -->
                        <div class="card border-left-orange shadow-sm mb-4">
                            <div class="card-header bg-orange-soft py-3">
                                <h5 class="card-title mb-0 fw-bold">
                                    <i class="fas fa-users me-2 text-orange"></i> Parties Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <h6 class="border-bottom pb-2 mb-3"><i class="fas fa-user me-2 text-orange"></i> Complainant</h6>
                                <div class="icon-box">
                                    <i class="fas fa-user-circle text-orange"></i>
                                    <div class="ms-2">
                                        <span class="fw-bold">Name:</span><br>
                                        <span class="ps-1"><?php echo htmlspecialchars($hearing['complainant_name']); ?></span>
                                    </div>
                                </div>
                                
                                <?php if (!empty($hearing['complainant_contact'])): ?>
                                <div class="icon-box">
                                    <i class="fas fa-phone text-orange"></i>
                                    <div class="ms-2">
                                        <span class="fw-bold">Contact:</span><br>
                                        <span class="ps-1"><?php echo htmlspecialchars($hearing['complainant_contact']); ?></span>
                                        </div>
                                        </div>
                                <?php endif; ?>
                                
                                <h6 class="border-bottom pb-2 mb-3 mt-4"><i class="fas fa-user me-2 text-danger"></i> Respondent</h6>
                                <div class="icon-box">
                                    <i class="fas fa-user-circle text-danger"></i>
                                    <div class="ms-2">
                                        <span class="fw-bold">Name:</span><br>
                                        <span class="ps-1"><?php echo htmlspecialchars($hearing['respondent_name']); ?></span>
                                    </div>
                                </div>
                                
                                <?php if (!empty($hearing['respondent_contact'])): ?>
                                <div class="icon-box">
                                    <i class="fas fa-phone text-danger"></i>
                                    <div class="ms-2">
                                        <span class="fw-bold">Contact:</span><br>
                                        <span class="ps-1"><?php echo htmlspecialchars($hearing['respondent_contact']); ?></span>
                                    </div>
                                </div>
                                <?php endif; ?>
                        </div>
                    </div>
                    
                        <!-- Actions Card -->
                        <div class="card border-left-purple shadow-sm mb-4">
                            <div class="card-header bg-purple-soft py-3">
                                <h5 class="card-title mb-0 fw-bold">
                                    <i class="fas fa-cogs me-2 text-purple"></i> Actions
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <?php if (hasPermission('update_hearing') && $hearing['status'] == 'Scheduled'): ?>
                                    <a href="edit_hearing.php?id=<?php echo $hearing['hearing_id']; ?>" class="btn btn-primary">
                                        <i class="fas fa-edit me-2"></i> Edit Hearing
                                    </a>
                                    <?php endif; ?>
                                    
                                    <?php if (hasPermission('update_hearing') && $hearing['status'] == 'Scheduled'): ?>
                                    <a href="view_hearing.php?id=<?php echo $hearing['hearing_id']; ?>&action=complete" 
                                       class="btn btn-success"
                                       onclick="return confirm('Are you sure you want to mark this hearing as completed?');">
                                        <i class="fas fa-check-circle me-2"></i> Mark as Completed
                                    </a>
                                    <?php endif; ?>
                                    
                                    <?php if (hasPermission('update_hearing') && $hearing['status'] == 'Scheduled'): ?>
                                    <a href="postpone_hearing.php?id=<?php echo $hearing['hearing_id']; ?>" class="btn btn-warning">
                                        <i class="fas fa-calendar-alt me-2"></i> Postpone Hearing
                                    </a>
                                    <?php endif; ?>
                                    
                                    <?php if (hasPermission('update_hearing') && in_array($hearing['status'], ['Scheduled', 'Postponed'])): ?>
                                    <a href="cancel_hearing.php?id=<?php echo $hearing['hearing_id']; ?>" 
                                       class="btn btn-danger"
                                       onclick="return confirm('Are you sure you want to cancel this hearing?');">
                                        <i class="fas fa-times-circle me-2"></i> Cancel Hearing
                                    </a>
                                    <?php endif; ?>
                                    
                                    <?php if ($hearing['status'] == 'Completed' && hasPermission('add_resolution')): ?>
                                    <a href="add_resolution.php?complaint_id=<?php echo $hearing['complaint_id']; ?>&hearing_id=<?php echo $hearing['hearing_id']; ?>" 
                                       class="btn" style="background-color: #20c997; color: white;">
                                        <i class="fas fa-file-contract me-2"></i> Add Resolution
                                    </a>
                                    <?php endif; ?>
                                    
                                    <a href="print_hearing.php?id=<?php echo $hearing['hearing_id']; ?>" class="btn btn-outline-dark" target="_blank">
                                        <i class="fas fa-print me-2"></i> Print Details
                                    </a>
                                    
                                    <a href="hearings.php" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i> Back to List
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Hearing Schedule Card -->
                        <div class="card border-left-success shadow-sm mb-4">
                            <div class="card-header bg-success-soft py-3">
                                <h5 class="card-title mb-0 fw-bold">
                                    <i class="fas fa-calendar-check me-2 text-success"></i> Schedule Details
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="d-flex justify-content-center mb-3">
                                    <div class="text-center p-3 bg-light rounded-3 w-100">
                                        <h3 class="fw-bold mb-0 text-success"><?php echo date('M d, Y', strtotime($hearing['hearing_date'])); ?></h3>
                                        <p class="mb-0 fs-5 text-primary"><?php echo isset($hearing['hearing_time']) ? date('h:i A', strtotime($hearing['hearing_time'])) : 'Time not set'; ?></p>
                                        <div class="mt-2 fw-bold text-secondary">
                                            <i class="fas fa-map-marker-alt me-1"></i> 
                                            <?php 
                                            // Directly check and display the location with a fallback
                                            if (isset($hearing['location']) && !empty($hearing['location'])) {
                                                echo htmlspecialchars($hearing['location']);
                                            } else {
                                                echo 'Barangay Hall Conference Room';
                                            }
                                            ?>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="text-center">
                                    <?php 
                                    try {
                                        if (isset($hearing['hearing_date'])) {
                                            if (isset($hearing['hearing_time']) && !empty($hearing['hearing_time'])) {
                                                // Use the date and time directly without concatenating
                                                $hearingDateTime = new DateTime($hearing['hearing_date'] . ' ' . $hearing['hearing_time']);
                                            } else {
                                                // Only use the date part without time
                                                $hearingDateTime = new DateTime($hearing['hearing_date']);
                                            }
                                            
                                            $now = new DateTime();
                                            $interval = $now->diff($hearingDateTime);
                                            
                                            if ($hearingDateTime > $now && $hearing['status'] == 'Scheduled') {
                                                if ($interval->days == 0) {
                                                    $hours = $interval->h;
                                                    $minutes = $interval->i;
                                                    echo '<div class="badge bg-danger p-2 mb-2 fs-6 w-100">Today - ';
                                                    if ($hours > 0) echo $hours . ' hour' . ($hours > 1 ? 's' : '') . ' ';
                                                    echo $minutes . ' minute' . ($minutes > 1 ? 's' : '') . ' remaining</div>';
                                                } elseif ($interval->days <= 3) {
                                                    echo '<div class="badge bg-warning text-dark p-2 mb-2 fs-6 w-100">Coming up in ' . $interval->days . ' day' . ($interval->days > 1 ? 's' : '') . '</div>';
                                                } else {
                                                    echo '<div class="badge bg-success p-2 mb-2 fs-6 w-100">Scheduled in ' . $interval->days . ' days</div>';
                                                }
                                            } elseif ($hearing['status'] == 'Completed') {
                                                echo '<div class="badge bg-success p-2 mb-2 fs-6 w-100">Completed</div>';
                                            } elseif ($hearing['status'] == 'Cancelled') {
                                                echo '<div class="badge bg-danger p-2 mb-2 fs-6 w-100">Cancelled</div>';
                                            } elseif ($hearing['status'] == 'Postponed') {
                                                echo '<div class="badge bg-warning text-dark p-2 mb-2 fs-6 w-100">Postponed</div>';
                                            } elseif ($hearingDateTime < $now) {
                                                echo '<div class="badge bg-secondary p-2 mb-2 fs-6 w-100">Past due</div>';
                                            }
                                        } else {
                                            echo '<div class="badge bg-secondary p-2 mb-2 fs-6 w-100">Date not set</div>';
                                        }
                                    } catch (Exception $e) {
                                        echo '<div class="badge bg-danger p-2 mb-2 fs-6 w-100">Error parsing date</div>';
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <?php else: ?>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Hearing not found or you don't have permission to view it.
                    <div class="mt-3">
                        <a href="hearings.php" class="btn btn-primary">
                            <i class="fas fa-arrow-left me-1"></i> Back to Hearings
                        </a>
                    </div>
                </div>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 