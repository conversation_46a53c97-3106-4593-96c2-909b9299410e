<?php
session_start();
include '../../includes/config/database.php';
include '../../includes/functions/permission_functions.php';
include '../../includes/functions/utility.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check if database connection has an error
$db_error = isset($db_connection_error) && $db_connection_error;

// Check permission
if (!hasPermission('generate_document') && !$db_error) {
    header("Location: ../../index.php");
    exit;
}

// Initialize variables
$request_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$document = null;
$resident = null;
$errors = [];

// Page title
$page_title = "Generate Document - Barangay Management System";

// Get document details if not a DB error
if (!$db_error && $request_id > 0) {
    // Get document request details with associated resident and users
    $query = "SELECT d.*, 
             r.first_name, r.middle_name, r.last_name, r.contact_number, r.address,
             r.gender, r.birthdate, r.civil_status, r.voter_status,
             c.username as created_by_name
             FROM document_requests d
             JOIN residents r ON d.resident_id = r.resident_id
             LEFT JOIN users c ON d.created_by = c.user_id
             WHERE d.document_id = ?";
    
    $stmt = mysqli_prepare($conn, $query);
    
    if ($stmt) {
        mysqli_stmt_bind_param($stmt, "i", $request_id);
        mysqli_stmt_execute($stmt);
        
        $result = mysqli_stmt_get_result($stmt);
        
        if ($result && mysqli_num_rows($result) > 0) {
            $document = mysqli_fetch_assoc($result);
            
            // Calculate age
            $birthdate = new DateTime($document['birthdate']);
            $today = new DateTime();
            $age = $birthdate->diff($today)->y;
            $document['age'] = $age;
            
            // Format dates
            $document['created_at_formatted'] = date('F j, Y', strtotime($document['created_at']));
            $document['birth_date_formatted'] = date('F j, Y', strtotime($document['birthdate']));
            
            // Get municipality info
            $settings_query = "SELECT setting_key, setting_value FROM system_settings WHERE setting_key IN ('barangay_name', 'municipality', 'province', 'barangay_captain', 'barangay_secretary')";
            $settings_result = mysqli_query($conn, $settings_query);
            
            $settings = [];
            if ($settings_result) {
                while ($row = mysqli_fetch_assoc($settings_result)) {
                    $settings[$row['setting_key']] = $row['setting_value'];
                }
            }
            
            $document['barangay_name'] = $settings['barangay_name'] ?? 'Unknown Barangay';
            $document['municipality'] = $settings['municipality'] ?? 'Unknown Municipality';
            $document['province'] = $settings['province'] ?? 'Unknown Province';
            $document['barangay_captain'] = $settings['barangay_captain'] ?? 'Unknown Captain';
            $document['barangay_secretary'] = $settings['barangay_secretary'] ?? 'Unknown Secretary';
        } else {
            $errors[] = "Document request not found";
        }
        
        mysqli_stmt_close($stmt);
    } else {
        $errors[] = "Database error: " . mysqli_error($conn);
    }
} elseif ($request_id <= 0) {
    $errors[] = "Invalid document request ID";
}

// Define templates for different document types
function getDocumentTemplate($document) {
    $type = $document['document_type'];
    
    switch ($type) {
        case 'Barangay Clearance':
            return getBarangayClearanceTemplate($document);
        case 'Certificate of Residency':
            return getResidencyCertificateTemplate($document);
        case 'Certificate of Indigency':
            return getIndigencyCertificateTemplate($document);
        case 'Business Permit':
            return getBusinessPermitTemplate($document);
        case 'Barangay ID':
            return getBarangayIDTemplate($document);
        default:
            return getGenericTemplate($document);
    }
}

// Template functions for different document types
function getBarangayClearanceTemplate($document) {
    ob_start();
    ?>
    <h4 class="text-center mb-4">BARANGAY CLEARANCE</h4>
    
    <p class="mb-4">This is to certify that <strong><?php echo $document['first_name'] . ' ' . $document['middle_name'] . ' ' . $document['last_name']; ?></strong>, 
    <?php echo $document['age']; ?> years old, <?php echo $document['gender']; ?>, <?php echo $document['civil_status']; ?>,
    is a bonafide resident of <?php echo $document['address']; ?>, <?php echo $document['barangay_name']; ?>, <?php echo $document['municipality']; ?>, <?php echo $document['province']; ?>.</p>
    
    <p class="mb-4">This is to certify further that he/she is a person of good moral character and has no derogatory record on file in this barangay.</p>
    
    <p class="mb-4">This certification is being issued upon the request of the above-named person for <strong><?php echo $document['purpose']; ?></strong>.</p>
    
    <p class="mb-4">Given this <?php echo date('jS'); ?> day of <?php echo date('F Y'); ?> at <?php echo $document['barangay_name']; ?>, <?php echo $document['municipality']; ?>, <?php echo $document['province']; ?>.</p>
    <?php
    return ob_get_clean();
}

function getResidencyCertificateTemplate($document) {
    ob_start();
    ?>
    <h4 class="text-center mb-4">CERTIFICATE OF RESIDENCY</h4>
    
    <p class="mb-4">This is to certify that <strong><?php echo $document['first_name'] . ' ' . $document['middle_name'] . ' ' . $document['last_name']; ?></strong>, 
    <?php echo $document['age']; ?> years old, <?php echo $document['gender']; ?>, <?php echo $document['civil_status']; ?>,
    is a bonafide resident of <?php echo $document['address']; ?>, <?php echo $document['barangay_name']; ?>, <?php echo $document['municipality']; ?>, <?php echo $document['province']; ?>
    for more than <?php echo rand(1, 10); ?> years now.</p>
    
    <p class="mb-4">This certification is being issued upon the request of the above-named person for <strong><?php echo $document['purpose']; ?></strong>.</p>
    
    <p class="mb-4">Given this <?php echo date('jS'); ?> day of <?php echo date('F Y'); ?> at <?php echo $document['barangay_name']; ?>, <?php echo $document['municipality']; ?>, <?php echo $document['province']; ?>.</p>
    <?php
    return ob_get_clean();
}

function getIndigencyCertificateTemplate($document) {
    ob_start();
    ?>
    <h4 class="text-center mb-4">CERTIFICATE OF INDIGENCY</h4>
    
    <p class="mb-4">This is to certify that <strong><?php echo $document['first_name'] . ' ' . $document['middle_name'] . ' ' . $document['last_name']; ?></strong>, 
    <?php echo $document['age']; ?> years old, <?php echo $document['gender']; ?>, <?php echo $document['civil_status']; ?>,
    is a bonafide resident of <?php echo $document['address']; ?>, <?php echo $document['barangay_name']; ?>, <?php echo $document['municipality']; ?>, <?php echo $document['province']; ?>.</p>
    
    <p class="mb-4">This is to certify further that the above-named person belongs to the indigent family in our barangay whose family income is insufficient to support their daily basic needs.</p>
    
    <p class="mb-4">This certification is being issued upon the request of the above-named person for <strong><?php echo $document['purpose']; ?></strong>.</p>
    
    <p class="mb-4">Given this <?php echo date('jS'); ?> day of <?php echo date('F Y'); ?> at <?php echo $document['barangay_name']; ?>, <?php echo $document['municipality']; ?>, <?php echo $document['province']; ?>.</p>
    <?php
    return ob_get_clean();
}

function getBusinessPermitTemplate($document) {
    ob_start();
    ?>
    <h4 class="text-center mb-4">BARANGAY BUSINESS PERMIT</h4>
    
    <p class="mb-4">This is to certify that the business owned by <strong><?php echo $document['first_name'] . ' ' . $document['middle_name'] . ' ' . $document['last_name']; ?></strong>, 
    located at <?php echo $document['address']; ?>, <?php echo $document['barangay_name']; ?>, <?php echo $document['municipality']; ?>, <?php echo $document['province']; ?>
    is hereby granted permission to operate in accordance with the existing barangay ordinances.</p>
    
    <p class="mb-4"><strong>Business Name:</strong> <?php echo htmlspecialchars(substr($document['purpose'], 0, 30)); ?></p>
    <p class="mb-4"><strong>Nature of Business:</strong> <?php echo htmlspecialchars(substr($document['purpose'], 30)); ?></p>
    <p class="mb-4"><strong>Permit Number:</strong> <?php echo date('Y') . '-' . $document['document_id']; ?></p>
    <p class="mb-4"><strong>Validity:</strong> From <?php echo date('F j, Y'); ?> to December 31, <?php echo date('Y'); ?></p>
    
    <p class="mb-4">Given this <?php echo date('jS'); ?> day of <?php echo date('F Y'); ?> at <?php echo $document['barangay_name']; ?>, <?php echo $document['municipality']; ?>, <?php echo $document['province']; ?>.</p>
    <?php
    return ob_get_clean();
}

function getBarangayIDTemplate($document) {
    ob_start();
    ?>
    <h4 class="text-center mb-4">BARANGAY IDENTIFICATION CARD</h4>
    
    <div class="card mb-4" style="max-width: 500px; margin: 0 auto; border: 1px solid #000;">
        <div class="card-header bg-primary text-white text-center">
            <h5><?php echo $document['barangay_name']; ?></h5>
            <h6><?php echo $document['municipality'] . ', ' . $document['province']; ?></h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-4">
                    <div class="bg-light" style="width: 100px; height: 100px; display: flex; align-items: center; justify-content: center; border: 1px solid #ccc;">
                        <i class="fas fa-user fa-4x text-muted"></i>
                    </div>
                </div>
                <div class="col-8">
                    <p class="mb-1"><strong>Name:</strong> <?php echo $document['last_name'] . ', ' . $document['first_name'] . ' ' . $document['middle_name']; ?></p>
                    <p class="mb-1"><strong>Address:</strong> <?php echo $document['address']; ?></p>
                    <p class="mb-1"><strong>Birthdate:</strong> <?php echo $document['birth_date_formatted']; ?></p>
                    <p class="mb-1"><strong>Contact:</strong> <?php echo $document['contact_number']; ?></p>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12 text-center">
                    <p><strong>ID Number:</strong> BID-<?php echo date('Y') . sprintf('%04d', $document['document_id']); ?></p>
                    <p><strong>Issued On:</strong> <?php echo date('F j, Y'); ?></p>
                </div>
            </div>
        </div>
        <div class="card-footer text-center">
            <p class="mb-1">Signature of Barangay Captain</p>
            <p class="mb-0"><strong><?php echo $document['barangay_captain']; ?></strong></p>
        </div>
    </div>
    
    <p class="text-center mb-4"><strong>This ID is valid for 1 year from date of issuance.</strong></p>
    <?php
    return ob_get_clean();
}

function getGenericTemplate($document) {
    ob_start();
    ?>
    <h4 class="text-center mb-4"><?php echo strtoupper($document['document_type']); ?></h4>
    
    <p class="mb-4">This is to certify that <strong><?php echo $document['first_name'] . ' ' . $document['middle_name'] . ' ' . $document['last_name']; ?></strong>, 
    <?php echo $document['age']; ?> years old, <?php echo $document['gender']; ?>, <?php echo $document['civil_status']; ?>,
    is a bonafide resident of <?php echo $document['address']; ?>, <?php echo $document['barangay_name']; ?>, <?php echo $document['municipality']; ?>, <?php echo $document['province']; ?>.</p>
    
    <p class="mb-4">This certification is being issued upon the request of the above-named person for <strong><?php echo $document['purpose']; ?></strong>.</p>
    
    <p class="mb-4">Given this <?php echo date('jS'); ?> day of <?php echo date('F Y'); ?> at <?php echo $document['barangay_name']; ?>, <?php echo $document['municipality']; ?>, <?php echo $document['province']; ?>.</p>
    <?php
    return ob_get_clean();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        @media print {
            .no-print {
                display: none !important;
            }
            .container {
                width: 100%;
                max-width: 100%;
            }
            body {
                font-size: 12pt;
            }
            .document-content {
                padding: 20px !important;
                margin: 0 !important;
                border: none !important;
            }
            /* Ensure logo prints properly */
            .letter-head img {
                display: block !important;
                max-height: 100px !important;
                margin: 0 auto 15px !important;
                page-break-inside: avoid;
            }
            /* Add page breaks where needed */
            .document-footer {
                page-break-inside: avoid;
            }
        }
        .document-content {
            font-family: 'Times New Roman', Times, serif;
            font-size: 14pt;
            line-height: 1.5;
            padding: 40px;
            background-color: white;
            border: 1px solid #ddd;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            min-height: 600px;
        }
        .letter-head {
            text-align: center;
            margin-bottom: 30px;
        }
        .letter-head img {
            max-height: 100px;
            display: block;
            margin: 0 auto 15px;
        }
        .document-footer {
            margin-top: 60px;
        }
        .signature-line {
            width: 250px;
            border-bottom: 1px solid #000;
            margin: 50px auto 10px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar (Only visible in non-print mode) -->
            <div class="no-print">
                <?php include '../../includes/sidebar.php'; ?>
            </div>
            
            <!-- Main Content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom no-print">
                    <h1 class="h2">Generate Document</h1>
                    <div>
                        <a href="view_document.php?id=<?php echo $request_id; ?>" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Document
                        </a>
                        <button class="btn btn-primary" onclick="window.print()">
                            <i class="fas fa-print"></i> Print Document
                        </button>
                        <button class="btn btn-success" id="downloadPdf">
                            <i class="fas fa-file-pdf"></i> Download PDF
                        </button>
                    </div>
                </div>

                <?php if ($db_error): ?>
                <!-- Database Error Message -->
                <div class="alert alert-danger no-print">
                    <strong>Database Connection Error:</strong> Could not connect to the database. Please contact the system administrator.
                </div>
                <?php else: ?>

                <?php if (!empty($errors)): ?>
                <div class="alert alert-danger no-print">
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                        <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>

                <?php if ($document): ?>
                <!-- Document Content -->
                <div class="document-content mb-4">
                    <!-- Letter Head -->
                    <div class="letter-head">
                        <div class="row">
                            <div class="col-md-12">
                                <!-- Add Barangay Logo -->
                                <?php
                                // Get logo path using unified function
                                $logo_path = get_logo_path($conn, '../../');
                                ?>
                                <img src="<?php echo $logo_path; ?>" alt="Barangay Logo" style="max-height: 100px; margin-bottom: 15px;">
                                <h5>Republic of the Philippines</h5>
                                <h5>Province of <?php echo $document['province']; ?></h5>
                                <h5>Municipality of <?php echo $document['municipality']; ?></h5>
                                <h4>BARANGAY <?php echo strtoupper($document['barangay_name']); ?></h4>
                                <h5>OFFICE OF THE PUNONG BARANGAY</h5>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Document Body - Will be different for each document type -->
                    <?php echo getDocumentTemplate($document); ?>
                    
                    <!-- Document Footer with Signature -->
                    <div class="document-footer">
                        <div class="row">
                            <div class="col-md-7">
                                <!-- Control number, etc can go here -->
                                <p>Control No: <?php echo date('Ymd') . '-' . $document['document_id']; ?></p>
                                <p>Issued on: <?php echo date('F j, Y'); ?></p>
                            </div>
                            <div class="col-md-5 text-center">
                                <div class="signature-line"></div>
                                <p><strong><?php echo strtoupper($document['barangay_captain']); ?></strong></p>
                                <p>Punong Barangay</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Document Notes (Only visible in non-print mode) -->
                <div class="card mb-4 no-print">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i> Notes</h5>
                    </div>
                    <div class="card-body">
                        <ul>
                            <li>This is a preview of how the document will look when printed.</li>
                            <li>Click the Print Document button to print.</li>
                            <li>The PDF download functionality is a placeholder and will be implemented in future updates.</li>
                            <li>Document templates can be customized by the system administrator.</li>
                        </ul>
                    </div>
                </div>
                <?php else: ?>
                <div class="alert alert-warning no-print">
                    <i class="fas fa-exclamation-triangle"></i> Document request not found or you don't have permission to view it.
                </div>
                <?php endif; ?>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="../../assets/js/main.js"></script>
    
    <script>
        $(document).ready(function() {
            // Placeholder for PDF download functionality
            $('#downloadPdf').on('click', function() {
                alert('PDF download functionality will be implemented in a future update.');
            });
        });
    </script>
</body>
</html> 