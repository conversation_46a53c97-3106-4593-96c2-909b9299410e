<?php
session_start();
include '../../includes/config/database.php';
include '../../includes/functions/permission_functions.php';
include '../../includes/functions/utility.php';
include '../../includes/functions/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check permission
if (!hasPermission('add_complaint')) {
    header("Location: ../../index.php");
    exit;
}

// Page title
$page_title = "File New Complaint - Barangay Management System";

// Get residents for dropdown
$residents_query = "SELECT resident_id, first_name, middle_name, last_name FROM residents WHERE status = 'Active' ORDER BY last_name, first_name";
try {
    $residents_stmt = $conn->prepare($residents_query);
    $residents_stmt->execute();
    $residents = $residents_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error fetching residents: " . $e->getMessage());
    $residents = [];
}

// Get officials for assignment dropdown
try {
    // Check if officials table exists
    $check_table = $conn->prepare("SHOW TABLES LIKE 'officials'");
    $check_table->execute();
    $officials_exists = $check_table->rowCount() > 0;
    
    if ($officials_exists) {
        // Fetch officials from the officials table
        $officials_query = "SELECT o.official_id, CONCAT(o.position, ': ', r.first_name, ' ', r.last_name) AS name 
                         FROM officials o
                         JOIN residents r ON o.resident_id = r.resident_id
                         WHERE o.status = 'Active'
                         ORDER BY o.position";
        $officials_stmt = $conn->prepare($officials_query);
        $officials_stmt->execute();
        $officials = $officials_stmt->fetchAll(PDO::FETCH_ASSOC);
    } else {
        // Check if staff table exists as an alternative
        $check_staff = $conn->prepare("SHOW TABLES LIKE 'staff'");
        $check_staff->execute();
        $staff_exists = $check_staff->rowCount() > 0;
        
        if ($staff_exists) {
            // Use staff table instead
            $officials_query = "SELECT staff_id as official_id, CONCAT(position, ': ', first_name, ' ', last_name) AS name 
                             FROM staff
                             WHERE status = 'Active'
                             ORDER BY position";
            $officials_stmt = $conn->prepare($officials_query);
            $officials_stmt->execute();
            $officials = $officials_stmt->fetchAll(PDO::FETCH_ASSOC);
        } else {
            // No officials or staff table
            $officials = [];
        }
    }
} catch (PDOException $e) {
    error_log("Error fetching officials: " . $e->getMessage());
    $officials = [];
}

// Process form submission
$success_message = '';
$error_message = '';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Debug logging - log what's being received
    error_log("POST data received: " . print_r($_POST, true));
    
    // Get form data
    // For resident IDs, check both standard and hidden fields (fallback)
    $complainant_id = !empty($_POST['complainant_id']) ? (int)$_POST['complainant_id'] : 
                     (!empty($_POST['hidden_complainant_id']) ? (int)$_POST['hidden_complainant_id'] : NULL);
    
    $respondent_id = !empty($_POST['respondent_id']) ? (int)$_POST['respondent_id'] : 
                    (!empty($_POST['hidden_respondent_id']) ? (int)$_POST['hidden_respondent_id'] : NULL);
                    
    $complaint_type = sanitize($_POST['complaint_type']);
    $complaint_details = sanitize($_POST['complaint_details']);
    $incident_date = sanitize($_POST['incident_date']);
    $incident_location = sanitize($_POST['incident_location']);
    $status = sanitize($_POST['status']);
    $assigned_official_id = !empty($_POST['assigned_official_id']) ? (int)$_POST['assigned_official_id'] : NULL;
    $witnesses = sanitize($_POST['witnesses'] ?? '');
    
    // Log what was processed
    error_log("Processed complainant_id: " . ($complainant_id ?? 'NULL'));
    error_log("Processed respondent_id: " . ($respondent_id ?? 'NULL'));
    
    // Non-resident complainant handling
    $complainant_name = sanitize($_POST['complainant_name'] ?? '');
    $complainant_address = sanitize($_POST['complainant_address'] ?? '');
    $complainant_contact = sanitize($_POST['complainant_contact'] ?? '');
    
    // Non-resident respondent handling
    $respondent_name = sanitize($_POST['respondent_name'] ?? '');
    $respondent_address = sanitize($_POST['respondent_address'] ?? '');
    $respondent_contact = sanitize($_POST['respondent_contact'] ?? '');
    
    // Check if is_resident checkboxes are set
    $is_resident_complainant = isset($_POST['is_resident_complainant']);
    $is_resident_respondent = isset($_POST['is_resident_respondent']);
    
    // Validate required fields
    if (empty($complaint_type) || empty($complaint_details) || empty($incident_date) || empty($incident_location)) {
        $error_message = "Please fill in all required fields";
    } else if ($is_resident_complainant && empty($complainant_id)) {
        $error_message = "Please select a complainant from the residents list";
    } else if ($is_resident_respondent && empty($respondent_id)) {
        $error_message = "Please select a respondent from the residents list";
    } else if (!$is_resident_complainant && empty($complainant_name)) {
        $error_message = "Please enter the complainant's name";
    } else if (!$is_resident_respondent && empty($respondent_name)) {
        $error_message = "Please enter the respondent's name";
    } else {
        try {
            // Begin transaction
            $conn->beginTransaction();
            
            // Debug SQL query construction
            error_log("Starting to build SQL query for complaint insertion");
            
            // For direct debugging - simplified query just for testing
            if ($is_resident_complainant && $is_resident_respondent) {
                // Simple direct query when both are residents
                $insert_query = "INSERT INTO complaints (
                            complainant_id, respondent_id, complaint_type, 
                            complaint_details, incident_date, incident_location, 
                            status, assigned_official_id, witnesses, date_filed
                        ) VALUES (
                            :complainant_id, :respondent_id, :complaint_type, 
                            :complaint_details, :incident_date, :incident_location, 
                            :status, :assigned_official_id, :witnesses, NOW()
                        )";
                        
                error_log("Using direct resident query: " . $insert_query);
                
                $insert_stmt = $conn->prepare($insert_query);
                $insert_stmt->bindValue(':complainant_id', $complainant_id, PDO::PARAM_INT);
                $insert_stmt->bindValue(':respondent_id', $respondent_id, PDO::PARAM_INT);
                $insert_stmt->bindValue(':complaint_type', $complaint_type);
                $insert_stmt->bindValue(':complaint_details', $complaint_details);
                $insert_stmt->bindValue(':incident_date', $incident_date);
                $insert_stmt->bindValue(':incident_location', $incident_location);
                $insert_stmt->bindValue(':status', $status);
                $insert_stmt->bindValue(':assigned_official_id', !empty($assigned_official_id) ? $assigned_official_id : null, PDO::PARAM_INT);
                $insert_stmt->bindValue(':witnesses', $witnesses);
            } else {
                // Use the dynamic query construction for mixed cases
                // Prepare the base query
                $insert_query = "INSERT INTO complaints (
                            complaint_type, complaint_details, 
                            incident_date, incident_location, 
                            status, assigned_official_id, witnesses, date_filed";
                
                // Add complainant/respondent fields based on selection
                if ($is_resident_complainant) {
                    $insert_query .= ", complainant_id";
                } else {
                    $insert_query .= ", complainant_name, complainant_address, complainant_contact";
                }
                
                if ($is_resident_respondent) {
                    $insert_query .= ", respondent_id";
                } else {
                    $insert_query .= ", respondent_name, respondent_address, respondent_contact";
                }
                
                $insert_query .= ") VALUES (
                            :complaint_type, :complaint_details, 
                            :incident_date, :incident_location, 
                            :status, :assigned_official_id, :witnesses, NOW()";
                
                // Add values placeholders based on selection
                if ($is_resident_complainant) {
                    $insert_query .= ", :complainant_id";
                } else {
                    $insert_query .= ", :complainant_name, :complainant_address, :complainant_contact";
                }
                
                if ($is_resident_respondent) {
                    $insert_query .= ", :respondent_id";
                } else {
                    $insert_query .= ", :respondent_name, :respondent_address, :respondent_contact";
                }
                
                $insert_query .= ")";
                
                error_log("Using dynamic query: " . $insert_query);
                
                $insert_stmt = $conn->prepare($insert_query);
                
                // Bind common parameters
                $insert_stmt->bindValue(':complaint_type', $complaint_type);
                $insert_stmt->bindValue(':complaint_details', $complaint_details);
                $insert_stmt->bindValue(':incident_date', $incident_date);
                $insert_stmt->bindValue(':incident_location', $incident_location);
                $insert_stmt->bindValue(':status', $status);
                $insert_stmt->bindValue(':assigned_official_id', !empty($assigned_official_id) ? $assigned_official_id : null, PDO::PARAM_INT);
                $insert_stmt->bindValue(':witnesses', $witnesses);
                
                // Bind complainant parameters based on selection
                if ($is_resident_complainant) {
                    $insert_stmt->bindValue(':complainant_id', $complainant_id, PDO::PARAM_INT);
                    error_log("Binding complainant_id with value: " . $complainant_id);
                } else {
                    $insert_stmt->bindValue(':complainant_name', $complainant_name);
                    $insert_stmt->bindValue(':complainant_address', $complainant_address);
                    $insert_stmt->bindValue(':complainant_contact', $complainant_contact);
                }
                
                // Bind respondent parameters based on selection
                if ($is_resident_respondent) {
                    $insert_stmt->bindValue(':respondent_id', $respondent_id, PDO::PARAM_INT);
                    error_log("Binding respondent_id with value: " . $respondent_id);
                } else {
                    $insert_stmt->bindValue(':respondent_name', $respondent_name);
                    $insert_stmt->bindValue(':respondent_address', $respondent_address);
                    $insert_stmt->bindValue(':respondent_contact', $respondent_contact);
                }
            }
            
            // Execute insert statement
            error_log("Executing insert statement");
            $insert_stmt->execute();
            
            $complaint_id = $conn->lastInsertId();
            error_log("Complaint inserted with ID: " . $complaint_id);
            
            // Handle file uploads
            $evidence_files = [];
            if (isset($_FILES['evidence_files']) && !empty($_FILES['evidence_files']['name'][0])) {
                // Create directory if it doesn't exist
                $upload_dir = "../../uploads/complaints/" . $complaint_id . "/evidence/";
                if (!file_exists($upload_dir)) {
                    mkdir($upload_dir, 0777, true);
                }
                
                // Upload each file
                for ($i = 0; $i < count($_FILES['evidence_files']['name']); $i++) {
                    $file_name = $_FILES['evidence_files']['name'][$i];
                    $file_tmp = $_FILES['evidence_files']['tmp_name'][$i];
                    $file_size = $_FILES['evidence_files']['size'][$i];
                    $file_error = $_FILES['evidence_files']['error'][$i];
                    
                    // Check for errors
                    if ($file_error === 0) {
                        // Generate unique filename
                        $file_ext = pathinfo($file_name, PATHINFO_EXTENSION);
                        $safe_filename = 'evidence_' . uniqid() . '.' . $file_ext;
                        $target_file = $upload_dir . $safe_filename;
                        
                        // Move uploaded file
                        if (move_uploaded_file($file_tmp, $target_file)) {
                            $evidence_files[] = 'uploads/complaints/' . $complaint_id . '/evidence/' . $safe_filename;
                        }
                    }
                }
                
                // Save file paths to database
                if (!empty($evidence_files)) {
                    $evidence_paths = json_encode($evidence_files);
                    $update_query = "UPDATE complaints SET evidence_files = :evidence_files WHERE complaint_id = :complaint_id";
                    $update_stmt = $conn->prepare($update_query);
                    $update_stmt->bindValue(':evidence_files', $evidence_paths);
                    $update_stmt->bindValue(':complaint_id', $complaint_id, PDO::PARAM_INT);
                    $update_stmt->execute();
                }
            }
            
            // Log activity
            logActivity($conn, 'Add Complaint', $_SESSION['user_id'], 'complaints', "Filed new complaint: $complaint_type ($complaint_id)");
            
            // Commit transaction
            $conn->commit();
            
            $success_message = "Complaint filed successfully!";
            
            // Redirect to complaints list
            header("Location: complaints.php?success=added");
            exit;
        } catch (PDOException $e) {
            // Rollback transaction on error
            $conn->rollBack();
            $error_message = "Error: " . $e->getMessage();
            error_log("Error adding complaint: " . $e->getMessage());
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        /* Card styling with clean modern design */
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border: none;
            margin-bottom: 24px;
        }
        
        /* Main content container */
        .content-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        /* Page header */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }
        
        .page-header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
        }
        
        /* Section Cards */
        .section-card {
            border-radius: 8px;
            padding: 0;
            margin-bottom: 24px;
            overflow: hidden;
        }
        
        .section-header {
            padding: 12px 16px;
            font-weight: 600;
            color: white;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .section-header .icon {
            font-size: 1.2rem;
        }
        
        .section-body {
            padding: 20px;
            background-color: white;
        }
        
        /* Primary (blue) section */
        .section-primary .section-header {
            background-color: #0d6efd;
        }
        
        /* Info (cyan) section */
        .section-info .section-header {
            background-color: #0dcaf0;
            color: #000;
        }
        
        /* Warning (yellow) section */
        .section-warning .section-header {
            background-color: #ffc107;
            color: #000;
        }
        
        /* Success (green) section */
        .section-success .section-header {
            background-color: #198754;
        }
        
        /* Danger (red) section */
        .section-danger .section-header {
            background-color: #dc3545;
        }
        
        /* Secondary (gray) section */
        .section-secondary .section-header {
            background-color: #6c757d;
        }
        
        /* Form elements */
        .form-label {
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
        }
        
        .form-control, .form-select {
            padding: 10px 12px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            font-size: 1rem;
        }
        
        .form-select {
            background-position: right 10px center;
        }
        
        .form-check-label {
            font-weight: 500;
        }
        
        /* Button styling */
        .btn {
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: 500;
        }
        
        .btn-back {
            background-color: #f0f0f0;
            color: #333;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px 16px;
            border-radius: 6px;
        }
        
        .btn-back:hover {
            background-color: #e0e0e0;
        }
        
        /* Alert styling */
        .alert {
            border-radius: 6px;
            padding: 12px 16px;
            margin-bottom: 24px;
        }
        
        /* Badge styling */
        .badge {
            padding: 5px 10px;
            font-weight: 500;
            border-radius: 30px;
        }
        
        /* Grid spacing */
        .mb-4 {
            margin-bottom: 24px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        /* Select2 customization */
        .select2-container--bootstrap-5 .select2-selection {
            height: 38px;
            padding: 4px 12px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            box-shadow: none;
        }
        
        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__arrow {
            height: 36px;
        }
        
        .select2-container--bootstrap-5 .select2-selection--single .select2-selection__rendered {
            line-height: 28px;
            padding: 0;
            color: #212529;
        }
        
        .select2-dropdown {
            border-color: #ced4da;
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .select2-container--bootstrap-5 .select2-results__option--highlighted[aria-selected] {
            background-color: #0d6efd;
            color: white;
        }
        
        /* Custom Select2 resident styling */
        .select2-result {
            display: flex;
            align-items: center;
            padding: 2px 0;
        }
        
        .select2-result__avatar {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 22px;
            height: 22px;
            background-color: #f8f9fa;
            border-radius: 50%;
            margin-right: 6px;
            color: #6c757d;
        }
        
        .select2-container .select2-results__option {
            line-height: 1.3;
        }
        
        .select2-result__avatar i {
            font-size: 12px;
        }
        
        .select2-result__meta {
            flex: 1;
        }
        
        .select2-result__title {
            font-weight: 500;
            color: #212529;
            font-size: 14px;
        }
        
        .select2-container--bootstrap-5 .select2-results__option {
            padding: 4px 8px;
            font-size: 14px;
        }
        
        /* Override the dropdown width to match the input */
        .select2-container--bootstrap-5 .select2-dropdown {
            min-width: 100% !important;
        }
        
        /* Limit the dropdown height to show fewer items */
        .select2-container--bootstrap-5 .select2-results > .select2-results__options {
            max-height: 200px;
            overflow-y: auto;
        }
        
        /* Custom class for smaller dropdown */
        .select2-dropdown-sm .select2-results__option {
            padding: 4px 6px !important;
            font-size: 13px !important;
        }
        
        .select2-dropdown-sm .select2-search--dropdown .select2-search__field {
            padding: 6px;
            font-size: 13px;
        }
        
        /* Required field indicator */
        .required-indicator {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
                <div class="content-container">
                    <div class="page-header">
                        <h1>📋 File New Complaint</h1>
                        <a href="complaints.php" class="btn-back">
                            ⬅️ Back to Complaints
                        </a>
                    </div>
                    
                    <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success" role="alert">
                        ✅ <?php echo $success_message; ?>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger" role="alert">
                        ❌ <?php echo $error_message; ?>
                    </div>
                    <?php endif; ?>
                    
                    <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" method="POST" class="needs-validation" enctype="multipart/form-data" novalidate>
                        <!-- Complainant Information -->
                        <div class="section-card section-info shadow">
                            <div class="section-header">
                                <span class="icon">👤</span>
                                <span>Complainant Information</span>
                            </div>
                            <div class="section-body">
                                <div class="form-group">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="isResidentComplainant" name="is_resident_complainant" checked onchange="toggleComplainantFields()">
                                        <label class="form-check-label" for="isResidentComplainant">
                                            Complainant is a registered resident
                                        </label>
                                    </div>
                                </div>
                                
                                <div id="residentComplainantFields">
                                    <div class="form-group">
                                        <label for="complainant_id" class="form-label">Select Complainant <span class="required-indicator">*</span></label>
                                        <select class="form-select select2" id="complainant_id" name="complainant_id" required>
                                            <option value=""></option>
                                            <?php foreach ($residents as $resident): ?>
                                            <option value="<?php echo $resident['resident_id']; ?>">
                                                <?php echo $resident['last_name'] . ', ' . $resident['first_name'] . ' ' . $resident['middle_name']; ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <div class="invalid-feedback">Please select a complainant</div>
                                    </div>
                                </div>
                                
                                <div id="nonResidentComplainantFields" style="display: none;">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="complainant_name" class="form-label">Complainant Name <span class="required-indicator">*</span></label>
                                                <input type="text" class="form-control" id="complainant_name" name="complainant_name">
                                                <div class="invalid-feedback">Please enter complainant name</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="complainant_contact" class="form-label">📞 Contact Number</label>
                                                <input type="text" class="form-control" id="complainant_contact" name="complainant_contact">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="complainant_address" class="form-label">📍 Address <span class="required-indicator">*</span></label>
                                        <textarea class="form-control" id="complainant_address" name="complainant_address" rows="2"></textarea>
                                        <div class="invalid-feedback">Please enter complainant address</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Respondent Information -->
                        <div class="section-card section-warning shadow">
                            <div class="section-header">
                                <span class="icon">👥</span>
                                <span>Respondent Information</span>
                            </div>
                            <div class="section-body">
                                <div class="form-group">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="isResidentRespondent" name="is_resident_respondent" checked onchange="toggleRespondentFields()">
                                        <label class="form-check-label" for="isResidentRespondent">
                                            Respondent is a registered resident
                                        </label>
                                    </div>
                                </div>
                                
                                <div id="residentRespondentFields">
                                    <div class="form-group">
                                        <label for="respondent_id" class="form-label">Select Respondent <span class="required-indicator">*</span></label>
                                        <select class="form-select select2" id="respondent_id" name="respondent_id" required>
                                            <option value=""></option>
                                            <?php foreach ($residents as $resident): ?>
                                            <option value="<?php echo $resident['resident_id']; ?>">
                                                <?php echo $resident['last_name'] . ', ' . $resident['first_name'] . ' ' . $resident['middle_name']; ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <div class="invalid-feedback">Please select a respondent</div>
                                    </div>
                                </div>
                                
                                <div id="nonResidentRespondentFields" style="display: none;">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="respondent_name" class="form-label">Respondent Name <span class="required-indicator">*</span></label>
                                                <input type="text" class="form-control" id="respondent_name" name="respondent_name">
                                                <div class="invalid-feedback">Please enter respondent name</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="respondent_contact" class="form-label">📞 Contact Number</label>
                                                <input type="text" class="form-control" id="respondent_contact" name="respondent_contact">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="respondent_address" class="form-label">📍 Address <span class="required-indicator">*</span></label>
                                        <textarea class="form-control" id="respondent_address" name="respondent_address" rows="2"></textarea>
                                        <div class="invalid-feedback">Please enter respondent address</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Complaint Details -->
                        <div class="section-card section-primary shadow">
                            <div class="section-header">
                                <span class="icon">📝</span>
                                <span>Complaint Details</span>
                            </div>
                            <div class="section-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="complaint_type" class="form-label">🔖 Complaint Type <span class="required-indicator">*</span></label>
                                            <select class="form-select" id="complaint_type" name="complaint_type" required>
                                                <option value="">Select Complaint Type</option>
                                                <option value="Dispute">Dispute</option>
                                                <option value="Nuisance">Nuisance</option>
                                                <option value="Theft">Theft</option>
                                                <option value="Assault">Assault</option>
                                                <option value="Property Damage">Property Damage</option>
                                                <option value="Harassment">Harassment</option>
                                                <option value="Noise Complaint">Noise Complaint</option>
                                                <option value="Domestic Violence">Domestic Violence</option>
                                                <option value="Other">Other</option>
                                            </select>
                                            <div class="invalid-feedback">Please select a complaint type</div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="incident_date" class="form-label">📅 Incident Date <span class="required-indicator">*</span></label>
                                            <input type="date" class="form-control" id="incident_date" name="incident_date" required max="<?php echo date('Y-m-d'); ?>">
                                            <div class="invalid-feedback">Please provide the incident date</div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="incident_location" class="form-label">📍 Incident Location <span class="required-indicator">*</span></label>
                                    <input type="text" class="form-control" id="incident_location" name="incident_location" required>
                                    <div class="invalid-feedback">Please provide the incident location</div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="complaint_details" class="form-label">📄 Complaint Details <span class="required-indicator">*</span></label>
                                    <textarea class="form-control" id="complaint_details" name="complaint_details" rows="5" required></textarea>
                                    <div class="invalid-feedback">Please provide the complaint details</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Case Management -->
                        <div class="section-card section-success shadow">
                            <div class="section-header">
                                <span class="icon">⚖️</span>
                                <span>Case Management</span>
                            </div>
                            <div class="section-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="status" class="form-label">📊 Status</label>
                                            <select class="form-select" id="status" name="status">
                                                <option value="Pending">⏳ Pending</option>
                                                <option value="Under Investigation">🔍 Under Investigation</option>
                                                <option value="Resolved">✅ Resolved</option>
                                                <option value="Dismissed">❌ Dismissed</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="assigned_official_id" class="form-label">👮 Assign To</label>
                                            <select class="form-select" id="assigned_official_id" name="assigned_official_id">
                                                <option value="">Unassigned</option>
                                                <?php foreach ($officials as $official): ?>
                                                    <option value="<?php echo $official['official_id']; ?>">
                                                        <?php echo $official['name']; ?>
                                                    </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="form-group">
                                    <label for="witnesses" class="form-label">👁️ Witnesses</label>
                                    <textarea class="form-control" id="witnesses" name="witnesses" rows="2" placeholder="Enter names of witnesses, separated by commas"></textarea>
                                    <div class="form-text">List any witnesses to the incident. Include their names and contact information if available.</div>
                                </div>
                                
                                <div class="form-group mb-0">
                                    <label for="evidence_files" class="form-label">🔍 Evidence Files</label>
                                    <input type="file" class="form-control" id="evidence_files" name="evidence_files[]" multiple>
                                    <div class="form-text">Upload any evidence files related to this complaint (images, documents, etc.)</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="d-flex justify-content-end mt-4">
                            <button type="reset" class="btn btn-secondary me-2">
                                ❌ Clear Form
                            </button>
                            <button type="submit" class="btn btn-primary">
                                💾 Submit Complaint
                            </button>
                        </div>
                    </form>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="../../assets/js/main.js"></script>
    
    <script>
        // Initialize Select2
        $(document).ready(function() {
            $('.select2').select2({
                theme: 'bootstrap-5',
                width: '100%',
                dropdownCssClass: 'select2-dropdown-sm',
                placeholder: 'Select Resident',
                allowClear: true,
                templateResult: formatResident,
                templateSelection: formatResidentSelection,
                minimumResultsForSearch: 6,
                closeOnSelect: true,
                dropdownParent: $('body')
            });
            
            // Initialize toggle fields on page load
            toggleComplainantFields();
            toggleRespondentFields();
        });
        
        // Function to format resident options in dropdown
        function formatResident(resident) {
            if (!resident.id) {
                return resident.text;
            }
            
            return $('<div class="select2-result clearfix">' +
                     '<div class="select2-result__avatar"><i class="fas fa-user"></i></div>' +
                     '<div class="select2-result__meta">' +
                     '<div class="select2-result__title">' + resident.text + '</div>' +
                     '</div>' +
                     '</div>');
        }
        
        // Function to format the selected resident
        function formatResidentSelection(resident) {
            return resident.text || resident.id;
        }
        
        // Toggle complainant fields
        function toggleComplainantFields() {
            const isResident = document.getElementById('isResidentComplainant').checked;
            const residentFields = document.getElementById('residentComplainantFields');
            const nonResidentFields = document.getElementById('nonResidentComplainantFields');
            
            // Get form elements
            const complainantIdSelect = document.getElementById('complainant_id');
            const complainantNameInput = document.getElementById('complainant_name');
            const complainantAddressInput = document.getElementById('complainant_address');
            
            if (isResident) {
                // Show resident fields, hide non-resident fields
                residentFields.style.display = 'block';
                nonResidentFields.style.display = 'none';
                
                // Make resident field required, remove requirement from non-resident fields
                complainantIdSelect.setAttribute('required', 'required');
                complainantNameInput.removeAttribute('required');
                complainantAddressInput.removeAttribute('required');
                
                // For debugging - add direct event listener for dropdown change
                complainantIdSelect.addEventListener('change', function() {
                    console.log('Complainant ID changed to: ' + this.value);
                    // Also add a hidden input as a backup
                    let hiddenComplainantId = document.getElementById('hidden_complainant_id');
                    if (!hiddenComplainantId) {
                        hiddenComplainantId = document.createElement('input');
                        hiddenComplainantId.type = 'hidden';
                        hiddenComplainantId.id = 'hidden_complainant_id';
                        hiddenComplainantId.name = 'hidden_complainant_id';
                        document.querySelector('form').appendChild(hiddenComplainantId);
                    }
                    hiddenComplainantId.value = this.value;
                });
                
                // Extra handling for Select2
                try {
                    if ($.fn.select2 && $(complainantIdSelect).hasClass('select2')) {
                        $(complainantIdSelect).on('select2:select', function (e) {
                            console.log('Select2 complainant selection:', e.params.data);
                        });
                    }
                } catch (e) {
                    console.error('Select2 event binding error:', e);
                }
            } else {
                // Show non-resident fields, hide resident fields
                residentFields.style.display = 'none';
                nonResidentFields.style.display = 'block';
                
                // Make non-resident fields required, remove requirement from resident field
                complainantIdSelect.removeAttribute('required');
                complainantNameInput.setAttribute('required', 'required');
                complainantAddressInput.setAttribute('required', 'required');
                
                // Clear the resident selection
                complainantIdSelect.value = '';
                
                // Also clear Select2 if it's initialized
                try {
                    if ($.fn.select2 && $(complainantIdSelect).hasClass('select2')) {
                        $(complainantIdSelect).val(null).trigger('change');
                    }
                } catch (e) {
                    console.error('Select2 reset error:', e);
                }
            }
        }
        
        // Toggle respondent fields
        function toggleRespondentFields() {
            const isResident = document.getElementById('isResidentRespondent').checked;
            const residentFields = document.getElementById('residentRespondentFields');
            const nonResidentFields = document.getElementById('nonResidentRespondentFields');
            
            // Get form elements
            const respondentIdSelect = document.getElementById('respondent_id');
            const respondentNameInput = document.getElementById('respondent_name');
            const respondentAddressInput = document.getElementById('respondent_address');
            
            if (isResident) {
                // Show resident fields, hide non-resident fields
                residentFields.style.display = 'block';
                nonResidentFields.style.display = 'none';
                
                // Make resident field required, remove requirement from non-resident fields
                respondentIdSelect.setAttribute('required', 'required');
                respondentNameInput.removeAttribute('required');
                respondentAddressInput.removeAttribute('required');
                
                // For debugging - add direct event listener for dropdown change
                respondentIdSelect.addEventListener('change', function() {
                    console.log('Respondent ID changed to: ' + this.value);
                    // Also add a hidden input as a backup
                    let hiddenRespondentId = document.getElementById('hidden_respondent_id');
                    if (!hiddenRespondentId) {
                        hiddenRespondentId = document.createElement('input');
                        hiddenRespondentId.type = 'hidden';
                        hiddenRespondentId.id = 'hidden_respondent_id';
                        hiddenRespondentId.name = 'hidden_respondent_id';
                        document.querySelector('form').appendChild(hiddenRespondentId);
                    }
                    hiddenRespondentId.value = this.value;
                });
                
                // Extra handling for Select2
                try {
                    if ($.fn.select2 && $(respondentIdSelect).hasClass('select2')) {
                        $(respondentIdSelect).on('select2:select', function (e) {
                            console.log('Select2 respondent selection:', e.params.data);
                        });
                    }
                } catch (e) {
                    console.error('Select2 event binding error:', e);
                }
            } else {
                // Show non-resident fields, hide resident fields
                residentFields.style.display = 'none';
                nonResidentFields.style.display = 'block';
                
                // Make non-resident fields required, remove requirement from resident field
                respondentIdSelect.removeAttribute('required');
                respondentNameInput.setAttribute('required', 'required');
                respondentAddressInput.setAttribute('required', 'required');
                
                // Clear the resident selection
                respondentIdSelect.value = '';
                
                // Also clear Select2 if it's initialized
                try {
                    if ($.fn.select2 && $(respondentIdSelect).hasClass('select2')) {
                        $(respondentIdSelect).val(null).trigger('change');
                    }
                } catch (e) {
                    console.error('Select2 reset error:', e);
                }
            }
        }
        
        // Form validation
        (function() {
            'use strict';
            
            // Fetch all forms we want to apply validation to
            var forms = document.querySelectorAll('.needs-validation');
            
            // Loop over them and prevent submission
            Array.prototype.slice.call(forms).forEach(function(form) {
                form.addEventListener('submit', function(event) {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }
                    
                    form.classList.add('was-validated');
                }, false);
            });
        })();
    </script>
</body>
</html> 