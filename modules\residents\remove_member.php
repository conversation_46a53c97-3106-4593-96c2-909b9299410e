<?php
session_start();
include '../../includes/config/database.php';
include '../../includes/functions/permission_functions.php';
include '../../includes/functions/utility.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check permission
if (!hasPermission('edit_household')) {
    header("Location: ../../index.php");
    exit;
}

// Check if request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    header("Location: households.php");
    exit;
}

// Validate input
if (!isset($_POST['household_id']) || !isset($_POST['resident_id'])) {
    $_SESSION['error'] = "Invalid request. Missing required parameters.";
    header("Location: households.php");
    exit;
}

$household_id = (int)$_POST['household_id'];
$resident_id = (int)$_POST['resident_id'];

// Get database connection
$db = Database::getInstance();
$conn = $db->getConnection();

// Verify the household exists
$household_query = "SELECT household_id, household_head_id FROM households WHERE household_id = :household_id";
$household_stmt = $conn->prepare($household_query);
$household_stmt->bindParam(':household_id', $household_id, PDO::PARAM_INT);
$household_stmt->execute();

if ($household_stmt->rowCount() == 0) {
    $_SESSION['error'] = "Household not found.";
    header("Location: households.php");
    exit;
}

$household = $household_stmt->fetch(PDO::FETCH_ASSOC);

// Prevent removing the household head
if ($household['household_head_id'] == $resident_id) {
    $_SESSION['error'] = "Cannot remove the household head. Change the household head first or delete the entire household.";
    header("Location: view_household.php?id=$household_id");
    exit;
}

// Verify the resident is a member of the household
$member_query = "SELECT * FROM household_members WHERE household_id = :household_id AND resident_id = :resident_id";
$member_stmt = $conn->prepare($member_query);
$member_stmt->bindParam(':household_id', $household_id, PDO::PARAM_INT);
$member_stmt->bindParam(':resident_id', $resident_id, PDO::PARAM_INT);
$member_stmt->execute();

if ($member_stmt->rowCount() == 0) {
    $_SESSION['error'] = "The resident is not a member of this household.";
    header("Location: view_household.php?id=$household_id");
    exit;
}

// Get resident name for logging
$resident_query = "SELECT CONCAT(first_name, ' ', last_name) as full_name FROM residents WHERE resident_id = :resident_id";
$resident_stmt = $conn->prepare($resident_query);
$resident_stmt->bindParam(':resident_id', $resident_id, PDO::PARAM_INT);
$resident_stmt->execute();
$resident = $resident_stmt->fetch(PDO::FETCH_ASSOC);

// Begin transaction
$conn->beginTransaction();

try {
    // Remove the resident from household
    $delete_query = "DELETE FROM household_members WHERE household_id = :household_id AND resident_id = :resident_id";
    $delete_stmt = $conn->prepare($delete_query);
    $delete_stmt->bindParam(':household_id', $household_id, PDO::PARAM_INT);
    $delete_stmt->bindParam(':resident_id', $resident_id, PDO::PARAM_INT);

    if (!$delete_stmt->execute()) {
        throw new Exception("Failed to remove member: " . implode(", ", $delete_stmt->errorInfo()));
    }

    // Log activity
    logActivity("Removed resident " . $resident['full_name'] . " from household ID: $household_id");

    // Commit transaction
    $conn->commit();

    // Redirect to edit_household_members.php with success message
    header("Location: edit_household_members.php?id=$household_id&success=3");
    exit;

} catch (Exception $e) {
    // Rollback transaction on error
    $conn->rollBack();

    $_SESSION['error'] = $e->getMessage();
    header("Location: edit_household_members.php?id=$household_id");
    exit;
}
?>