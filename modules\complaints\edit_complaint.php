<?php
session_start();
include '../../includes/config/database.php';
include '../../includes/functions/permission_functions.php';
include '../../includes/functions/utility.php';
include '../../includes/functions/functions.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check permission
if (!hasPermission('edit_complaint')) {
    header("Location: ../../index.php");
    exit;
}

// Initialize variables
$success_message = '';
$error_message = '';
$complaint_data = null;
$residents = [];
$officials = [];

// Check if complaint ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = "No complaint ID provided.";
    header("Location: complaints.php");
    exit;
}

$complaint_id = (int)$_GET['id'];

// Get residents for dropdown
$residents_query = "SELECT resident_id, first_name, middle_name, last_name FROM residents WHERE status = 'Active' ORDER BY last_name, first_name";
try {
    $residents_stmt = $conn->prepare($residents_query);
    $residents_stmt->execute();
    $residents = $residents_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error fetching residents: " . $e->getMessage());
    $residents = [];
}

// Get officials for dropdown
try {
    // Check if officials table exists
    $check_table = $conn->prepare("SHOW TABLES LIKE 'officials'");
    $check_table->execute();
    $officials_exists = $check_table->rowCount() > 0;

    if ($officials_exists) {
        // Fetch officials from the officials table
        $officials_query = "SELECT o.official_id, CONCAT(o.position, ': ', r.first_name, ' ', r.last_name) AS name
                         FROM officials o
                         JOIN residents r ON o.resident_id = r.resident_id
                         WHERE o.status = 'Active'
                         ORDER BY o.position";
        $officials_stmt = $conn->prepare($officials_query);
        $officials_stmt->execute();
        $officials = $officials_stmt->fetchAll(PDO::FETCH_ASSOC);
    } else {
        // Check if staff table exists as an alternative
        $check_staff = $conn->prepare("SHOW TABLES LIKE 'staff'");
        $check_staff->execute();
        $staff_exists = $check_staff->rowCount() > 0;

        if ($staff_exists) {
            // Use staff table instead
            $officials_query = "SELECT staff_id as official_id, CONCAT(position, ': ', first_name, ' ', last_name) AS name
                             FROM staff
                             WHERE status = 'Active'
                             ORDER BY position";
            $officials_stmt = $conn->prepare($officials_query);
            $officials_stmt->execute();
            $officials = $officials_stmt->fetchAll(PDO::FETCH_ASSOC);
        } else {
            // No officials or staff table
            $officials = [];
        }
    }
} catch (PDOException $e) {
    error_log("Error fetching officials: " . $e->getMessage());
    $officials = [];
}

// Fetch complaint details
try {
    $query = "SELECT c.*,
             r1.first_name as complainant_first_name, r1.middle_name as complainant_middle_name, r1.last_name as complainant_last_name,
             r2.first_name as respondent_first_name, r2.middle_name as respondent_middle_name, r2.last_name as respondent_last_name
             FROM complaints c
             LEFT JOIN residents r1 ON c.complainant_id = r1.resident_id
             LEFT JOIN residents r2 ON c.respondent_id = r2.resident_id
             WHERE c.complaint_id = :complaint_id";

    $stmt = $conn->prepare($query);
    $stmt->bindValue(':complaint_id', $complaint_id, PDO::PARAM_INT);
    $stmt->execute();

    if ($stmt->rowCount() == 0) {
        $_SESSION['error'] = "Complaint not found.";
        header("Location: complaints.php");
        exit;
    }

    $complaint_data = $stmt->fetch(PDO::FETCH_ASSOC);

    // Check if complaint can be edited (not Resolved or Dismissed)
    if (in_array($complaint_data['status'], ['Resolved', 'Dismissed'])) {
        $_SESSION['error'] = "This complaint cannot be edited because it is already " . $complaint_data['status'] . ".";
        header("Location: view_complaint.php?id=" . $complaint_id);
        exit;
    }
} catch (PDOException $e) {
    error_log("Error fetching complaint: " . $e->getMessage());
    $_SESSION['error'] = "Database error occurred. Please try again.";
    header("Location: complaints.php");
    exit;
}

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get form data
    $complainant_id = !empty($_POST['complainant_id']) ? (int)$_POST['complainant_id'] : NULL;
    $respondent_id = !empty($_POST['respondent_id']) ? (int)$_POST['respondent_id'] : NULL;
    $complaint_type = sanitize($_POST['complaint_type']);
    $complaint_details = sanitize($_POST['complaint_details']);
    $incident_date = sanitize($_POST['incident_date']);
    $incident_location = sanitize($_POST['incident_location']);
    $status = sanitize($_POST['status']);
    $assigned_official_id = !empty($_POST['assigned_official_id']) ? (int)$_POST['assigned_official_id'] : NULL;

    // Non-resident complainant handling
    $complainant_name = sanitize($_POST['complainant_name'] ?? '');
    $complainant_address = sanitize($_POST['complainant_address'] ?? '');
    $complainant_contact = sanitize($_POST['complainant_contact'] ?? '');

    // Non-resident respondent handling
    $respondent_name = sanitize($_POST['respondent_name'] ?? '');
    $respondent_address = sanitize($_POST['respondent_address'] ?? '');
    $respondent_contact = sanitize($_POST['respondent_contact'] ?? '');

    // Validate required fields
    if (empty($complaint_type) || empty($complaint_details) || empty($incident_date) || empty($incident_location)) {
        $error_message = "Please fill in all required fields";
    } else {
        try {
            // Begin transaction
            $conn->beginTransaction();

            // Update complaint data
            $update_query = "UPDATE complaints SET
                           complainant_id = :complainant_id,
                           respondent_id = :respondent_id,
                           complaint_type = :complaint_type,
                           complaint_details = :complaint_details,
                           incident_date = :incident_date,
                           incident_location = :incident_location,
                           status = :status,
                           assigned_official_id = :assigned_official_id,
                           complainant_name = :complainant_name,
                           complainant_address = :complainant_address,
                           complainant_contact = :complainant_contact,
                           respondent_name = :respondent_name,
                           respondent_address = :respondent_address,
                           respondent_contact = :respondent_contact
                           WHERE complaint_id = :complaint_id";

            $update_stmt = $conn->prepare($update_query);
            $update_stmt->bindValue(':complainant_id', $complainant_id, PDO::PARAM_INT);
            $update_stmt->bindValue(':respondent_id', $respondent_id, PDO::PARAM_INT);
            $update_stmt->bindValue(':complaint_type', $complaint_type);
            $update_stmt->bindValue(':complaint_details', $complaint_details);
            $update_stmt->bindValue(':incident_date', $incident_date);
            $update_stmt->bindValue(':incident_location', $incident_location);
            $update_stmt->bindValue(':status', $status);
            $update_stmt->bindValue(':assigned_official_id', $assigned_official_id, PDO::PARAM_INT);
            $update_stmt->bindValue(':complainant_name', $complainant_name);
            $update_stmt->bindValue(':complainant_address', $complainant_address);
            $update_stmt->bindValue(':complainant_contact', $complainant_contact);
            $update_stmt->bindValue(':respondent_name', $respondent_name);
            $update_stmt->bindValue(':respondent_address', $respondent_address);
            $update_stmt->bindValue(':respondent_contact', $respondent_contact);
            $update_stmt->bindValue(':complaint_id', $complaint_id, PDO::PARAM_INT);
            $update_stmt->execute();

            // If status changed, add to complaint updates
            if ($status != $complaint_data['status']) {
                $update_notes = "Status updated from " . $complaint_data['status'] . " to " . $status;
                $insert_update = "INSERT INTO complaint_updates (complaint_id, status, notes, updated_by, update_date)
                                VALUES (:complaint_id, :status, :notes, :updated_by, NOW())";
                $insert_stmt = $conn->prepare($insert_update);
                $insert_stmt->bindValue(':complaint_id', $complaint_id, PDO::PARAM_INT);
                $insert_stmt->bindValue(':status', $status);
                $insert_stmt->bindValue(':notes', $update_notes);
                $insert_stmt->bindValue(':updated_by', $_SESSION['user_id'], PDO::PARAM_INT);
                $insert_stmt->execute();
            }

            // Log activity
            logActivity($conn, "Updated complaint #$complaint_id", $_SESSION['user_id'], 'complaints', 'Edit Complaint');

            // Commit transaction
            $conn->commit();

            $success_message = "Complaint #$complaint_id has been updated successfully.";

            // Refresh complaint data
            $stmt->execute();
            $complaint_data = $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (PDOException $e) {
            // Rollback transaction on error
            if ($conn->inTransaction()) {
                $conn->rollBack();
            }

            error_log("Error updating complaint: " . $e->getMessage());
            $error_message = "Error updating complaint: " . $e->getMessage();
        }
    }
}

// Page title
$page_title = "Edit Complaint - Barangay Management System";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        /* Card styling with clean modern design */
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border: none;
            margin-bottom: 24px;
        }

        /* Main content container */
        .content-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        /* Page header */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }

        .page-header h1 {
            margin: 0;
            font-size: 1.8rem;
            font-weight: 600;
        }

        .page-header .actions {
            display: flex;
            gap: 10px;
        }

        /* Section Cards */
        .section-card {
            border-radius: 8px;
            padding: 0;
            margin-bottom: 24px;
            overflow: hidden;
        }

        .section-header {
            padding: 12px 16px;
            font-weight: 600;
            color: white;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-header .icon {
            font-size: 1.2rem;
        }

        .section-body {
            padding: 20px;
            background-color: white;
        }

        /* Primary (blue) section */
        .section-primary .section-header {
            background-color: #0d6efd;
        }

        /* Info (cyan) section */
        .section-info .section-header {
            background-color: #0dcaf0;
            color: #000;
        }

        /* Warning (yellow) section */
        .section-warning .section-header {
            background-color: #ffc107;
            color: #000;
        }

        /* Success (green) section */
        .section-success .section-header {
            background-color: #198754;
        }

        /* Danger (red) section */
        .section-danger .section-header {
            background-color: #dc3545;
        }

        /* Secondary (gray) section */
        .section-secondary .section-header {
            background-color: #6c757d;
        }

        /* Form elements */
        .form-label {
            font-weight: 600;
            margin-bottom: 8px;
            display: block;
        }

        .form-control, .form-select {
            padding: 10px 12px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            font-size: 1rem;
        }

        .form-select {
            background-position: right 10px center;
        }

        .form-check-label {
            font-weight: 500;
        }

        /* Button styling */
        .btn {
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: 500;
        }

        .btn-back, .btn-view {
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px 16px;
            border-radius: 6px;
        }

        .btn-back {
            background-color: #f0f0f0;
            color: #333;
        }

        .btn-back:hover {
            background-color: #e0e0e0;
        }

        .btn-view {
            background-color: #0dcaf0;
            color: #fff;
        }

        .btn-view:hover {
            background-color: #0baccc;
            color: #fff;
        }

        /* Alert styling */
        .alert {
            border-radius: 6px;
            padding: 12px 16px;
            margin-bottom: 24px;
        }

        /* Badge styling */
        .badge {
            padding: 5px 10px;
            font-weight: 500;
            border-radius: 30px;
        }

        /* Grid spacing */
        .mb-4 {
            margin-bottom: 24px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        /* Select2 customization */
        .select2-container--default .select2-selection--single,
        .select2-container--bootstrap-5 .select2-selection {
            height: 42px;
            padding: 6px 12px;
            border: 2px solid #ced4da;
            border-radius: 6px;
            background-color: #f8f9fa;
            cursor: pointer;
        }

        .select2-container--default .select2-selection--single:hover,
        .select2-container--bootstrap-5 .select2-selection:hover {
            border-color: #80bdff;
            background-color: #e9ecef;
        }

        .select2-container--default .select2-selection--single .select2-selection__arrow,
        .select2-container--bootstrap-5 .select2-selection .select2-selection__arrow {
            height: 42px;
            right: 8px;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered,
        .select2-container--bootstrap-5 .select2-selection .select2-selection__rendered {
            line-height: 28px;
            color: #495057;
        }

        /* Make labels for select2 fields look clickable */
        label[for="complainant_id"],
        label[for="respondent_id"] {
            cursor: pointer;
            color: #0d6efd;
            text-decoration: underline;
            display: inline-block;
            margin-bottom: 8px;
        }

        label[for="complainant_id"]:hover,
        label[for="respondent_id"]:hover {
            color: #0a58ca;
        }

        /* Add a visual cue to indicate the field is clickable */
        #residentComplainantFields::before,
        #residentRespondentFields::before {
            content: "👉 Click on the field or label below to select";
            display: block;
            margin-bottom: 8px;
            color: #0d6efd;
            font-size: 0.9rem;
        }

        /* Required field indicator */
        .required-indicator {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>

            <!-- Main Content -->
            <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
                <div class="content-container">
                    <div class="page-header">
                        <h1>✏️ Edit Complaint #<?php echo $complaint_id; ?></h1>
                        <div class="actions">
                            <a href="view_complaint.php?id=<?php echo $complaint_id; ?>" class="btn-view">
                                👁️ View Details
                            </a>
                            <a href="complaints.php" class="btn-back">
                                ⬅️ Back to List
                            </a>
                        </div>
                    </div>

                    <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success" role="alert">
                        ✅ <?php echo $success_message; ?>
                    </div>
                    <?php endif; ?>

                    <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger" role="alert">
                        ❌ <?php echo $error_message; ?>
                    </div>
                    <?php endif; ?>

                    <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]) . '?id=' . $complaint_id; ?>" method="POST" class="needs-validation" novalidate>

                        <!-- Complainant Information -->
                        <div class="section-card section-info shadow">
                            <div class="section-header">
                                <span class="icon">👤</span>
                                <span>Complainant Information</span>
                            </div>
                            <div class="section-body">
                                <div class="form-group">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="isResidentComplainant" name="is_resident_complainant" <?php echo !empty($complaint_data['complainant_id']) ? 'checked' : ''; ?> onchange="toggleComplainantFields()">
                                        <label class="form-check-label" for="isResidentComplainant">
                                            Complainant is a registered resident
                                        </label>
                                    </div>
                                </div>

                                <div id="residentComplainantFields" <?php echo empty($complaint_data['complainant_id']) ? 'style="display: none;"' : ''; ?>>
                                    <div class="form-group">
                                        <label for="complainant_id" class="form-label">📋 Click here to select a complainant</label>
                                        <select class="form-select select2" id="complainant_id" name="complainant_id">
                                            <option value="">Select Resident</option>
                                            <?php foreach ($residents as $resident): ?>
                                            <option value="<?php echo $resident['resident_id']; ?>" <?php echo ($complaint_data['complainant_id'] == $resident['resident_id']) ? 'selected' : ''; ?>>
                                                <?php echo $resident['last_name'] . ', ' . $resident['first_name'] . ' ' . $resident['middle_name']; ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>

                                <div id="nonResidentComplainantFields" <?php echo !empty($complaint_data['complainant_id']) ? 'style="display: none;"' : ''; ?>>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="complainant_name" class="form-label">Complainant Name</label>
                                                <input type="text" class="form-control" id="complainant_name" name="complainant_name" value="<?php echo htmlspecialchars($complaint_data['complainant_name'] ?? ''); ?>">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="complainant_contact" class="form-label">📞 Contact Number</label>
                                                <input type="text" class="form-control" id="complainant_contact" name="complainant_contact" value="<?php echo htmlspecialchars($complaint_data['complainant_contact'] ?? ''); ?>">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="complainant_address" class="form-label">📍 Address</label>
                                        <textarea class="form-control" id="complainant_address" name="complainant_address" rows="2"><?php echo htmlspecialchars($complaint_data['complainant_address'] ?? ''); ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Respondent Information -->
                        <div class="section-card section-warning shadow">
                            <div class="section-header">
                                <span class="icon">👥</span>
                                <span>Respondent Information</span>
                            </div>
                            <div class="section-body">
                                <div class="form-group">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="isResidentRespondent" name="is_resident_respondent" <?php echo !empty($complaint_data['respondent_id']) ? 'checked' : ''; ?> onchange="toggleRespondentFields()">
                                        <label class="form-check-label" for="isResidentRespondent">
                                            Respondent is a registered resident
                                        </label>
                                    </div>
                                </div>

                                <div id="residentRespondentFields" <?php echo empty($complaint_data['respondent_id']) ? 'style="display: none;"' : ''; ?>>
                                    <div class="form-group">
                                        <label for="respondent_id" class="form-label">📋 Click here to select a respondent</label>
                                        <select class="form-select select2" id="respondent_id" name="respondent_id">
                                            <option value="">Select Resident</option>
                                            <?php foreach ($residents as $resident): ?>
                                            <option value="<?php echo $resident['resident_id']; ?>" <?php echo ($complaint_data['respondent_id'] == $resident['resident_id']) ? 'selected' : ''; ?>>
                                                <?php echo $resident['last_name'] . ', ' . $resident['first_name'] . ' ' . $resident['middle_name']; ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>

                                <div id="nonResidentRespondentFields" <?php echo !empty($complaint_data['respondent_id']) ? 'style="display: none;"' : ''; ?>>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="respondent_name" class="form-label">Respondent Name</label>
                                                <input type="text" class="form-control" id="respondent_name" name="respondent_name" value="<?php echo htmlspecialchars($complaint_data['respondent_name'] ?? ''); ?>">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="respondent_contact" class="form-label">📞 Contact Number</label>
                                                <input type="text" class="form-control" id="respondent_contact" name="respondent_contact" value="<?php echo htmlspecialchars($complaint_data['respondent_contact'] ?? ''); ?>">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label for="respondent_address" class="form-label">📍 Address</label>
                                        <textarea class="form-control" id="respondent_address" name="respondent_address" rows="2"><?php echo htmlspecialchars($complaint_data['respondent_address'] ?? ''); ?></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Complaint Details -->
                        <div class="section-card section-primary shadow">
                            <div class="section-header">
                                <span class="icon">📝</span>
                                <span>Complaint Details</span>
                            </div>
                            <div class="section-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="complaint_type" class="form-label">🔖 Complaint Type <span class="required-indicator">*</span></label>
                                            <select class="form-select" id="complaint_type" name="complaint_type" required>
                                                <option value="">Select Complaint Type</option>
                                                <option value="Dispute" <?php echo ($complaint_data['complaint_type'] == 'Dispute') ? 'selected' : ''; ?>>Dispute</option>
                                                <option value="Nuisance" <?php echo ($complaint_data['complaint_type'] == 'Nuisance') ? 'selected' : ''; ?>>Nuisance</option>
                                                <option value="Theft" <?php echo ($complaint_data['complaint_type'] == 'Theft') ? 'selected' : ''; ?>>Theft</option>
                                                <option value="Assault" <?php echo ($complaint_data['complaint_type'] == 'Assault') ? 'selected' : ''; ?>>Assault</option>
                                                <option value="Property Damage" <?php echo ($complaint_data['complaint_type'] == 'Property Damage') ? 'selected' : ''; ?>>Property Damage</option>
                                                <option value="Harassment" <?php echo ($complaint_data['complaint_type'] == 'Harassment') ? 'selected' : ''; ?>>Harassment</option>
                                                <option value="Noise Complaint" <?php echo ($complaint_data['complaint_type'] == 'Noise Complaint') ? 'selected' : ''; ?>>Noise Complaint</option>
                                                <option value="Domestic Violence" <?php echo ($complaint_data['complaint_type'] == 'Domestic Violence') ? 'selected' : ''; ?>>Domestic Violence</option>
                                                <option value="Other" <?php echo ($complaint_data['complaint_type'] == 'Other') ? 'selected' : ''; ?>>Other</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="incident_date" class="form-label">📅 Incident Date <span class="required-indicator">*</span></label>
                                            <input type="date" class="form-control" id="incident_date" name="incident_date" required value="<?php echo $complaint_data['incident_date']; ?>" max="<?php echo date('Y-m-d'); ?>">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="incident_location" class="form-label">📍 Incident Location <span class="required-indicator">*</span></label>
                                    <input type="text" class="form-control" id="incident_location" name="incident_location" required value="<?php echo htmlspecialchars($complaint_data['incident_location']); ?>">
                                </div>

                                <div class="form-group">
                                    <label for="complaint_details" class="form-label">📄 Complaint Details <span class="required-indicator">*</span></label>
                                    <textarea class="form-control" id="complaint_details" name="complaint_details" rows="5" required><?php echo htmlspecialchars($complaint_data['complaint_details']); ?></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Case Management -->
                        <div class="section-card section-success shadow">
                            <div class="section-header">
                                <span class="icon">⚖️</span>
                                <span>Case Management</span>
                            </div>
                            <div class="section-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="status" class="form-label">📊 Status</label>
                                            <select class="form-select" id="status" name="status">
                                                <option value="Pending" <?php echo ($complaint_data['status'] == 'Pending') ? 'selected' : ''; ?>>⏳ Pending</option>
                                                <option value="Under Investigation" <?php echo ($complaint_data['status'] == 'Under Investigation') ? 'selected' : ''; ?>>🔍 Under Investigation</option>
                                                <option value="Resolved" <?php echo ($complaint_data['status'] == 'Resolved') ? 'selected' : ''; ?>>✅ Resolved</option>
                                                <option value="Dismissed" <?php echo ($complaint_data['status'] == 'Dismissed') ? 'selected' : ''; ?>>❌ Dismissed</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="assigned_official_id" class="form-label">👮 Assign To</label>
                                            <select class="form-select" id="assigned_official_id" name="assigned_official_id">
                                                <option value="">Unassigned</option>
                                                <?php foreach ($officials as $official): ?>
                                                <option value="<?php echo $official['official_id']; ?>" <?php echo ($complaint_data['assigned_official_id'] == $official['official_id']) ? 'selected' : ''; ?>>
                                                    <?php echo $official['name']; ?>
                                                </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end mt-4">
                            <a href="complaints.php" class="btn btn-secondary me-2">
                                ❌ Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                💾 Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <script>
        // Initialize Select2
        $(document).ready(function() {
            $('.select2').select2({
                theme: 'bootstrap-5',
                width: '100%',
                dropdownParent: $('body'),
                placeholder: 'Select a resident',
                allowClear: true
            });

            // Make sure the select2 containers are properly styled
            $('.select2-container').css('width', '100%');

            // Add click handler to the labels to open the dropdowns
            $('label[for="complainant_id"]').click(function() {
                $('#complainant_id').select2('open');
            });

            $('label[for="respondent_id"]').click(function() {
                $('#respondent_id').select2('open');
            });
        });

        // Toggle complainant fields
        function toggleComplainantFields() {
            const isResident = document.getElementById('isResidentComplainant').checked;
            const residentFields = document.getElementById('residentComplainantFields');
            const nonResidentFields = document.getElementById('nonResidentComplainantFields');

            if (isResident) {
                residentFields.style.display = 'block';
                nonResidentFields.style.display = 'none';
                document.getElementById('complainant_name').removeAttribute('required');
                document.getElementById('complainant_address').removeAttribute('required');
            } else {
                residentFields.style.display = 'none';
                nonResidentFields.style.display = 'block';
                document.getElementById('complainant_name').setAttribute('required', 'required');
                document.getElementById('complainant_address').setAttribute('required', 'required');
                document.getElementById('complainant_id').value = '';
            }
        }

        // Toggle respondent fields
        function toggleRespondentFields() {
            const isResident = document.getElementById('isResidentRespondent').checked;
            const residentFields = document.getElementById('residentRespondentFields');
            const nonResidentFields = document.getElementById('nonResidentRespondentFields');

            if (isResident) {
                residentFields.style.display = 'block';
                nonResidentFields.style.display = 'none';
                document.getElementById('respondent_name').removeAttribute('required');
                document.getElementById('respondent_address').removeAttribute('required');
            } else {
                residentFields.style.display = 'none';
                nonResidentFields.style.display = 'block';
                document.getElementById('respondent_name').setAttribute('required', 'required');
                document.getElementById('respondent_address').setAttribute('required', 'required');
                document.getElementById('respondent_id').value = '';
            }
        }

        // Form validation
        (function() {
            'use strict';

            // Fetch all forms we want to apply validation to
            var forms = document.querySelectorAll('.needs-validation');

            // Loop over them and prevent submission
            Array.prototype.slice.call(forms).forEach(function(form) {
                form.addEventListener('submit', function(event) {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }

                    form.classList.add('was-validated');
                }, false);
            });
        })();
    </script>
</body>
</html>