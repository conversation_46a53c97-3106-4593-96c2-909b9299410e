<?php
// Include configuration file
require_once __DIR__ . '/config/config.php';

// Include permission functions for role-based sidebar visibility
require_once __DIR__ . '/functions/permission_functions.php';

// Get current page for highlighting active link
$current_page = basename($_SERVER['PHP_SELF']);

// Get unread notifications count
$unread_notifications = 0;
if (isset($_SESSION['user_id']) && !isset($db_connection_error)) {
    try {
        $user_id = $_SESSION['user_id'];
        $query = "SELECT COUNT(*) as count FROM notifications WHERE user_id = :user_id AND is_read = 0";
        $stmt = $conn->prepare($query);
        $stmt->bindParam(':user_id', $user_id, PDO::PARAM_INT);
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $unread_notifications = $result['count'] ?? 0;
    } catch (PDOException $e) {
        error_log("Error fetching notifications: " . $e->getMessage());
        $unread_notifications = 0;
    }
}

// Fetch barangay settings if not in session or if accessing settings page
$should_refresh_settings = (basename($_SERVER['PHP_SELF']) == 'settings.php' || !isset($_SESSION['barangay_name']));

if ($should_refresh_settings && !isset($db_connection_error)) {
    try {
        $query = "SELECT setting_name, setting_value FROM system_settings WHERE setting_name IN ('barangay_name', 'municipality', 'province', 'logo', 'welcome_name')";
        $stmt = $conn->prepare($query);
        $stmt->execute();

        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $_SESSION[$row['setting_name']] = $row['setting_value'];
        }
    } catch (PDOException $e) {
        error_log("Error fetching barangay settings: " . $e->getMessage());
    }
}

// Set default barangay name if not found
$barangay_name = $_SESSION['barangay_name'] ?? 'Barangay Management System';
// Set logo path
$logo_path = isset($_SESSION['logo']) && !empty($_SESSION['logo'])
    ? get_absolute_path('assets/img/logos/' . $_SESSION['logo']) . (isset($_SESSION['logo_timestamp']) ? '?v=' . $_SESSION['logo_timestamp'] : '')
    : get_absolute_path('assets/images/IMG_0039-removebg-preview.png');

// Function to check if current page is in a specific module
function isActiveModule($module) {
    $current_dir = dirname($_SERVER['PHP_SELF']);
    return strpos($current_dir, $module) !== false;
}

// Function to check if a dropdown should be expanded
function isDropdownExpanded($links, $current_page) {
    foreach ($links as $link) {
        if ($current_page == $link) {
            return true;
        }
    }
    return false;
}

// Define module pages arrays for dropdown control
$userManagementPages = ['officials.php', 'staff.php', 'users.php'];
$residentManagementPages = ['residents.php', 'households.php', 'family_relationships.php', 'migration_records.php'];
$documentPages = ['documents.php', 'clearance.php', 'certificate.php', 'residency.php', 'indigency.php', 'good_standing.php', 'no_pending_case.php', 'demolition.php', 'solo_parents.php', 'toda_certificate.php'];
$complaintPages = ['complaints.php', 'hearings.php', 'resolutions.php'];
$bailPages = ['dashboard.php', 'blotter.php', 'bail_assessment.php', 'bail_payment.php', 'release_order.php', 'hearings.php', 'reports.php'];
$socialPages = ['seniors.php', 'pwd.php', 'scholarships.php', 'financial.php', 'medical.php', 'relief.php', 'programs.php'];
$financePages = ['budget.php', 'taxation.php', 'payments.php'];
$infrastructurePages = ['properties.php', 'projects.php', 'maintenance.php'];
$healthPages = ['health_records.php', 'vaccinations.php', 'disease_monitoring.php'];
$disasterPages = ['disaster_preparedness.php', 'evacuation.php', 'emergency_contacts.php'];
$governancePages = ['elections.php', 'ordinances.php', 'meetings.php', 'executive_orders.php'];
$communicationPages = ['announcements.php', 'sms.php', 'email.php', 'coordination.php'];
$reportsPages = ['population.php', 'financial_reports.php', 'document_reports.php', 'case_reports.php', 'project_reports.php', 'export.php'];
$adminPages = ['settings.php', 'template_manager.php', 'template_placeholder_guide.php', 'create_sample_template.php', 'simple_template_test.php', 'test_placeholder_replacement.php', 'test_template_with_placeholders.php', 'debug_template_generation.php', 'test_template_flow.php', 'logs.php', 'backup.php', 'backup_settings.php', 'permissions.php', 'qr_management.php'];

// Store sidebar script for later output (after headers)
$sidebarScript = "<script>
    // Get stored sidebar position from sessionStorage when page loads
    document.addEventListener('DOMContentLoaded', function() {
        var storedPosition = sessionStorage.getItem('sidebarScrollPosition');
        if (storedPosition) {
            document.querySelector('.sidebar-sticky').scrollTop = parseInt(storedPosition);
        }

        // Check if we have a recently uploaded logo stored
        let lastUploadedLogo = localStorage.getItem('lastUploadedLogo');
        let logoUpdateTime = localStorage.getItem('logoUpdateTime');

        // Only use the stored logo if it was updated recently (within the last minute)
        if (lastUploadedLogo && logoUpdateTime) {
            const currentTime = new Date().getTime();
            const timeDiff = currentTime - logoUpdateTime;

            // If logo was updated within the last minute, use it
            if (timeDiff < 60000) {
                document.getElementById('sidebarLogo').src = lastUploadedLogo;

                // Clear the stored logo after using it
                setTimeout(function() {
                    localStorage.removeItem('lastUploadedLogo');
                    localStorage.removeItem('logoUpdateTime');
                }, 5000);
            } else {
                // Clear old data
                localStorage.removeItem('lastUploadedLogo');
                localStorage.removeItem('logoUpdateTime');
            }
        }
    });

    // Store position when user scrolls
    function storeSidebarPosition() {
        var scrollPosition = document.querySelector('.sidebar-sticky').scrollTop;
        sessionStorage.setItem('sidebarScrollPosition', scrollPosition);
    }
</script>";
?>

<style>
    /* Custom styles to fix sidebar height */
    .sidebar {
        min-height: 100%;
        height: auto !important;
        overflow: hidden;
    }
    .sidebar-sticky {
        position: relative;
        height: auto !important;
        max-height: calc(100vh - 70px);
        overflow-y: auto;
    }
    /* Add scroll padding to prevent content from being cut off at the bottom */
    .sidebar-sticky {
        padding-bottom: 20px;
    }
    /* Hide overflow for main container */
    .wrapper {
        overflow-x: hidden;
    }
    /* Rotate dropdown arrow when expanded */
    .nav-link[aria-expanded="true"] .fa-angle-down {
        transform: rotate(180deg);
        transition: transform 0.3s ease;
    }
    .nav-link .fa-angle-down {
        transition: transform 0.3s ease;
    }
</style>

<?php echo $sidebarScript; ?>

<div class="col-md-4 col-lg-2 d-md-block bg-light sidebar collapse">
    <div class="sidebar-sticky pt-3" onscroll="storeSidebarPosition()">
        <div class="text-center mb-4">
            <img src="<?php echo $logo_path; ?>" alt="Barangay Logo" class="img-fluid rounded-circle mb-3" style="max-width: 120px;" id="sidebarLogo">
            <h5><?php echo htmlspecialchars($barangay_name); ?></h5>
            <p class="small"><?php echo $_SESSION['user_type']; ?> Panel</p>
            <hr>
        </div>

        <ul class="nav flex-column">
            <li class="nav-item">
                <a class="nav-link <?php echo ($current_page == 'index.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('index.php'); ?>">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    Dashboard
                </a>
            </li>

            <!-- User Management Section -->
            <?php if (canAccessModule('officials', $conn) || canAccessModule('users', $conn)): ?>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="collapse" href="#userManagementCollapse" role="button" aria-expanded="<?php echo isDropdownExpanded($userManagementPages, $current_page) ? 'true' : 'false'; ?>">
                    <i class="fas fa-users-cog me-2"></i>
                    User Management
                    <i class="fas fa-angle-down float-end"></i>
                </a>
                <div class="collapse <?php echo isDropdownExpanded($userManagementPages, $current_page) ? 'show' : ''; ?>" id="userManagementCollapse">
                    <ul class="nav flex-column ps-3">
                        <?php if (canAccessModule('officials', $conn)): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'officials.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/officials/officials.php'); ?>">
                                <i class="fas fa-user-tie me-2"></i>
                                Barangay Officials
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (canAccessModule('officials', $conn)): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'staff.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/officials/staff.php'); ?>">
                                <i class="fas fa-users-cog me-2"></i>
                                Staff Management
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if ($_SESSION['user_type'] == 'Admin'): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'users.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('admin/users.php'); ?>">
                                <i class="fas fa-user-cog me-2"></i>
                                System Users
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </li>
            <?php endif; ?>

            <!-- Resident Management Section -->
            <?php if (canAccessModule('residents', $conn) || canAccessModule('households', $conn)): ?>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="collapse" href="#residentManagementCollapse" role="button" aria-expanded="<?php echo isDropdownExpanded($residentManagementPages, $current_page) ? 'true' : 'false'; ?>">
                    <i class="fas fa-house-user me-2"></i>
                    Resident Management
                    <i class="fas fa-angle-down float-end"></i>
                </a>
                <div class="collapse <?php echo isDropdownExpanded($residentManagementPages, $current_page) ? 'show' : ''; ?>" id="residentManagementCollapse">
                    <ul class="nav flex-column ps-3">
                        <?php if (canAccessModule('residents', $conn)): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'residents.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/residents/residents.php'); ?>">
                                <i class="fas fa-users me-2"></i>
                                Residents
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (canAccessModule('households', $conn)): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'households.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/residents/households.php'); ?>">
                                <i class="fas fa-home me-2"></i>
                                Households
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (canAccessModule('residents', $conn)): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'family_relationships.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/residents/family_relationships.php'); ?>">
                                <i class="fas fa-people-roof me-2"></i>
                                Family Relationships
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (canAccessModule('residents', $conn)): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'migration_records.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/residents/migration_records.php'); ?>">
                                <i class="fas fa-exchange-alt me-2"></i>
                                Migration Records
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </div>
            </li>
            <?php endif; ?>

            <!-- Document Section -->
            <?php if (canAccessModule('documents', $conn)): ?>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="collapse" href="#documentCollapse" role="button" aria-expanded="<?php echo isDropdownExpanded($documentPages, $current_page) ? 'true' : 'false'; ?>">
                    <i class="fas fa-file-alt me-2"></i>
                    Document Services
                    <i class="fas fa-angle-down float-end"></i>
                </a>
                <div class="collapse <?php echo isDropdownExpanded($documentPages, $current_page) ? 'show' : ''; ?>" id="documentCollapse">
                    <ul class="nav flex-column ps-3">
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'documents.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/documents/documents.php'); ?>">
                                <i class="fas fa-clipboard-list me-2"></i>
                                Document Requests
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'clearance.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/documents/clearance.php'); ?>">
                                <i class="fas fa-certificate me-2"></i>
                                Barangay Clearance
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'certificate.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/documents/certificate.php'); ?>">
                                <i class="fas fa-file-alt me-2"></i>
                                Barangay Certificate
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'residency.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/documents/residency.php'); ?>">
                                <i class="fas fa-id-card me-2"></i>
                                Certificate of Residency
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'indigency.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/documents/indigency.php'); ?>">
                                <i class="fas fa-hand-holding-heart me-2"></i>
                                Certificate of Indigency
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'good_standing.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/documents/good_standing.php'); ?>">
                                <i class="fas fa-medal me-2"></i>
                                Certificate of Good Standing
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'no_pending_case.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/documents/no_pending_case.php'); ?>">
                                <i class="fas fa-gavel me-2"></i>
                                Certificate of No Pending Case
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'demolition.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/documents/demolition.php'); ?>">
                                <i class="fas fa-hammer me-2"></i>
                                Certificate of Demolition
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'solo_parents.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/documents/solo_parents.php'); ?>">
                                <i class="fas fa-user-friends me-2"></i>
                                Solo Parents
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'toda_certificate.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/documents/toda_certificate.php'); ?>">
                                <i class="fas fa-car me-2"></i>
                                TODA Certificate
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'business_permit.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/documents/business_permit.php'); ?>">
                                <i class="fas fa-store me-2"></i>
                                Business Permit
                            </a>
                        </li>

                    </ul>
                </div>
            </li>
            <?php endif; ?>

            <!-- Complaints and Blotter Section -->
            <?php if (canAccessModule('complaints', $conn)): ?>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="collapse" href="#complaintCollapse" role="button" aria-expanded="<?php echo isDropdownExpanded($complaintPages, $current_page) ? 'true' : 'false'; ?>">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    Complaints & Blotter
                    <i class="fas fa-angle-down float-end"></i>
                </a>
                <div class="collapse <?php echo isDropdownExpanded($complaintPages, $current_page) ? 'show' : ''; ?>" id="complaintCollapse">
                    <ul class="nav flex-column ps-3">
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'complaints.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/complaints/complaints.php'); ?>">
                                <i class="fas fa-file-signature me-2"></i>
                    Blotter/Complaints
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'hearings.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/complaints/hearings.php'); ?>">
                                <i class="fas fa-gavel me-2"></i>
                                Hearings
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'resolutions.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/complaints/resolutions.php'); ?>">
                                <i class="fas fa-file-contract me-2"></i>
                                Case Resolutions
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            <?php endif; ?>

            <!-- Bail Management Section -->
            <?php if (canAccessModule('bail', $conn)): ?>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="collapse" href="#bailCollapse" role="button" aria-expanded="<?php echo isDropdownExpanded($bailPages, $current_page) ? 'true' : 'false'; ?>">
                    <i class="fas fa-gavel me-2"></i>
                    Bail Management
                    <i class="fas fa-angle-down float-end"></i>
                </a>
                <div class="collapse <?php echo isDropdownExpanded($bailPages, $current_page) ? 'show' : ''; ?>" id="bailCollapse">
                    <ul class="nav flex-column ps-3">
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'dashboard.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/bail/dashboard.php'); ?>">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                Bail Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'blotter.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/bail/blotter.php'); ?>">
                                <i class="fas fa-clipboard me-2"></i>
                                Bail Cases
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'bail_assessment.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/bail/bail_assessment.php'); ?>">
                                <i class="fas fa-balance-scale me-2"></i>
                                Bail Assessments
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'bail_payment.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/bail/bail_payment.php'); ?>">
                                <i class="fas fa-money-bill-wave me-2"></i>
                                Bail Payments
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'release_order.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/bail/release_order.php'); ?>">
                                <i class="fas fa-file-contract me-2"></i>
                                Release Orders
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'hearings.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/bail/hearings.php'); ?>">
                                <i class="fas fa-calendar-alt me-2"></i>
                                Court Hearings
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'reports.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/bail/reports.php'); ?>">
                                <i class="fas fa-file-alt me-2"></i>
                                Reports
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            <?php endif; ?>

            <!-- Social Services Section -->
            <?php if (canAccessModule('social', $conn)): ?>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="collapse" href="#socialServicesCollapse" role="button" aria-expanded="<?php echo isDropdownExpanded($socialPages, $current_page) ? 'true' : 'false'; ?>">
                    <i class="fas fa-hands-helping me-2"></i>
                    Social Services
                    <i class="fas fa-angle-down float-end"></i>
                </a>
                <div class="collapse <?php echo isDropdownExpanded($socialPages, $current_page) ? 'show' : ''; ?>" id="socialServicesCollapse">
                    <ul class="nav flex-column ps-3">
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'seniors.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/social/seniors.php'); ?>">
                                <i class="fas fa-user-plus me-2"></i>
                                Senior Citizens
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'pwd.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/social/pwd.php'); ?>">
                                <i class="fas fa-wheelchair me-2"></i>
                                PWD Records
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'scholarships.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/social/scholarships.php'); ?>">
                                <i class="fas fa-graduation-cap me-2"></i>
                                Scholarships
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'financial.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/social/financial.php'); ?>">
                                <i class="fas fa-hand-holding-usd me-2"></i>
                                Financial Assistance
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'medical.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/social/medical.php'); ?>">
                                <i class="fas fa-briefcase-medical me-2"></i>
                                Medical Assistance
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'relief.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/social/relief.php'); ?>">
                                <i class="fas fa-box-open me-2"></i>
                                Relief Distribution
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'programs.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/assistance/programs.php'); ?>">
                                <i class="fas fa-hands-helping me-2"></i>
                                Assistance Programs
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            <?php endif; ?>

            <!-- Financial Management -->
            <?php if (canAccessModule('finance', $conn)): ?>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="collapse" href="#financialCollapse" role="button" aria-expanded="<?php echo isDropdownExpanded($financePages, $current_page) ? 'true' : 'false'; ?>">
                    <i class="fas fa-money-bill-wave me-2"></i>
                    Financial Management
                    <i class="fas fa-angle-down float-end"></i>
                </a>
                <div class="collapse <?php echo isDropdownExpanded($financePages, $current_page) ? 'show' : ''; ?>" id="financialCollapse">
                    <ul class="nav flex-column ps-3">
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'budget.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/finance/budget.php'); ?>">
                                <i class="fas fa-coins me-2"></i>
                                Budget & Expenses
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'taxation.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/finance/taxation.php'); ?>">
                                <i class="fas fa-receipt me-2"></i>
                                Taxation & Fees
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'payments.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/finance/payments.php'); ?>">
                                <i class="fas fa-credit-card me-2"></i>
                                Payment Records
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            <?php endif; ?>

            <!-- Infrastructure Management -->
            <?php if (canAccessModule('properties', $conn)): ?>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="collapse" href="#infrastructureCollapse" role="button" aria-expanded="<?php echo isDropdownExpanded($infrastructurePages, $current_page) ? 'true' : 'false'; ?>">
                    <i class="fas fa-building me-2"></i>
                    Infrastructure
                    <i class="fas fa-angle-down float-end"></i>
                </a>
                <div class="collapse <?php echo isDropdownExpanded($infrastructurePages, $current_page) ? 'show' : ''; ?>" id="infrastructureCollapse">
                    <ul class="nav flex-column ps-3">
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'properties.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/properties/properties.php'); ?>">
                                <i class="fas fa-clipboard-list me-2"></i>
                                Properties & Assets
                            </a>
                        </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($current_page == 'projects.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/projects/projects.php'); ?>">
                    <i class="fas fa-project-diagram me-2"></i>
                    Projects
                </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'maintenance.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/properties/maintenance.php'); ?>">
                                <i class="fas fa-tools me-2"></i>
                                Maintenance Records
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            <?php endif; ?>

            <!-- Health Services -->
            <?php if (canAccessModule('health', $conn)): ?>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="collapse" href="#healthCollapse" role="button" aria-expanded="<?php echo isDropdownExpanded($healthPages, $current_page) ? 'true' : 'false'; ?>">
                    <i class="fas fa-heartbeat me-2"></i>
                    Health Services
                    <i class="fas fa-angle-down float-end"></i>
                </a>
                <div class="collapse <?php echo isDropdownExpanded($healthPages, $current_page) ? 'show' : ''; ?>" id="healthCollapse">
                    <ul class="nav flex-column ps-3">
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'health_records.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/health/health_records.php'); ?>">
                                <i class="fas fa-notes-medical me-2"></i>
                                Health Records
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'vaccinations.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/health/vaccinations.php'); ?>">
                                <i class="fas fa-syringe me-2"></i>
                                Vaccination
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'disease_monitoring.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/health/disease_monitoring.php'); ?>">
                                <i class="fas fa-virus me-2"></i>
                                Disease Monitoring
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            <?php endif; ?>

            <!-- Disaster Management -->
            <?php if (canAccessModule('disaster', $conn)): ?>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="collapse" href="#disasterCollapse" role="button" aria-expanded="<?php echo isDropdownExpanded($disasterPages, $current_page) ? 'true' : 'false'; ?>">
                    <i class="fas fa-house-damage me-2"></i>
                    Disaster Management
                    <i class="fas fa-angle-down float-end"></i>
                </a>
                <div class="collapse <?php echo isDropdownExpanded($disasterPages, $current_page) ? 'show' : ''; ?>" id="disasterCollapse">
                    <ul class="nav flex-column ps-3">
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'disaster_preparedness.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/disaster/disaster_preparedness.php'); ?>">
                                <i class="fas fa-shield-alt me-2"></i>
                                Preparedness
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'evacuation.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/disaster/evacuation.php'); ?>">
                                <i class="fas fa-running me-2"></i>
                                Evacuation Centers
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'emergency_contacts.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/disaster/emergency_contacts.php'); ?>">
                                <i class="fas fa-phone-alt me-2"></i>
                                Emergency Contacts
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            <?php endif; ?>

            <!-- Governance -->
            <?php if (canAccessModule('governance', $conn)): ?>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="collapse" href="#governanceCollapse" role="button" aria-expanded="<?php echo isDropdownExpanded($governancePages, $current_page) ? 'true' : 'false'; ?>">
                    <i class="fas fa-balance-scale me-2"></i>
                    Governance
                    <i class="fas fa-angle-down float-end"></i>
                </a>
                <div class="collapse <?php echo isDropdownExpanded($governancePages, $current_page) ? 'show' : ''; ?>" id="governanceCollapse">
                    <ul class="nav flex-column ps-3">
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'elections.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/governance/elections.php'); ?>">
                                <i class="fas fa-vote-yea me-2"></i>
                                Elections
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'ordinances.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/governance/ordinances.php'); ?>">
                                <i class="fas fa-scroll me-2"></i>
                                Ordinances
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'meetings.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/governance/meetings.php'); ?>">
                                <i class="fas fa-users me-2"></i>
                                Meetings
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'executive_orders.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/governance/executive_orders.php'); ?>">
                                <i class="fas fa-gavel me-2"></i>
                                Executive Orders
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            <?php endif; ?>

            <!-- Communication -->
            <?php if (canAccessModule('communication', $conn)): ?>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="collapse" href="#communicationCollapse" role="button" aria-expanded="<?php echo isDropdownExpanded($communicationPages, $current_page) ? 'true' : 'false'; ?>">
                    <i class="fas fa-comment-alt me-2"></i>
                    Communication
                    <i class="fas fa-angle-down float-end"></i>
                </a>
                <div class="collapse <?php echo isDropdownExpanded($communicationPages, $current_page) ? 'show' : ''; ?>" id="communicationCollapse">
                    <ul class="nav flex-column ps-3">
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'announcements.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/announcements/announcements.php'); ?>">
                                <i class="fas fa-bullhorn me-2"></i>
                                Announcements
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'sms.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/announcements/sms.php'); ?>">
                                <i class="fas fa-sms me-2"></i>
                                SMS Notifications
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'email.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/announcements/email.php'); ?>">
                                <i class="fas fa-envelope me-2"></i>
                                Email Notifications
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'coordination.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/announcements/coordination.php'); ?>">
                                <i class="fas fa-handshake me-2"></i>
                                Barangay Coordination
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            <?php endif; ?>

            <!-- Reports Section -->
            <?php if (canAccessModule('reports', $conn)): ?>
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="collapse" href="#reportsCollapse" role="button" aria-expanded="<?php echo isDropdownExpanded($reportsPages, $current_page) ? 'true' : 'false'; ?>">
                    <i class="fas fa-chart-bar me-2"></i>
                    Reports & Analytics
                    <i class="fas fa-angle-down float-end"></i>
                </a>
                <div class="collapse <?php echo isDropdownExpanded($reportsPages, $current_page) ? 'show' : ''; ?>" id="reportsCollapse">
                    <ul class="nav flex-column ps-3">
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'population.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/reports/population.php'); ?>">
                                <i class="fas fa-users me-2"></i>
                                Demographics
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'financial_reports.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/reports/financial_reports.php'); ?>">
                                <i class="fas fa-file-invoice-dollar me-2"></i>
                                Financial Reports
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'document_reports.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/reports/document_reports.php'); ?>">
                                <i class="fas fa-file-alt me-2"></i>
                                Document Services
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'case_reports.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/reports/case_reports.php'); ?>">
                                <i class="fas fa-balance-scale me-2"></i>
                                Case Reports
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'project_reports.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/reports/project_reports.php'); ?>">
                                <i class="fas fa-tasks me-2"></i>
                                Project Reports
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'export.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('modules/reports/export.php'); ?>">
                                <i class="fas fa-file-export me-2"></i>
                                Export Data
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            <?php endif; ?>

            <?php if ($_SESSION['user_type'] == 'Admin'): ?>
            <!-- Admin Section -->
            <li class="nav-item">
                <a class="nav-link" data-bs-toggle="collapse" href="#adminCollapse" role="button" aria-expanded="<?php echo isDropdownExpanded($adminPages, $current_page) ? 'true' : 'false'; ?>">
                    <i class="fas fa-user-shield me-2"></i>
                    Administration
                    <i class="fas fa-angle-down float-end"></i>
                </a>
                <div class="collapse <?php echo isDropdownExpanded($adminPages, $current_page) ? 'show' : ''; ?>" id="adminCollapse">
                    <ul class="nav flex-column ps-3">
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'settings.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('admin/settings.php'); ?>">
                                <i class="fas fa-cogs me-2"></i>
                                System Settings
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'template_manager.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('admin/template_manager.php'); ?>">
                                <i class="fas fa-file-word me-2"></i>
                                Template Manager
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'template_placeholder_guide.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('admin/template_placeholder_guide.php'); ?>">
                                <i class="fas fa-code me-2"></i>
                                Template Guide
                            </a>
                        </li>
            <li class="nav-item">
                <a class="nav-link <?php echo ($current_page == 'logs.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('admin/logs.php'); ?>">
                    <i class="fas fa-history me-2"></i>
                    Activity Logs
                </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'backup.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('admin/backup.php'); ?>">
                                <i class="fas fa-database me-2"></i>
                                Database Backup
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'backup_settings.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('admin/backup_settings.php'); ?>">
                                <i class="fas fa-cog me-2"></i>
                                Backup Settings
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'permissions.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('admin/permissions.php'); ?>">
                                <i class="fas fa-key me-2"></i>
                                User Permissions
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo ($current_page == 'qr_management.php') ? 'active' : ''; ?>" href="<?php echo get_absolute_path('admin/qr_management.php'); ?>">
                                <i class="fas fa-qrcode me-2"></i>
                                QR Code Management
                            </a>
                        </li>
                    </ul>
                </div>
            </li>
            <?php endif; ?>
        </ul>

        <hr>
        <div class="nav-item">
            <a class="nav-link text-danger" href="/barangay/logout.php">
                <i class="fas fa-sign-out-alt me-2"></i>
                Logout
            </a>
        </div>
    </div>
</div>