<?php
session_start();
include '../../includes/config/database.php';
include '../../includes/functions/permission_functions.php';
include '../../includes/functions/utility.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check permission
if (!hasPermission('edit_household')) {
    header("Location: ../../index.php");
    exit;
}

// Get household ID from URL
$household_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($household_id <= 0) {
    header("Location: households.php");
    exit;
}

// Get household information
$query = "SELECT h.*, 
          CONCAT(r.last_name, ', ', r.first_name, ' ', COALESCE(r.middle_name, '')) as head_name,
          r.resident_id as head_id
          FROM households h
          LEFT JOIN residents r ON h.household_head_id = r.resident_id
          WHERE h.household_id = :household_id";
$stmt = $conn->prepare($query);
$stmt->bindParam(':household_id', $household_id, PDO::PARAM_INT);
$stmt->execute();

if ($stmt->rowCount() == 0) {
    // Household not found
    header("Location: households.php?error=not_found");
    exit;
}

$household = $stmt->fetch(PDO::FETCH_ASSOC);

// Get all active residents for household head dropdown
$residents_query = "SELECT r.resident_id, 
                  CONCAT(r.last_name, ', ', r.first_name, ' ', COALESCE(r.middle_name, '')) as full_name,
                  r.address, r.gender
             FROM residents r
             WHERE r.status = 'Active'
                  AND (r.resident_id = :current_head_id 
                       OR r.resident_id NOT IN (SELECT household_head_id FROM households WHERE household_head_id IS NOT NULL))
             ORDER BY r.last_name, r.first_name";
$residents_stmt = $conn->prepare($residents_query);
$residents_stmt->bindParam(':current_head_id', $household['household_head_id'], PDO::PARAM_INT);
$residents_stmt->execute();

$errors = [];
$success_message = '';

// Process form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Sanitize and validate input
    $household_code = htmlspecialchars(trim($_POST['household_code'] ?? ''));
    $household_head = isset($_POST['household_head']) ? (int)$_POST['household_head'] : 0;
    $address = htmlspecialchars(trim($_POST['address'] ?? ''));
    $status = htmlspecialchars(trim($_POST['status'] ?? 'Active'));
    $landmark = htmlspecialchars(trim($_POST['landmark'] ?? ''));
    $years_of_residency = isset($_POST['years_of_residency']) && $_POST['years_of_residency'] !== '' ? (int)$_POST['years_of_residency'] : null;
    $house_ownership = htmlspecialchars(trim($_POST['house_ownership'] ?? 'Owned'));
    $monthly_rent = isset($_POST['monthly_rent']) ? (float)$_POST['monthly_rent'] : 0;
    $has_electricity = isset($_POST['has_electricity']) ? 1 : 0;
    $has_water_supply = isset($_POST['has_water_supply']) ? 1 : 0;
    $has_internet = isset($_POST['has_internet']) ? 1 : 0;
    $economic_status = htmlspecialchars(trim($_POST['economic_status'] ?? ''));
    
    // Validate required fields
    if (empty($household_code)) $errors[] = "Household code is required";
    if (empty($household_head)) $errors[] = "Household head is required";
    if (empty($address)) $errors[] = "Address is required";
    
    // Check if household code already exists (and it's not this household's code)
    $check_query = "SELECT household_id FROM households WHERE household_code = :household_code AND household_id != :household_id";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bindParam(':household_code', $household_code, PDO::PARAM_STR);
    $check_stmt->bindParam(':household_id', $household_id, PDO::PARAM_INT);
    $check_stmt->execute();
    if ($check_stmt->rowCount() > 0) {
        $errors[] = "Household code already exists. Please use a different code.";
    }
    
    // Proceed if no errors
    if (empty($errors)) {
        // Begin transaction
        $conn->beginTransaction();
        
        try {
            // Update household information
            $update_query = "UPDATE households SET 
                household_code = :household_code,
                household_head_id = :household_head,
                address = :address,
                status = :status,
                landmark = :landmark,
                years_of_residency = :years_of_residency,
                house_ownership = :house_ownership,
                monthly_rent = :monthly_rent,
                has_electricity = :has_electricity,
                has_water_supply = :has_water_supply,
                has_internet = :has_internet,
                economic_status = :economic_status
                WHERE household_id = :household_id";
                
            $update_stmt = $conn->prepare($update_query);
            $update_stmt->bindParam(':household_code', $household_code, PDO::PARAM_STR);
            $update_stmt->bindParam(':household_head', $household_head, PDO::PARAM_INT);
            $update_stmt->bindParam(':address', $address, PDO::PARAM_STR);
            $update_stmt->bindParam(':status', $status, PDO::PARAM_STR);
            $update_stmt->bindParam(':landmark', $landmark, PDO::PARAM_STR);
            $update_stmt->bindParam(':years_of_residency', $years_of_residency, PDO::PARAM_INT);
            $update_stmt->bindParam(':house_ownership', $house_ownership, PDO::PARAM_STR);
            $update_stmt->bindParam(':monthly_rent', $monthly_rent, PDO::PARAM_STR);
            $update_stmt->bindParam(':has_electricity', $has_electricity, PDO::PARAM_INT);
            $update_stmt->bindParam(':has_water_supply', $has_water_supply, PDO::PARAM_INT);
            $update_stmt->bindParam(':has_internet', $has_internet, PDO::PARAM_INT);
            $update_stmt->bindParam(':economic_status', $economic_status, PDO::PARAM_STR);
            $update_stmt->bindParam(':household_id', $household_id, PDO::PARAM_INT);
            
            if (!$update_stmt->execute()) {
                throw new Exception("Error updating household");
            }
            
            // Check if household head changed
            if ($household_head != $household['household_head_id']) {
                // Update the household members table if head changed
                
                // First, check if the new head is already a member
                $check_member_query = "SELECT * FROM household_members 
                                     WHERE household_id = :household_id AND resident_id = :resident_id";
                $check_member_stmt = $conn->prepare($check_member_query);
                $check_member_stmt->bindParam(':household_id', $household_id, PDO::PARAM_INT);
                $check_member_stmt->bindParam(':resident_id', $household_head, PDO::PARAM_INT);
                $check_member_stmt->execute();
                
                if ($check_member_stmt->rowCount() > 0) {
                    // Update existing member to be head
                    $update_member_query = "UPDATE household_members 
                                         SET relation_to_head = 'Head' 
                                         WHERE household_id = :household_id AND resident_id = :resident_id";
                    $update_member_stmt = $conn->prepare($update_member_query);
                    $update_member_stmt->bindParam(':household_id', $household_id, PDO::PARAM_INT);
                    $update_member_stmt->bindParam(':resident_id', $household_head, PDO::PARAM_INT);
                    
                    if (!$update_member_stmt->execute()) {
                        throw new Exception("Error updating household head in members");
                    }
                } else {
                    // Add new head as a member
                    $insert_member_query = "INSERT INTO household_members (household_id, resident_id, relation_to_head) 
                                         VALUES (:household_id, :resident_id, 'Head')";
                    $insert_member_stmt = $conn->prepare($insert_member_query);
                    $insert_member_stmt->bindParam(':household_id', $household_id, PDO::PARAM_INT);
                    $insert_member_stmt->bindParam(':resident_id', $household_head, PDO::PARAM_INT);
                    
                    if (!$insert_member_stmt->execute()) {
                        throw new Exception("Error adding household head as member");
                    }
                }
                
                // Update the old head's relation if they are still a member
                if ($household['household_head_id']) {
                    $update_old_head_query = "UPDATE household_members 
                                            SET relation_to_head = 'Member' 
                                            WHERE household_id = :household_id AND resident_id = :resident_id";
                    $update_old_head_stmt = $conn->prepare($update_old_head_query);
                    $update_old_head_stmt->bindParam(':household_id', $household_id, PDO::PARAM_INT);
                    $update_old_head_stmt->bindParam(':resident_id', $household['household_head_id'], PDO::PARAM_INT);
                    $update_old_head_stmt->execute();
                }
            }
            
            // Log activity
            if (function_exists('logActivity')) {
                logActivity('Updated household: ' . $household_code, $_SESSION['user_id'], 'update', 'households', $household_id);
            }
            
            // Commit transaction
            $conn->commit();
            $success_message = "Household has been updated successfully.";
            
            // Redirect to household view page
            header("Location: view_household.php?id=$household_id&success=2");
            exit;
            
        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollBack();
            $errors[] = $e->getMessage();
        }
    }
}

// Page title
$page_title = "Edit Household - Barangay Management System";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        /* Card styles */
        .card {
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }
        .card-header {
            font-weight: bold;
        }
        .border-left-primary {
            border-left: 4px solid #4e73df !important;
        }
        .border-left-success {
            border-left: 4px solid #1cc88a !important;
        }
        .border-left-info {
            border-left: 4px solid #36b9cc !important;
        }
        .border-left-warning {
            border-left: 4px solid #f6c23e !important;
        }
        .border-left-danger {
            border-left: 4px solid #e74a3b !important;
        }
        
        /* Button styles */
        .btn {
            border-radius: 5px;
            font-weight: 500;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">🏠 Edit Household</h1>
                    <div>
                        <a href="view_household.php?id=<?php echo $household_id; ?>" class="btn btn-secondary">
                            ⬅️ Back to Household
                        </a>
                    </div>
                </div>
                
                <?php if (!empty($errors)): ?>
                <div class="alert alert-danger">
                    <strong>❌ Error:</strong>
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                        <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show">
                    <strong>✅ Success:</strong> <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <form action="edit_household.php?id=<?php echo $household_id; ?>" method="POST">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card border-left-primary shadow">
                                <div class="card-header bg-light">
                                    <h5 class="card-title mb-0 text-primary">🏠 Household Information</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="household_code" class="form-label">🔢 Household Code <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="household_code" name="household_code" value="<?php echo htmlspecialchars($household['household_code']); ?>" required>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="household_head" class="form-label">👨‍👩‍👧‍👦 Household Head <span class="text-danger">*</span></label>
                                        <select class="form-select" id="household_head" name="household_head" required>
                                            <option value="">-- Select Resident --</option>
                                            <?php while ($resident = $residents_stmt->fetch(PDO::FETCH_ASSOC)): ?>
                                            <option value="<?php echo $resident['resident_id']; ?>" 
                                                <?php echo ($resident['resident_id'] == $household['household_head_id']) ? 'selected' : ''; ?>
                                                data-address="<?php echo htmlspecialchars($resident['address']); ?>">
                                                <?php echo htmlspecialchars($resident['full_name']); ?> (<?php echo $resident['gender']; ?>)
                                            </option>
                                            <?php endwhile; ?>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="address" class="form-label">📍 Household Address <span class="text-danger">*</span></label>
                                        <textarea class="form-control" id="address" name="address" rows="3" required><?php echo htmlspecialchars($household['address']); ?></textarea>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="status" class="form-label">📊 Status <span class="text-danger">*</span></label>
                                        <select class="form-select" id="status" name="status" required>
                                            <option value="Active" <?php echo $household['status'] == 'Active' ? 'selected' : ''; ?>>Active</option>
                                            <option value="Inactive" <?php echo $household['status'] == 'Inactive' ? 'selected' : ''; ?>>Inactive</option>
                                            <option value="Relocated" <?php echo $household['status'] == 'Relocated' ? 'selected' : ''; ?>>Relocated</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card border-left-info shadow">
                                <div class="card-header bg-light">
                                    <h5 class="card-title mb-0 text-info">📝 Additional Information</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="landmark" class="form-label">🗿 Landmark</label>
                                        <textarea class="form-control" id="landmark" name="landmark" rows="3"><?php echo htmlspecialchars($household['landmark'] ?? ''); ?></textarea>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="years_of_residency" class="form-label">📅 Years of Residency</label>
                                        <input type="text" class="form-control" id="years_of_residency" name="years_of_residency" value="<?php echo $household['years_of_residency'] ?? ''; ?>">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="house_ownership" class="form-label">🏘️ House Ownership</label>
                                        <select class="form-select" id="house_ownership" name="house_ownership">
                                            <option value="Owned" <?php echo $household['house_ownership'] == 'Owned' ? 'selected' : ''; ?>>Owned</option>
                                            <option value="Rented" <?php echo $household['house_ownership'] == 'Rented' ? 'selected' : ''; ?>>Rented</option>
                                            <option value="Living with Relatives" <?php echo $household['house_ownership'] == 'Living with Relatives' ? 'selected' : ''; ?>>Living with Relatives</option>
                                            <option value="Other" <?php echo $household['house_ownership'] == 'Other' ? 'selected' : ''; ?>>Other</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="monthly_rent" class="form-label">💰 Monthly Rent</label>
                                        <input type="text" class="form-control" id="monthly_rent" name="monthly_rent" value="<?php echo $household['monthly_rent'] ?? '0'; ?>">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="economic_status" class="form-label">💼 Economic Status</label>
                                        <select class="form-select" id="economic_status" name="economic_status">
                                            <option value="">Select Economic Status</option>
                                            <option value="Low Income" <?php echo $household['economic_status'] == 'Low Income' ? 'selected' : ''; ?>>Low Income</option>
                                            <option value="Middle Income" <?php echo $household['economic_status'] == 'Middle Income' ? 'selected' : ''; ?>>Middle Income</option>
                                            <option value="High Income" <?php echo $household['economic_status'] == 'High Income' ? 'selected' : ''; ?>>High Income</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="1" id="has_electricity" name="has_electricity" <?php echo $household['has_electricity'] ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="has_electricity">
                                                💡 Has Electricity
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="1" id="has_water_supply" name="has_water_supply" <?php echo $household['has_water_supply'] ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="has_water_supply">
                                                🚿 Has Water Supply
                                            </label>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" value="1" id="has_internet" name="has_internet" <?php echo $household['has_internet'] ? 'checked' : ''; ?>>
                                            <label class="form-check-label" for="has_internet">
                                                🌐 Has Internet
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12 text-end">
                            <a href="view_household.php?id=<?php echo $household_id; ?>" class="btn btn-secondary">❌ Cancel</a>
                            <button type="submit" class="btn btn-primary">💾 Update Household</button>
                        </div>
                    </div>
                </form>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize Select2 for resident dropdowns
            $('#household_head').select2({
                theme: 'bootstrap-5',
                placeholder: 'Select a resident'
            });
            
            // Show/hide monthly rent field based on house ownership
            const monthlyRentToggle = function() {
                if($('#house_ownership').val() === 'Rented') {
                    $('#monthly_rent').closest('.mb-3').show();
                } else {
                    $('#monthly_rent').closest('.mb-3').hide();
                }
            };
            
            // Initial toggle based on selected value
            monthlyRentToggle();
            
            // Toggle on change
            $('#house_ownership').on('change', monthlyRentToggle);
        });
    </script>
</body>
</html> 