<?php
session_start();
include '../../includes/config/database.php';
include '../../includes/functions/permission_functions.php';
include '../../includes/functions/utility.php';
include '../../includes/functions/functions.php';
include '../../includes/functions/log_function.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check permission
if (!hasPermission('edit_complaint')) {
    header("Location: ../../index.php");
    exit;
}

// Initialize variables
$success_message = '';
$error_message = '';
$complaint_data = null;

// Check if complaint ID is provided
if (!isset($_GET['complaint_id']) || empty($_GET['complaint_id'])) {
    $_SESSION['error'] = "No complaint ID provided.";
    header("Location: complaints.php");
    exit;
}

$complaint_id = (int)$_GET['complaint_id'];

// Fetch complaint details
try {
    $query = "SELECT c.*, 
             r1.first_name as complainant_first_name, r1.last_name as complainant_last_name,
             r2.first_name as respondent_first_name, r2.last_name as respondent_last_name
             FROM complaints c
             LEFT JOIN residents r1 ON c.complainant_id = r1.resident_id
             LEFT JOIN residents r2 ON c.respondent_id = r2.resident_id
             WHERE c.complaint_id = :complaint_id";
    
    $stmt = $conn->prepare($query);
    $stmt->bindValue(':complaint_id', $complaint_id, PDO::PARAM_INT);
    $stmt->execute();
    
    if ($stmt->rowCount() == 0) {
        $_SESSION['error'] = "Complaint not found.";
        header("Location: complaints.php");
        exit;
    }
    
    $complaint_data = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error fetching complaint: " . $e->getMessage());
    $_SESSION['error'] = "Database error occurred. Please try again.";
    header("Location: complaints.php");
    exit;
}

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get form data
    $resolution_type = sanitize($_POST['resolution_type']);
    $resolution_content = sanitize($_POST['resolution_content']);
    $resolution_date = sanitize($_POST['resolution_date']);
    $update_status = isset($_POST['update_status']) ? true : false;
    
    // Validate required fields
    if (empty($resolution_type) || empty($resolution_content) || empty($resolution_date)) {
        $error_message = "Please fill in all required fields";
    } else {
        try {
            // Begin transaction
            $conn->beginTransaction();
            
            // Check if resolutions table exists
            $table_check = $conn->prepare("SHOW TABLES LIKE 'resolutions'");
            $table_check->execute();
            
            if ($table_check->rowCount() == 0) {
                // Create resolutions table if it doesn't exist
                $create_table = "CREATE TABLE resolutions (
                    resolution_id INT AUTO_INCREMENT PRIMARY KEY,
                    complaint_id INT NOT NULL,
                    resolution_type VARCHAR(50) NOT NULL,
                    resolution_content TEXT NOT NULL,
                    resolution_date DATE NOT NULL,
                    resolved_by INT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (complaint_id) REFERENCES complaints(complaint_id) ON DELETE CASCADE
                )";
                $conn->exec($create_table);
            }
            
            // Insert resolution
            $insert_query = "INSERT INTO resolutions (complaint_id, resolution_type, resolution_details, resolution_date, resolved_by)
                           VALUES (:complaint_id, :resolution_type, :resolution_details, :resolution_date, :resolved_by)";
            
            $insert_stmt = $conn->prepare($insert_query);
            $insert_stmt->bindValue(':complaint_id', $complaint_id, PDO::PARAM_INT);
            $insert_stmt->bindValue(':resolution_type', $resolution_type);
            $insert_stmt->bindValue(':resolution_details', $resolution_content);
            $insert_stmt->bindValue(':resolution_date', $resolution_date);
            $insert_stmt->bindValue(':resolved_by', $_SESSION['user_id'], PDO::PARAM_INT);
            $insert_stmt->execute();
            
            // Update complaint status if requested
            if ($update_status) {
                $update_query = "UPDATE complaints SET status = 'Resolved' WHERE complaint_id = :complaint_id";
                $update_stmt = $conn->prepare($update_query);
                $update_stmt->bindValue(':complaint_id', $complaint_id, PDO::PARAM_INT);
                $update_stmt->execute();
                
                // Add to complaint updates
                $update_notes = "Status updated to Resolved. Resolution added: " . $resolution_type;
                
                // Check if complaint_updates table exists
                $table_check = $conn->prepare("SHOW TABLES LIKE 'complaint_updates'");
                $table_check->execute();
                
                if ($table_check->rowCount() == 0) {
                    // Create complaint_updates table if it doesn't exist
                    $create_table = "CREATE TABLE complaint_updates (
                        update_id INT AUTO_INCREMENT PRIMARY KEY,
                        complaint_id INT NOT NULL,
                        status VARCHAR(50) NOT NULL,
                        notes TEXT,
                        updated_by INT NOT NULL,
                        update_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (complaint_id) REFERENCES complaints(complaint_id) ON DELETE CASCADE
                    )";
                    $conn->exec($create_table);
                }
                
                $insert_update = "INSERT INTO complaint_updates (complaint_id, status, notes, updated_by)
                                VALUES (:complaint_id, 'Resolved', :notes, :updated_by)";
                $insert_stmt = $conn->prepare($insert_update);
                $insert_stmt->bindValue(':complaint_id', $complaint_id, PDO::PARAM_INT);
                $insert_stmt->bindValue(':notes', $update_notes);
                $insert_stmt->bindValue(':updated_by', $_SESSION['user_id'], PDO::PARAM_INT);
                $insert_stmt->execute();
            }
            
            // Log activity
            log_activity_safe($conn, $_SESSION['user_id'], 'Add Resolution', "Added resolution to complaint #$complaint_id", 'complaints', $complaint_id);
            
            // Commit transaction
            $conn->commit();
            
            $_SESSION['message'] = "Resolution has been added successfully.";
            $_SESSION['message_type'] = "success";
            
            // Redirect to view complaint page
            header("Location: view_complaint.php?id=" . $complaint_id);
            exit;
        } catch (PDOException $e) {
            // Rollback transaction on error
            if ($conn->inTransaction()) {
                $conn->rollBack();
            }
            
            error_log("Error adding resolution: " . $e->getMessage());
            $error_message = "Error adding resolution: " . $e->getMessage();
        }
    }
}

// Page title
$page_title = "Add Resolution - Barangay Management System";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border: none;
            margin-bottom: 24px;
        }
        
        .card-header {
            background-color: rgba(32, 201, 151, 0.1);
            border-bottom: none;
            padding: 15px 20px;
        }
        
        .form-label {
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .required-indicator {
            color: #dc3545;
            font-weight: bold;
        }
        
        .btn-success {
            background-color: #20c997;
            border-color: #20c997;
        }
        
        .btn-success:hover {
            background-color: #1ba87e;
            border-color: #1ba87e;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="fas fa-check-circle text-success me-2"></i> Add Resolution</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="../../index.php">Home</a></li>
                            <li class="breadcrumb-item"><a href="complaints.php">Complaints</a></li>
                            <li class="breadcrumb-item"><a href="view_complaint.php?id=<?php echo $complaint_id; ?>">View Complaint</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Add Resolution</li>
                        </ol>
                    </nav>
                </div>
                
                <?php if (!empty($success_message)): ?>
                <div class="alert alert-success" role="alert">
                    <i class="fas fa-check-circle me-2"></i> <?php echo $success_message; ?>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i> <?php echo $error_message; ?>
                </div>
                <?php endif; ?>
                
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-file-contract me-2"></i> Resolution Details
                                </h5>
                            </div>
                            <div class="card-body">
                                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]) . '?complaint_id=' . $complaint_id; ?>" method="POST">
                                    <div class="mb-3">
                                        <label for="resolution_type" class="form-label">Resolution Type <span class="required-indicator">*</span></label>
                                        <select class="form-select" id="resolution_type" name="resolution_type" required>
                                            <option value="">Select Resolution Type</option>
                                            <option value="Amicable Settlement">Amicable Settlement</option>
                                            <option value="Mediation">Mediation</option>
                                            <option value="Arbitration">Arbitration</option>
                                            <option value="Dismissal">Dismissal</option>
                                            <option value="Referral to Higher Authority">Referral to Higher Authority</option>
                                            <option value="Other">Other</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="resolution_date" class="form-label">Resolution Date <span class="required-indicator">*</span></label>
                                        <input type="date" class="form-control" id="resolution_date" name="resolution_date" required value="<?php echo date('Y-m-d'); ?>" max="<?php echo date('Y-m-d'); ?>">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="resolution_content" class="form-label">Resolution Content <span class="required-indicator">*</span></label>
                                        <textarea class="form-control" id="resolution_content" name="resolution_content" rows="8" required></textarea>
                                        <small class="text-muted">Provide detailed information about the resolution, including terms, agreements, and outcomes.</small>
                                    </div>
                                    
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="update_status" name="update_status" checked>
                                        <label class="form-check-label" for="update_status">Update complaint status to "Resolved"</label>
                                    </div>
                                    
                                    <div class="d-flex justify-content-end mt-4">
                                        <a href="view_complaint.php?id=<?php echo $complaint_id; ?>" class="btn btn-secondary me-2">
                                            <i class="fas fa-times me-1"></i> Cancel
                                        </a>
                                        <button type="submit" class="btn btn-success">
                                            <i class="fas fa-save me-1"></i> Save Resolution
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-info-circle me-2"></i> Complaint Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <p><strong>Case #:</strong> <?php echo str_pad($complaint_data['complaint_id'], 5, '0', STR_PAD_LEFT); ?></p>
                                <p><strong>Type:</strong> <?php echo htmlspecialchars($complaint_data['complaint_type']); ?></p>
                                <p><strong>Status:</strong> <span class="badge bg-<?php echo getStatusColor($complaint_data['status']); ?>"><?php echo htmlspecialchars($complaint_data['status']); ?></span></p>
                                <p><strong>Complainant:</strong> <?php echo !empty($complaint_data['complainant_id']) ? htmlspecialchars($complaint_data['complainant_first_name'] . ' ' . $complaint_data['complainant_last_name']) : htmlspecialchars($complaint_data['complainant_name']); ?></p>
                                <p><strong>Respondent:</strong> <?php echo !empty($complaint_data['respondent_id']) ? htmlspecialchars($complaint_data['respondent_first_name'] . ' ' . $complaint_data['respondent_last_name']) : htmlspecialchars($complaint_data['respondent_name']); ?></p>
                                <p><strong>Incident Date:</strong> <?php echo date('F d, Y', strtotime($complaint_data['incident_date'])); ?></p>
                                <p><strong>Filed Date:</strong> <?php echo isset($complaint_data['filing_date']) ? date('F d, Y', strtotime($complaint_data['filing_date'])) : 'Not available'; ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</body>
</html>
