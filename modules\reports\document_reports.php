<?php
include '../../includes/functions/permission_functions.php';
// Start session
session_start();

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    // Include necessary files
    require_once '../../includes/config/config.php';
    require_once '../../includes/config/database.php';
    require_once '../../includes/functions/utility.php';
    
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        header("Location: ../../login.php");
        exit();
    }
    
    // Check if user has permission for reports
    if (!hasPermission('view_reports') && !hasPermission('admin')) {
        header("Location: ../../index.php");
        exit();
    }
    
    // Set page title and module info
    $page_title = "Document Reports";
    $module_name = "Reports";
    $sub_module = "Documents";
    
    // Initialize variables
    $message = "";
    $message_type = "";
    $selected_year = isset($_GET['year']) ? $_GET['year'] : date('Y');
    $selected_month = isset($_GET['month']) ? $_GET['month'] : '';
    $selected_document_type = isset($_GET['document_type']) ? $_GET['document_type'] : '';
    
    // Connect to database
    $conn = new PDO("mysql:host=$db_host;dbname=$db_name", $db_user, $db_pass);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Initialize document summary variables with default values
    $total_requests = 0;
    $pending_requests = 0;
    $completed_requests = 0;
    $rejected_requests = 0;
    $available_years = [date('Y')]; // Initialize with current year
    $document_types = [];
    $document_type_counts = [];
    $monthly_requests_data = [];
    $status_counts = [];
    
    // Document types list
    $document_type_list = [
        'Barangay Clearance',
        'Barangay Certificate', 
        'Certificate of Residency', 
        'Business Permit', 
        'Certificate of Indigency',
        'Other'
    ];

    // Check if required tables exist
    $tables_exist = true;
    try {
        $stmt = $conn->prepare("SHOW TABLES LIKE 'document_requests'");
        $stmt->execute();
        
        if ($stmt->rowCount() == 0) {
            $tables_exist = false;
            $message = "The document requests table does not exist yet. Please set up the document module first.";
            $message_type = "info";
        }
        
        // If table exists, load document data
        if ($tables_exist) {
            // Get available years
            $year_query = "SELECT DISTINCT YEAR(request_date) as year FROM document_requests ORDER BY year DESC";
            $year_stmt = $conn->query($year_query);
            $available_years = $year_stmt->fetchAll(PDO::FETCH_COLUMN);
            
            if (empty($available_years)) {
                $available_years = [date('Y')];
            }
            
            // Process year selection
            if (isset($_GET['year']) && !empty($_GET['year'])) {
                $selected_year = $_GET['year'];
            }
            
            // Get document types from database
            $type_query = "SELECT DISTINCT document_type FROM document_requests";
            $type_stmt = $conn->query($type_query);
            $document_types = $type_stmt->fetchAll(PDO::FETCH_COLUMN);
            
            // Build query conditions
            $conditions = ["YEAR(request_date) = :year"];
            $params = [':year' => $selected_year];
            
            if (!empty($selected_month)) {
                $conditions[] = "MONTH(request_date) = :month";
                $params[':month'] = $selected_month;
            }
            
            if (!empty($selected_document_type)) {
                $conditions[] = "document_type = :document_type";
                $params[':document_type'] = $selected_document_type;
            }
            
            $where_clause = !empty($conditions) ? "WHERE " . implode(" AND ", $conditions) : "";
            
            // Get document summary data
            $summary_query = "SELECT 
                COUNT(*) as total_requests,
                SUM(CASE WHEN status = 'Pending' OR status = 'Processing' THEN 1 ELSE 0 END) as pending_requests,
                SUM(CASE WHEN status = 'Released' OR status = 'Ready for Pickup' OR status = 'Completed' THEN 1 ELSE 0 END) as completed_requests,
                SUM(CASE WHEN status = 'Cancelled' OR status = 'Rejected' THEN 1 ELSE 0 END) as rejected_requests
                FROM document_requests
                $where_clause";
            
            $summary_stmt = $conn->prepare($summary_query);
            foreach ($params as $key => $value) {
                $summary_stmt->bindValue($key, $value);
            }
            $summary_stmt->execute();
            
            $summary_data = $summary_stmt->fetch(PDO::FETCH_ASSOC);
            $total_requests = $summary_data['total_requests'];
            $pending_requests = $summary_data['pending_requests'];
            $completed_requests = $summary_data['completed_requests'];
            $rejected_requests = $summary_data['rejected_requests'];
            
            // Get document type distribution
            $type_query = "SELECT document_type, COUNT(*) as count 
                          FROM document_requests 
                          $where_clause 
                          GROUP BY document_type";
            
            $type_stmt = $conn->prepare($type_query);
            foreach ($params as $key => $value) {
                $type_stmt->bindValue($key, $value);
            }
            $type_stmt->execute();
            
            $document_type_counts = [];
            $type_data = $type_stmt->fetchAll(PDO::FETCH_ASSOC);
            foreach ($type_data as $row) {
                $document_type_counts[$row['document_type']] = $row['count'];
            }
            
            // Get monthly distribution
            $monthly_query = "SELECT MONTH(request_date) as month, COUNT(*) as count 
                             FROM document_requests 
                             WHERE YEAR(request_date) = :year 
                             GROUP BY MONTH(request_date)";
            
            $monthly_stmt = $conn->prepare($monthly_query);
            $monthly_stmt->bindValue(':year', $selected_year);
            $monthly_stmt->execute();
            
            $monthly_data = array_fill(1, 12, 0); // Initialize all months with 0
            $monthly_results = $monthly_stmt->fetchAll(PDO::FETCH_ASSOC);
            foreach ($monthly_results as $row) {
                $monthly_data[intval($row['month'])] = intval($row['count']);
            }
            $monthly_requests_data = array_values($monthly_data);
            
            // Get status distribution
            $status_query = "SELECT status, COUNT(*) as count 
                           FROM document_requests 
                           $where_clause 
                           GROUP BY status";
            
            $status_stmt = $conn->prepare($status_query);
            foreach ($params as $key => $value) {
                $status_stmt->bindValue($key, $value);
            }
            $status_stmt->execute();
            
            $status_counts = [];
            $status_data = $status_stmt->fetchAll(PDO::FETCH_ASSOC);
            foreach ($status_data as $row) {
                $status_counts[$row['status']] = $row['count'];
            }
        }
    } catch (PDOException $e) {
        // Don't show database errors to users
        error_log("Error retrieving document data: " . $e->getMessage());
        $message = "The document reporting system is currently being set up. Please check back later.";
        $message_type = "info";
        
        // Initialize with empty data
        $total_requests = 0;
        $pending_requests = 0;
        $completed_requests = 0;
        $rejected_requests = 0;
        $selected_year = date('Y');
        $tables_exist = false;
    }

    // Include header
    include_once '../../includes/header.php';
} catch (Exception $e) {
    // Handle any exceptions
    $message = "An error occurred: " . $e->getMessage();
    $message_type = "danger";
    
    // Try to include header even if there was an error
    try {
        include_once '../../includes/header.php';
    } catch (Exception $headerEx) {
        // If header include fails, provide a minimal HTML structure
        echo '<!DOCTYPE html>
        <html>
        <head>
            <title>Document Reports</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        </head>
        <body>
            <div class="container mt-4">
                <h1>Document Reports</h1>';
    }
}
?>

<style>
    /* Stat Card Styles */
    .document-card {
        border-radius: 0.75rem;
        overflow: hidden;
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
        position: relative;
        z-index: 1;
    }
    .document-card:hover {
        transform: translateY(-5px) !important;
        box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2) !important;
        z-index: 2;
    }
    
    /* Quick stats cards specific styling */
    .document-card .card-icon {
        font-size: 2.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 60px;
        width: 60px;
        border-radius: 50%;
        transition: transform 0.3s ease;
    }
    .document-card:hover .card-icon {
        transform: scale(1.1);
    }
    
    /* Chart card hover effects */
    .chart-card {
        transition: all 0.3s ease;
        border-radius: 0.75rem;
        overflow: hidden;
        border: none;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
    }
    .chart-card:hover {
        transform: translateY(-5px) !important;
        box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2) !important;
    }
    
    /* Table hover effects */
    .table-card {
        transition: all 0.3s ease;
        border-radius: 0.75rem;
        overflow: hidden;
        border: none;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
    }
    .table-card:hover {
        transform: translateY(-5px) !important;
        box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2) !important;
    }
    
    /* Background colors with opacity */
    .bg-primary-soft { background-color: rgba(78, 115, 223, 0.1) !important; }
    .bg-success-soft { background-color: rgba(28, 200, 138, 0.1) !important; }
    .bg-info-soft { background-color: rgba(54, 185, 204, 0.1) !important; }
    .bg-warning-soft { background-color: rgba(246, 194, 62, 0.1) !important; }
    .bg-danger-soft { background-color: rgba(231, 74, 59, 0.1) !important; }

    .chart-container {
        position: relative;
        height: 300px;
        margin-bottom: 20px;
    }
    @media (max-width: 768px) {
        .chart-container {
            height: 250px;
        }
    }
</style>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <?php include_once '../../includes/sidebar.php'; ?>
        
        <!-- Main Content -->
        <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-file-alt me-2"></i> Document Reports</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <form action="" method="GET" class="d-flex">
                        <select class="form-select me-2" name="year" onchange="this.form.submit()">
                            <?php foreach($available_years as $year): ?>
                                <option value="<?php echo $year; ?>" <?php echo ($year == $selected_year) ? 'selected' : ''; ?>>
                                    <?php echo $year; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <select class="form-select me-2" name="document_type" onchange="this.form.submit()">
                            <option value="">All Document Types</option>
                            <?php foreach($document_type_list as $type): ?>
                                <option value="<?php echo $type; ?>" <?php echo ($type == $selected_document_type) ? 'selected' : ''; ?>>
                                    <?php echo $type; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print"></i> Print Report
                        </button>
                    </form>
                </div>
            </div>

            <?php if (!empty($message)): ?>
                <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                    <i class="fas fa-info-circle me-2"></i> <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (!$tables_exist): ?>
                <div class="alert alert-info mb-4">
                    <h5><i class="fas fa-info-circle me-2"></i> Document Reports Module Information</h5>
                    <p>The document reporting system is still being set up. Please configure the document module first.</p>
                    <p>When fully implemented, this report will provide comprehensive document insights including request volumes, processing times, and status distribution.</p>
                </div>
            <?php endif; ?>

            <!-- Document Summary Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card document-card shadow h-100">
                        <div class="card-body p-3">
                            <div class="row align-items-center">
                                <div class="col-4">
                                    <div class="card-icon bg-primary-soft text-primary">
                                        📄
                                    </div>
                                </div>
                                <div class="col-8 text-end">
                                    <h4 class="mt-0 mb-1"><?php echo number_format($total_requests ?? 0, 0); ?></h4>
                                    <p class="mb-0 text-muted">Total Requests</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card document-card shadow h-100">
                        <div class="card-body p-3">
                            <div class="row align-items-center">
                                <div class="col-4">
                                    <div class="card-icon bg-warning-soft text-warning">
                                        ⏳
                                    </div>
                                </div>
                                <div class="col-8 text-end">
                                    <h4 class="mt-0 mb-1"><?php echo number_format($pending_requests ?? 0, 0); ?></h4>
                                    <p class="mb-0 text-muted">Pending Requests</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card document-card shadow h-100">
                        <div class="card-body p-3">
                            <div class="row align-items-center">
                                <div class="col-4">
                                    <div class="card-icon bg-success-soft text-success">
                                        ✅
                                    </div>
                                </div>
                                <div class="col-8 text-end">
                                    <h4 class="mt-0 mb-1"><?php echo number_format($completed_requests ?? 0, 0); ?></h4>
                                    <p class="mb-0 text-muted">Completed Requests</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card document-card shadow h-100">
                        <div class="card-body p-3">
                            <div class="row align-items-center">
                                <div class="col-4">
                                    <div class="card-icon bg-danger-soft text-danger">
                                        ❌
                                    </div>
                                </div>
                                <div class="col-8 text-end">
                                    <h4 class="mt-0 mb-1"><?php echo number_format($rejected_requests ?? 0, 0); ?></h4>
                                    <p class="mb-0 text-muted">Rejected/Cancelled</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts Row -->
            <div class="row">
                <div class="col-lg-6 mb-4">
                    <div class="card chart-card shadow mb-4">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">Monthly Document Requests (<?php echo $selected_year; ?>)</h6>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="monthlyRequestsChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6 mb-4">
                    <div class="card chart-card shadow mb-4">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">Document Requests by Type</h6>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="documentTypeChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status Distribution Chart -->
            <div class="row">
                <div class="col-12 mb-4">
                    <div class="card chart-card shadow mb-4">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">Request Status Distribution</h6>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="statusDistributionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Document Request Detail Table -->
            <div class="card table-card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Document Request Details</h6>
                </div>
                <div class="card-body">
                    <?php if (!$tables_exist): ?>
                        <div class="alert alert-info">
                            <p><i class="fas fa-info-circle me-2"></i> Document request data will be displayed here once the module is set up.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <?php
                            // Fetch detailed document requests data
                            $detail_conditions = ["YEAR(request_date) = :year"];
                            $detail_params = [':year' => $selected_year];
                            
                            if (!empty($selected_month)) {
                                $detail_conditions[] = "MONTH(request_date) = :month";
                                $detail_params[':month'] = $selected_month;
                            }
                            
                            if (!empty($selected_document_type)) {
                                $detail_conditions[] = "document_type = :document_type";
                                $detail_params[':document_type'] = $selected_document_type;
                            }
                            
                            $detail_where_clause = !empty($detail_conditions) ? "WHERE " . implode(" AND ", $detail_conditions) : "";
                            
                            $details_query = "SELECT 
                                d.document_type, 
                                d.status,
                                COUNT(*) as count,
                                AVG(DATEDIFF(IFNULL(d.release_date, CURRENT_DATE), d.request_date)) as avg_processing_days
                                FROM document_requests d
                                $detail_where_clause
                                GROUP BY d.document_type, d.status
                                ORDER BY d.document_type, d.status";
                            
                            $details_stmt = $conn->prepare($details_query);
                            foreach ($detail_params as $key => $value) {
                                $details_stmt->bindValue($key, $value);
                            }
                            $details_stmt->execute();
                            $details_data = $details_stmt->fetchAll(PDO::FETCH_ASSOC);
                            ?>
                            
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Document Type</th>
                                        <th>Status</th>
                                        <th>Count</th>
                                        <th>Avg. Processing Time (days)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (count($details_data) > 0): ?>
                                        <?php foreach ($details_data as $detail): ?>
                                            <tr>
                                                <td><?php echo $detail['document_type']; ?></td>
                                                <td>
                                                    <?php 
                                                    $status_class = '';
                                                    switch ($detail['status']) {
                                                        case 'Pending':
                                                        case 'Processing':
                                                            $status_class = 'bg-warning';
                                                            break;
                                                        case 'Released':
                                                        case 'Ready for Pickup':
                                                        case 'Completed':
                                                            $status_class = 'bg-success';
                                                            break;
                                                        case 'Cancelled':
                                                        case 'Rejected':
                                                            $status_class = 'bg-danger';
                                                            break;
                                                        default:
                                                            $status_class = 'bg-secondary';
                                                    }
                                                    ?>
                                                    <span class="badge <?php echo $status_class; ?>"><?php echo $detail['status']; ?></span>
                                                </td>
                                                <td><?php echo $detail['count']; ?></td>
                                                <td><?php echo round($detail['avg_processing_days'], 1); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="4" class="text-center">No document request data available for the selected filters.</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Print Button -->
            <div class="text-end mb-4">
                <button class="btn btn-primary" onclick="window.print()">
                    <i class="fas fa-print me-2"></i> Print Report
                </button>
            </div>
        </main>
    </div>
</div>

<!-- Include Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Monthly Document Requests Chart
        var monthlyCtx = document.getElementById('monthlyRequestsChart').getContext('2d');
        var monthlyChart = new Chart(monthlyCtx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                datasets: [{
                    label: 'Document Requests',
                    data: <?php echo json_encode($monthly_requests_data); ?>,
                    backgroundColor: 'rgba(78, 115, 223, 0.2)',
                    borderColor: 'rgba(78, 115, 223, 1)',
                    borderWidth: 2,
                    tension: 0.3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                    }
                }
            }
        });

        // Document Type Distribution Chart
        var typeCtx = document.getElementById('documentTypeChart').getContext('2d');
        var typeChart = new Chart(typeCtx, {
            type: 'pie',
            data: {
                labels: <?php echo json_encode(array_keys($document_type_counts)); ?>,
                datasets: [{
                    data: <?php echo json_encode(array_values($document_type_counts)); ?>,
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.8)',
                        'rgba(54, 162, 235, 0.8)',
                        'rgba(255, 206, 86, 0.8)',
                        'rgba(75, 192, 192, 0.8)',
                        'rgba(153, 102, 255, 0.8)',
                        'rgba(255, 159, 64, 0.8)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    }
                }
            }
        });

        // Status Distribution Chart
        var statusCtx = document.getElementById('statusDistributionChart').getContext('2d');
        var statusChart = new Chart(statusCtx, {
            type: 'bar',
            data: {
                labels: <?php echo json_encode(array_keys($status_counts)); ?>,
                datasets: [{
                    label: 'Number of Requests',
                    data: <?php echo json_encode(array_values($status_counts)); ?>,
                    backgroundColor: [
                        'rgba(255, 193, 7, 0.8)',  // Pending - Warning
                        'rgba(23, 162, 184, 0.8)', // Processing - Info
                        'rgba(40, 167, 69, 0.8)',  // Completed - Success
                        'rgba(220, 53, 69, 0.8)',  // Rejected - Danger
                        'rgba(0, 123, 255, 0.8)',  // Ready for Pickup - Primary
                        'rgba(40, 167, 69, 0.8)',  // Released - Success
                        'rgba(220, 53, 69, 0.8)'   // Cancelled - Danger
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    });
</script>

<?php include_once '../../includes/footer.php'; ?> 