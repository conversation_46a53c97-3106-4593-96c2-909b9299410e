<?php
session_start();
include '../../includes/config/database.php';
include '../../includes/functions/permission_functions.php';
include '../../includes/functions/utility.php';
include '../../includes/functions/functions.php';
include '../../includes/functions/log_function.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check permission
if (!hasPermission('edit_complaint')) {
    header("Location: ../../index.php");
    exit;
}

// Initialize variables
$success_message = '';
$error_message = '';
$complaint_data = null;

// Check if complaint ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = "No complaint ID provided.";
    header("Location: complaints.php");
    exit;
}

$complaint_id = (int)$_GET['id'];

// Fetch complaint details
try {
    $query = "SELECT c.*, 
             r1.first_name as complainant_first_name, r1.last_name as complainant_last_name,
             r2.first_name as respondent_first_name, r2.last_name as respondent_last_name
             FROM complaints c
             LEFT JOIN residents r1 ON c.complainant_id = r1.resident_id
             LEFT JOIN residents r2 ON c.respondent_id = r2.resident_id
             WHERE c.complaint_id = :complaint_id";
    
    $stmt = $conn->prepare($query);
    $stmt->bindValue(':complaint_id', $complaint_id, PDO::PARAM_INT);
    $stmt->execute();
    
    if ($stmt->rowCount() == 0) {
        $_SESSION['error'] = "Complaint not found.";
        header("Location: complaints.php");
        exit;
    }
    
    $complaint_data = $stmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error fetching complaint: " . $e->getMessage());
    $_SESSION['error'] = "Database error occurred. Please try again.";
    header("Location: complaints.php");
    exit;
}

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get form data
    $status = sanitize($_POST['status']);
    $update_details = sanitize($_POST['update_details']);
    
    // Validate required fields
    if (empty($status) || empty($update_details)) {
        $error_message = "Please fill in all required fields";
    } else {
        try {
            // Begin transaction
            $conn->beginTransaction();
            
            // Check if complaint_updates table exists
            $table_check = $conn->prepare("SHOW TABLES LIKE 'complaint_updates'");
            $table_check->execute();
            
            if ($table_check->rowCount() == 0) {
                // Create complaint_updates table if it doesn't exist
                $create_table = "CREATE TABLE complaint_updates (
                    update_id INT AUTO_INCREMENT PRIMARY KEY,
                    complaint_id INT NOT NULL,
                    status VARCHAR(50) NOT NULL,
                    notes TEXT,
                    updated_by INT NOT NULL,
                    update_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (complaint_id) REFERENCES complaints(complaint_id) ON DELETE CASCADE
                )";
                $conn->exec($create_table);
            }
            
            // Insert update
            $insert_update = "INSERT INTO complaint_updates (complaint_id, status, notes, updated_by)
                            VALUES (:complaint_id, :status, :notes, :updated_by)";
            $insert_stmt = $conn->prepare($insert_update);
            $insert_stmt->bindValue(':complaint_id', $complaint_id, PDO::PARAM_INT);
            $insert_stmt->bindValue(':status', $status);
            $insert_stmt->bindValue(':notes', $update_details);
            $insert_stmt->bindValue(':updated_by', $_SESSION['user_id'], PDO::PARAM_INT);
            $insert_stmt->execute();
            
            // Update complaint status
            $update_query = "UPDATE complaints SET status = :status WHERE complaint_id = :complaint_id";
            $update_stmt = $conn->prepare($update_query);
            $update_stmt->bindValue(':status', $status);
            $update_stmt->bindValue(':complaint_id', $complaint_id, PDO::PARAM_INT);
            $update_stmt->execute();
            
            // Log activity
            log_activity_safe($conn, $_SESSION['user_id'], 'Update Complaint', "Updated status of complaint #$complaint_id to $status", 'complaints', $complaint_id);
            
            // Commit transaction
            $conn->commit();
            
            $_SESSION['message'] = "Complaint status has been updated successfully.";
            $_SESSION['message_type'] = "success";
            
            // Redirect to view complaint page
            header("Location: view_complaint.php?id=" . $complaint_id);
            exit;
        } catch (PDOException $e) {
            // Rollback transaction on error
            if ($conn->inTransaction()) {
                $conn->rollBack();
            }
            
            error_log("Error updating complaint: " . $e->getMessage());
            $error_message = "Error updating complaint: " . $e->getMessage();
        }
    }
}

// Function to get color for status badges
function getStatusColor($status) {
    switch ($status) {
        case 'Pending':
            return 'warning';
        case 'Under Investigation':
            return 'info';
        case 'Resolved':
            return 'success';
        case 'Dismissed':
            return 'danger';
        default:
            return 'secondary';
    }
}

// Page title
$page_title = "Update Complaint - Barangay Management System";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border: none;
            margin-bottom: 24px;
        }
        
        .card-header {
            background-color: rgba(111, 66, 193, 0.1);
            border-bottom: none;
            padding: 15px 20px;
        }
        
        .form-label {
            font-weight: 600;
            margin-bottom: 8px;
        }
        
        .required-indicator {
            color: #dc3545;
            font-weight: bold;
        }
        
        .btn-purple {
            background-color: #6f42c1;
            border-color: #6f42c1;
            color: white;
        }
        
        .btn-purple:hover {
            background-color: #5a32a3;
            border-color: #5a32a3;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2"><i class="fas fa-clipboard-list text-purple me-2"></i> Update Complaint</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="../../index.php">Home</a></li>
                            <li class="breadcrumb-item"><a href="complaints.php">Complaints</a></li>
                            <li class="breadcrumb-item"><a href="view_complaint.php?id=<?php echo $complaint_id; ?>">View Complaint</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Add Update</li>
                        </ol>
                    </nav>
                </div>
                
                <?php if (!empty($success_message)): ?>
                <div class="alert alert-success" role="alert">
                    <i class="fas fa-check-circle me-2"></i> <?php echo $success_message; ?>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i> <?php echo $error_message; ?>
                </div>
                <?php endif; ?>
                
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-clipboard-list me-2"></i> Update Details
                                </h5>
                            </div>
                            <div class="card-body">
                                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]) . '?id=' . $complaint_id; ?>" method="POST">
                                    <div class="mb-3">
                                        <label for="status" class="form-label">Status <span class="required-indicator">*</span></label>
                                        <select class="form-select" id="status" name="status" required>
                                            <option value="">Select Status</option>
                                            <option value="Pending" <?php echo ($complaint_data['status'] == 'Pending') ? 'selected' : ''; ?>>Pending</option>
                                            <option value="Under Investigation" <?php echo ($complaint_data['status'] == 'Under Investigation') ? 'selected' : ''; ?>>Under Investigation</option>
                                            <option value="Resolved" <?php echo ($complaint_data['status'] == 'Resolved') ? 'selected' : ''; ?>>Resolved</option>
                                            <option value="Dismissed" <?php echo ($complaint_data['status'] == 'Dismissed') ? 'selected' : ''; ?>>Dismissed</option>
                                        </select>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="update_details" class="form-label">Update Details <span class="required-indicator">*</span></label>
                                        <textarea class="form-control" id="update_details" name="update_details" rows="8" required></textarea>
                                        <small class="text-muted">Provide detailed information about the update, including actions taken, findings, or next steps.</small>
                                    </div>
                                    
                                    <div class="d-flex justify-content-end mt-4">
                                        <a href="view_complaint.php?id=<?php echo $complaint_id; ?>" class="btn btn-secondary me-2">
                                            <i class="fas fa-times me-1"></i> Cancel
                                        </a>
                                        <button type="submit" class="btn btn-purple">
                                            <i class="fas fa-save me-1"></i> Save Update
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-info-circle me-2"></i> Complaint Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <p><strong>Case #:</strong> <?php echo str_pad($complaint_data['complaint_id'], 5, '0', STR_PAD_LEFT); ?></p>
                                <p><strong>Type:</strong> <?php echo htmlspecialchars($complaint_data['complaint_type']); ?></p>
                                <p><strong>Current Status:</strong> <span class="badge bg-<?php echo getStatusColor($complaint_data['status']); ?>"><?php echo htmlspecialchars($complaint_data['status']); ?></span></p>
                                <p><strong>Complainant:</strong> <?php echo !empty($complaint_data['complainant_id']) ? htmlspecialchars($complaint_data['complainant_first_name'] . ' ' . $complaint_data['complainant_last_name']) : htmlspecialchars($complaint_data['complainant_name']); ?></p>
                                <p><strong>Respondent:</strong> <?php echo !empty($complaint_data['respondent_id']) ? htmlspecialchars($complaint_data['respondent_first_name'] . ' ' . $complaint_data['respondent_last_name']) : htmlspecialchars($complaint_data['respondent_name']); ?></p>
                                <p><strong>Incident Date:</strong> <?php echo date('F d, Y', strtotime($complaint_data['incident_date'])); ?></p>
                                <p><strong>Filed Date:</strong> <?php echo isset($complaint_data['filing_date']) ? date('F d, Y', strtotime($complaint_data['filing_date'])) : 'Not available'; ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</body>
</html>
