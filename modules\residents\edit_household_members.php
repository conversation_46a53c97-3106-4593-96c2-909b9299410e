<?php
session_start();
include '../../includes/config/database.php';
include '../../includes/functions/permission_functions.php';
include '../../includes/functions/utility.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check permission
if (!hasPermission('edit_household')) {
    header("Location: ../../index.php");
    exit;
}

// Get household ID from URL
$household_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($household_id <= 0) {
    header("Location: households.php");
    exit;
}

// Get household information
$query = "SELECT h.*,
          CONCAT(r.last_name, ', ', r.first_name, ' ', COALESCE(r.middle_name, '')) as head_name,
          r.resident_id as head_id
          FROM households h
          LEFT JOIN residents r ON h.household_head_id = r.resident_id
          WHERE h.household_id = :household_id";
$stmt = $conn->prepare($query);
$stmt->bindParam(':household_id', $household_id, PDO::PARAM_INT);
$stmt->execute();

if ($stmt->rowCount() == 0) {
    // Household not found
    header("Location: households.php?error=not_found");
    exit;
}

$household = $stmt->fetch(PDO::FETCH_ASSOC);

// Get current household members
$members_query = "SELECT hm.*,
                 CONCAT(r.last_name, ', ', r.first_name, ' ', COALESCE(r.middle_name, '')) as full_name,
                 r.gender, r.birthdate, r.profile_photo,
                 TIMESTAMPDIFF(YEAR, r.birthdate, CURDATE()) as age
                 FROM household_members hm
                 JOIN residents r ON hm.resident_id = r.resident_id
                 WHERE hm.household_id = :household_id
                 ORDER BY
                    CASE
                        WHEN hm.relation_to_head = 'Head' THEN 1
                        WHEN hm.relation_to_head = 'Spouse' THEN 2
                        WHEN hm.relation_to_head = 'Son' THEN 3
                        WHEN hm.relation_to_head = 'Daughter' THEN 4
                        ELSE 5
                    END,
                    r.last_name, r.first_name";
$members_stmt = $conn->prepare($members_query);
$members_stmt->bindParam(':household_id', $household_id, PDO::PARAM_INT);
$members_stmt->execute();
$members = $members_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get all active residents who are not already in any household
$available_residents_query = "SELECT r.resident_id,
                           CONCAT(r.last_name, ', ', r.first_name, ' ', COALESCE(r.middle_name, '')) as full_name,
                           r.gender, r.birthdate, r.address,
                           TIMESTAMPDIFF(YEAR, r.birthdate, CURDATE()) as age
                           FROM residents r
                           WHERE r.status = 'active'
                           AND r.resident_id NOT IN (
                               SELECT resident_id FROM household_members
                           )
                           ORDER BY r.last_name, r.first_name";
$available_residents_stmt = $conn->prepare($available_residents_query);
$available_residents_stmt->execute();
$available_residents = $available_residents_stmt->fetchAll(PDO::FETCH_ASSOC);

$errors = [];
$success_message = '';

// Process form submission for adding members
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add_members') {
    // Start transaction
    $conn->beginTransaction();

    try {
        // Process new members
        if (isset($_POST['new_members']) && is_array($_POST['new_members']) &&
            isset($_POST['new_relations']) && is_array($_POST['new_relations'])) {

            $new_members = $_POST['new_members'];
            $new_relations = $_POST['new_relations'];

            // Validate arrays have same length
            if (count($new_members) !== count($new_relations)) {
                throw new Exception("Invalid form data. Please try again.");
            }

            // Insert new members
            $insert_query = "INSERT INTO household_members (household_id, resident_id, relation_to_head)
                           VALUES (:household_id, :resident_id, :relationship)";
            $insert_stmt = $conn->prepare($insert_query);

            for ($i = 0; $i < count($new_members); $i++) {
                if (empty($new_members[$i]) || empty($new_relations[$i])) {
                    continue; // Skip empty entries
                }

                $resident_id = (int)$new_members[$i];
                $relationship = htmlspecialchars(trim($new_relations[$i]));

                $insert_stmt->bindParam(':household_id', $household_id, PDO::PARAM_INT);
                $insert_stmt->bindParam(':resident_id', $resident_id, PDO::PARAM_INT);
                $insert_stmt->bindParam(':relationship', $relationship, PDO::PARAM_STR);
                $insert_stmt->execute();
            }
        }

        // Commit transaction
        $conn->commit();
        $success_message = "Household members have been updated successfully.";

        // Redirect to refresh the page and show updated data
        header("Location: edit_household_members.php?id=$household_id&success=1");
        exit;

    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollBack();
        $errors[] = $e->getMessage();
    }
}

// Process form submission for updating member relationships
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'update_relations') {
    // Start transaction
    $conn->beginTransaction();

    try {
        // Process updated relationships
        if (isset($_POST['member_ids']) && is_array($_POST['member_ids']) &&
            isset($_POST['relations']) && is_array($_POST['relations'])) {

            $member_ids = $_POST['member_ids'];
            $relations = $_POST['relations'];

            // Validate arrays have same length
            if (count($member_ids) !== count($relations)) {
                throw new Exception("Invalid form data. Please try again.");
            }

            // Update relationships
            $update_query = "UPDATE household_members
                           SET relation_to_head = :relationship
                           WHERE household_id = :household_id AND resident_id = :resident_id";
            $update_stmt = $conn->prepare($update_query);

            for ($i = 0; $i < count($member_ids); $i++) {
                if (empty($member_ids[$i]) || empty($relations[$i])) {
                    continue; // Skip empty entries
                }

                $resident_id = (int)$member_ids[$i];
                $relationship = htmlspecialchars(trim($relations[$i]));

                $update_stmt->bindParam(':household_id', $household_id, PDO::PARAM_INT);
                $update_stmt->bindParam(':resident_id', $resident_id, PDO::PARAM_INT);
                $update_stmt->bindParam(':relationship', $relationship, PDO::PARAM_STR);
                $update_stmt->execute();
            }
        }

        // Commit transaction
        $conn->commit();
        $success_message = "Member relationships have been updated successfully.";

        // Redirect to refresh the page and show updated data
        header("Location: edit_household_members.php?id=$household_id&success=2");
        exit;

    } catch (Exception $e) {
        // Rollback transaction on error
        $conn->rollBack();
        $errors[] = $e->getMessage();
    }
}

// Get success message from URL
if (isset($_GET['success'])) {
    switch ($_GET['success']) {
        case '1':
            $success_message = "Household members have been added successfully.";
            break;
        case '2':
            $success_message = "Member relationships have been updated successfully.";
            break;
        case '3':
            $success_message = "Member has been removed successfully.";
            break;
    }
}

// Page title
$page_title = "Manage Household Members - Barangay Management System";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        /* Card styles */
        .card {
            border-radius: 8px;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            margin-bottom: 1.5rem;
        }
        .card-header {
            padding: 1rem 1.35rem;
            margin-bottom: 0;
            background-color: #f8f9fc;
            border-bottom: 1px solid #e3e6f0;
        }
        .card-body {
            padding: 1.25rem;
        }

        /* Table styles */
        .table th {
            background-color: #f8f9fc;
            font-weight: 500;
        }

        /* Member row styles */
        .member-row {
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
            background-color: #f8f9fc;
        }

        /* Button styles */
        .btn-icon {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }

        /* Border left styles */
        .border-left-primary {
            border-left: 0.25rem solid #4e73df !important;
        }
        .border-left-success {
            border-left: 0.25rem solid #1cc88a !important;
        }
        .border-left-info {
            border-left: 0.25rem solid #36b9cc !important;
        }
        .border-left-warning {
            border-left: 0.25rem solid #f6c23e !important;
        }

        /* Profile image */
        .profile-img-sm {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
            border: 1px solid #e3e6f0;
            background-color: #f8f9fc;
        }

        /* Relationship badge styles */
        .badge-relationship {
            font-size: 0.8rem;
            padding: 0.35em 0.65em;
        }
        .badge-head {
            background-color: #4e73df;
            color: white;
        }
        .badge-spouse {
            background-color: #e74a3b;
            color: white;
        }
        .badge-child {
            background-color: #1cc88a;
            color: white;
        }
        .badge-other {
            background-color: #f6c23e;
            color: #212529;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>

            <!-- Main Content -->
            <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">👪 Manage Household Members</h1>
                    <div>
                        <a href="view_household.php?id=<?php echo $household_id; ?>" class="btn btn-secondary">
                            ⬅️ Back to Household
                        </a>
                    </div>
                </div>

                <!-- Alerts -->
                <?php if (!empty($errors)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <strong>Error!</strong>
                    <ul class="mb-0">
                        <?php foreach ($errors as $error): ?>
                        <li><?php echo $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <?php if (!empty($success_message)): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <strong>Success!</strong> <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <!-- Household Info Card -->
                <div class="card border-left-primary shadow mb-4">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0 text-primary">🏠 Household Information</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Household Code:</strong> <?php echo htmlspecialchars($household['household_code']); ?></p>
                                <p><strong>Address:</strong> <?php echo htmlspecialchars($household['address']); ?></p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Household Head:</strong> <?php echo htmlspecialchars($household['head_name']); ?></p>
                                <p><strong>Status:</strong> <?php echo htmlspecialchars($household['status']); ?></p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Current Members Card -->
                <div class="card border-left-info shadow mb-4">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0 text-info">👥 Current Members (<?php echo count($members); ?>)</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($members)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> No members found for this household.
                        </div>
                        <?php else: ?>
                        <form action="edit_household_members.php?id=<?php echo $household_id; ?>" method="POST">
                            <input type="hidden" name="action" value="update_relations">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>👤 Name</th>
                                            <th>⚧️ Gender</th>
                                            <th>🎂 Age</th>
                                            <th>👪 Relationship</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($members as $index => $member): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if (!empty($member['profile_photo'])): ?>
                                                        <?php
                                                        // Try to find the profile photo in different locations
                                                        $photo_url = '';

                                                        // Check if it's a full path or just a filename
                                                        if (strpos($member['profile_photo'], '/') !== false) {
                                                            // It's a path, use it directly
                                                            $photo_url = $member['profile_photo'];
                                                        } else {
                                                            // It's just a filename, assume it's in the residents folder
                                                            $photo_url = 'uploads/residents/' . $member['profile_photo'];
                                                        }
                                                        ?>
                                                        <img src="../../<?php echo $photo_url; ?>" alt="Profile" class="profile-img-sm me-2" onerror="this.onerror=null; this.src='../../assets/img/default-profile.jpg'; this.style.opacity=0.6;">
                                                    <?php else: ?>
                                                    <div class="me-2">👤</div>
                                                    <?php endif; ?>
                                                    <?php echo htmlspecialchars($member['full_name']); ?>
                                                    <input type="hidden" name="member_ids[]" value="<?php echo $member['resident_id']; ?>">
                                                </div>
                                            </td>
                                            <td><?php echo htmlspecialchars($member['gender']); ?></td>
                                            <td><?php echo $member['age']; ?></td>
                                            <td>
                                                <?php if ($member['resident_id'] == $household['household_head_id']): ?>
                                                <span class="badge badge-relationship badge-head">Head</span>
                                                <input type="hidden" name="relations[]" value="Head">
                                                <?php else: ?>
                                                <select class="form-select form-select-sm" name="relations[]">
                                                    <option value="Spouse" <?php echo ($member['relation_to_head'] == 'Spouse') ? 'selected' : ''; ?>>Spouse</option>
                                                    <option value="Son" <?php echo ($member['relation_to_head'] == 'Son') ? 'selected' : ''; ?>>Son</option>
                                                    <option value="Daughter" <?php echo ($member['relation_to_head'] == 'Daughter') ? 'selected' : ''; ?>>Daughter</option>
                                                    <option value="Father" <?php echo ($member['relation_to_head'] == 'Father') ? 'selected' : ''; ?>>Father</option>
                                                    <option value="Mother" <?php echo ($member['relation_to_head'] == 'Mother') ? 'selected' : ''; ?>>Mother</option>
                                                    <option value="Brother" <?php echo ($member['relation_to_head'] == 'Brother') ? 'selected' : ''; ?>>Brother</option>
                                                    <option value="Sister" <?php echo ($member['relation_to_head'] == 'Sister') ? 'selected' : ''; ?>>Sister</option>
                                                    <option value="Grandfather" <?php echo ($member['relation_to_head'] == 'Grandfather') ? 'selected' : ''; ?>>Grandfather</option>
                                                    <option value="Grandmother" <?php echo ($member['relation_to_head'] == 'Grandmother') ? 'selected' : ''; ?>>Grandmother</option>
                                                    <option value="Grandson" <?php echo ($member['relation_to_head'] == 'Grandson') ? 'selected' : ''; ?>>Grandson</option>
                                                    <option value="Granddaughter" <?php echo ($member['relation_to_head'] == 'Granddaughter') ? 'selected' : ''; ?>>Granddaughter</option>
                                                    <option value="Uncle" <?php echo ($member['relation_to_head'] == 'Uncle') ? 'selected' : ''; ?>>Uncle</option>
                                                    <option value="Aunt" <?php echo ($member['relation_to_head'] == 'Aunt') ? 'selected' : ''; ?>>Aunt</option>
                                                    <option value="Cousin" <?php echo ($member['relation_to_head'] == 'Cousin') ? 'selected' : ''; ?>>Cousin</option>
                                                    <option value="Nephew" <?php echo ($member['relation_to_head'] == 'Nephew') ? 'selected' : ''; ?>>Nephew</option>
                                                    <option value="Niece" <?php echo ($member['relation_to_head'] == 'Niece') ? 'selected' : ''; ?>>Niece</option>
                                                    <option value="In-law" <?php echo ($member['relation_to_head'] == 'In-law') ? 'selected' : ''; ?>>In-law</option>
                                                    <option value="Other" <?php echo ($member['relation_to_head'] == 'Other') ? 'selected' : ''; ?>>Other</option>
                                                </select>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($member['resident_id'] != $household['household_head_id']): ?>
                                                <button type="button" class="btn btn-sm btn-danger"
                                                        onclick="confirmRemoveMember(<?php echo $member['resident_id']; ?>, '<?php echo addslashes($member['full_name']); ?>')">
                                                    <i class="fas fa-trash"></i> Remove
                                                </button>
                                                <?php else: ?>
                                                <span class="text-muted">Cannot remove head</span>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <div class="text-end mt-3">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Update Relationships
                                </button>
                            </div>
                        </form>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Add New Members Card -->
                <div class="card border-left-success shadow mb-4">
                    <div class="card-header bg-light">
                        <h5 class="card-title mb-0 text-success">➕ Add New Members</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($available_residents)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> No available residents to add. All active residents are already assigned to households.
                        </div>
                        <?php else: ?>
                        <form action="edit_household_members.php?id=<?php echo $household_id; ?>" method="POST">
                            <input type="hidden" name="action" value="add_members">

                            <div id="newMembersContainer">
                                <div class="row member-row">
                                    <div class="col-md-5">
                                        <label for="new_member_1" class="form-label">Select Resident</label>
                                        <select class="form-select new-member-select" id="new_member_1" name="new_members[]" required>
                                            <option value="">-- Select Resident --</option>
                                            <?php foreach ($available_residents as $resident): ?>
                                            <option value="<?php echo $resident['resident_id']; ?>">
                                                <?php echo htmlspecialchars($resident['full_name']); ?>
                                                (<?php echo $resident['gender']; ?>, <?php echo $resident['age']; ?> yrs)
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="col-md-5">
                                        <label for="new_relation_1" class="form-label">Relationship to Head</label>
                                        <select class="form-select" id="new_relation_1" name="new_relations[]" required>
                                            <option value="">-- Select Relationship --</option>
                                            <option value="Spouse">Spouse</option>
                                            <option value="Son">Son</option>
                                            <option value="Daughter">Daughter</option>
                                            <option value="Father">Father</option>
                                            <option value="Mother">Mother</option>
                                            <option value="Brother">Brother</option>
                                            <option value="Sister">Sister</option>
                                            <option value="Grandfather">Grandfather</option>
                                            <option value="Grandmother">Grandmother</option>
                                            <option value="Grandson">Grandson</option>
                                            <option value="Granddaughter">Granddaughter</option>
                                            <option value="Uncle">Uncle</option>
                                            <option value="Aunt">Aunt</option>
                                            <option value="Cousin">Cousin</option>
                                            <option value="Nephew">Nephew</option>
                                            <option value="Niece">Niece</option>
                                            <option value="In-law">In-law</option>
                                            <option value="Other">Other</option>
                                        </select>
                                    </div>
                                    <div class="col-md-2 d-flex align-items-end">
                                        <button type="button" class="btn btn-danger remove-row-btn" style="display: none;">
                                            <i class="fas fa-times"></i> Remove
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <button type="button" id="addRowBtn" class="btn btn-secondary">
                                        <i class="fas fa-plus"></i> Add Another Member
                                    </button>
                                </div>
                            </div>

                            <div class="text-end mt-4">
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-user-plus"></i> Add Members to Household
                                </button>
                            </div>
                        </form>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Remove Member Confirmation Modal -->
    <div class="modal fade" id="removeMemberModal" tabindex="-1" aria-labelledby="removeMemberModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="removeMemberModalLabel">⚠️ Confirm Remove Member</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    Are you sure you want to remove <strong id="memberNameToRemove"></strong> from this household?
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">❌ Cancel</button>
                    <form id="removeMemberForm" action="remove_member.php" method="POST">
                        <input type="hidden" name="household_id" value="<?php echo $household_id; ?>">
                        <input type="hidden" id="residentIdToRemove" name="resident_id" value="">
                        <button type="submit" class="btn btn-danger">🗑️ Remove Member</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize Select2 for resident dropdowns
            $('.new-member-select').select2({
                theme: 'bootstrap-5',
                placeholder: 'Select a resident'
            });

            // Add new member row
            $('#addRowBtn').on('click', function() {
                // Clone the first row
                const newRow = $('#newMembersContainer .member-row:first').clone();

                // Update IDs and clear values
                const rowIndex = $('#newMembersContainer .member-row').length + 1;
                newRow.find('select').val('').attr('id', function(i, val) {
                    return val.replace('_1', '_' + rowIndex);
                });

                // Show remove button
                newRow.find('.remove-row-btn').show();

                // Add to container
                $('#newMembersContainer').append(newRow);

                // Initialize Select2 for the new dropdown
                newRow.find('.new-member-select').select2({
                    theme: 'bootstrap-5',
                    placeholder: 'Select a resident'
                });

                // Add event listener to remove button
                newRow.find('.remove-row-btn').on('click', function() {
                    $(this).closest('.member-row').remove();
                });
            });

            // Show remove button for rows after the first one
            if ($('#newMembersContainer .member-row').length > 1) {
                $('#newMembersContainer .member-row:not(:first) .remove-row-btn').show();
            }

            // Add event listeners to existing remove buttons
            $('.remove-row-btn').on('click', function() {
                $(this).closest('.member-row').remove();
            });
        });

        // Confirm remove member
        function confirmRemoveMember(residentId, residentName) {
            document.getElementById('residentIdToRemove').value = residentId;
            document.getElementById('memberNameToRemove').textContent = residentName;

            const modal = new bootstrap.Modal(document.getElementById('removeMemberModal'));
            modal.show();
        }
    </script>
</body>
</html>
