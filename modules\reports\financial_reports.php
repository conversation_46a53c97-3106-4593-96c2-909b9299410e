<?php
include '../../includes/functions/permission_functions.php';
// Start session
session_start();

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

try {
    // Include necessary files
    require_once '../../includes/config/config.php';
    require_once '../../includes/config/database.php';
    require_once '../../includes/functions/utility.php';
    
    // Check if user is logged in
    if (!isset($_SESSION['user_id'])) {
        header("Location: ../../login.php");
        exit();
    }
    
    // Check if user has permission for financial reports
    if (!hasPermission('view_reports') && !hasPermission('admin')) {
        header("Location: ../../index.php");
        exit();
    }
    
    // Set page title and module info
    $page_title = "Financial Reports";
    $module_name = "Reports";
    $sub_module = "Financial";
    
    // Initialize variables
    $message = "";
    $message_type = "";
    $selected_year = isset($_GET['year']) ? $_GET['year'] : date('Y');
    $selected_month = isset($_GET['month']) ? $_GET['month'] : '';
    
    // Connect to database
    $conn = new PDO("mysql:host=$db_host;dbname=$db_name", $db_user, $db_pass);
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Initialize financial summary variables with default values
    $total_budget = 0;
    $total_expenditure = 0;
    $total_revenue = 0;
    $current_balance = 0;
    $available_years = [date('Y')]; // Initialize with current year
    $budget_by_category = [];

    // Check if required tables exist
    $tables_exist = true;
    $missing_tables = [];
    $required_tables = ['finances'];

    try {
        foreach ($required_tables as $table) {
            $stmt = $conn->prepare("SHOW TABLES LIKE :table");
            $stmt->bindParam(':table', $table);
            $stmt->execute();
            
            if ($stmt->rowCount() == 0) {
                $tables_exist = false;
                $missing_tables[] = $table;
            }
        }
        
        // If finances table exists, load financial data
        if (count($missing_tables) == 0) {
            // Get available years
            try {
                $year_query = "SELECT DISTINCT YEAR(transaction_date) as year FROM finances ORDER BY year DESC";
                $year_stmt = $conn->query($year_query);
                $available_years = $year_stmt->fetchAll(PDO::FETCH_COLUMN);
                
                if (empty($available_years)) {
                    $available_years = [date('Y')];
                }
            } catch (PDOException $e) {
                error_log("Error fetching years: " . $e->getMessage());
                $available_years = [date('Y')];
            }
            
            // Get budget summary - Assuming total income is used as total budget
            try {
                // For simplicity, we'll use total income as the "budget"
                $budget_query = "SELECT SUM(amount) as total FROM finances 
                                WHERE transaction_type = 'Income' AND YEAR(transaction_date) = :year";
                $budget_stmt = $conn->prepare($budget_query);
                $budget_stmt->bindParam(':year', $selected_year);
                $budget_stmt->execute();
                $total_budget = $budget_stmt->fetchColumn() ?: 0;
                
                // Get budget by category (income by category)
                $category_query = "SELECT category, SUM(amount) as total FROM finances 
                                  WHERE transaction_type = 'Income' AND YEAR(transaction_date) = :year 
                                  GROUP BY category";
                $category_stmt = $conn->prepare($category_query);
                $category_stmt->bindParam(':year', $selected_year);
                $category_stmt->execute();
                $budget_by_category = $category_stmt->fetchAll(PDO::FETCH_ASSOC);
            } catch (PDOException $e) {
                error_log("Error fetching budget data: " . $e->getMessage());
            }
            
            // Get expenditure
            try {
                $expenditure_query = "SELECT SUM(amount) as total FROM finances 
                                    WHERE transaction_type = 'Expense' AND YEAR(transaction_date) = :year";
                $expenditure_stmt = $conn->prepare($expenditure_query);
                $expenditure_stmt->bindParam(':year', $selected_year);
                $expenditure_stmt->execute();
                $total_expenditure = $expenditure_stmt->fetchColumn() ?: 0;
            } catch (PDOException $e) {
                error_log("Error fetching expenditure data: " . $e->getMessage());
            }
            
            // Get revenue
            try {
                $revenue_query = "SELECT SUM(amount) as total FROM finances 
                                WHERE transaction_type = 'Income' AND YEAR(transaction_date) = :year";
                $revenue_stmt = $conn->prepare($revenue_query);
                $revenue_stmt->bindParam(':year', $selected_year);
                $revenue_stmt->execute();
                $total_revenue = $revenue_stmt->fetchColumn() ?: 0;
            } catch (PDOException $e) {
                error_log("Error fetching revenue data: " . $e->getMessage());
            }
            
            // Calculate balance
            $current_balance = $total_revenue - $total_expenditure;
        } else {
            // Set placeholder data for demo purposes
            $total_budget = 1000000;
            $total_expenditure = 800000;
            $total_revenue = 850000;
            $current_balance = $total_revenue - $total_expenditure;
        }
    } catch (PDOException $e) {
        // Don't show database errors to users
        error_log("Error retrieving financial data: " . $e->getMessage());
        $message = "The financial reporting system is currently being set up. Please check back later.";
        $message_type = "info";
        
        // Initialize with empty data
        $total_budget = 0;
        $total_expenditure = 0;
        $total_revenue = 0;
        $current_balance = 0;
        $tables_exist = false;
        $missing_tables = [];
    }

    // Include header
    include_once '../../includes/header.php';
} catch (Exception $e) {
    // Handle any exceptions
    $message = "An error occurred: " . $e->getMessage();
    $message_type = "danger";
    
    // Try to include header even if there was an error
    try {
        include_once '../../includes/header.php';
    } catch (Exception $headerEx) {
        // If header include fails, provide a minimal HTML structure
        echo '<!DOCTYPE html>
        <html>
        <head>
            <title>Financial Reports</title>
            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
            <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
        </head>
        <body>
            <div class="container mt-4">
                <h1>Financial Reports</h1>';
    }
}
?>

<style>
    /* Stat Card Styles */
    .financial-card {
        border-radius: 0.75rem;
        overflow: hidden;
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
        position: relative;
        z-index: 1;
    }
    .financial-card:hover {
        transform: translateY(-5px) !important;
        box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2) !important;
        z-index: 2;
    }
    
    /* Quick stats cards specific styling */
    .financial-card .card-icon {
        font-size: 2.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 60px;
        width: 60px;
        border-radius: 50%;
        transition: transform 0.3s ease;
    }
    .financial-card:hover .card-icon {
        transform: scale(1.1);
    }
    
    /* Chart card hover effects */
    .chart-card {
        transition: all 0.3s ease;
        border-radius: 0.75rem;
        overflow: hidden;
        border: none;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
    }
    .chart-card:hover {
        transform: translateY(-5px) !important;
        box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2) !important;
    }
    
    /* Table hover effects */
    .table-card {
        transition: all 0.3s ease;
        border-radius: 0.75rem;
        overflow: hidden;
        border: none;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
    }
    .table-card:hover {
        transform: translateY(-5px) !important;
        box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2) !important;
    }
    
    /* Background colors with opacity */
    .bg-primary-soft { background-color: rgba(78, 115, 223, 0.1) !important; }
    .bg-success-soft { background-color: rgba(28, 200, 138, 0.1) !important; }
    .bg-info-soft { background-color: rgba(54, 185, 204, 0.1) !important; }
    .bg-warning-soft { background-color: rgba(246, 194, 62, 0.1) !important; }
    .bg-danger-soft { background-color: rgba(231, 74, 59, 0.1) !important; }
    
    .chart-container {
        position: relative;
        height: 300px;
        margin-bottom: 20px;
    }
    @media (max-width: 768px) {
        .chart-container {
            height: 250px;
        }
    }
</style>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <?php include_once '../../includes/sidebar.php'; ?>
        
        <!-- Main Content -->
        <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-chart-line me-2"></i> Financial Reports</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <form action="" method="GET" class="d-flex">
                        <select class="form-select me-2" name="year" onchange="this.form.submit()">
                            <?php foreach($available_years as $year): ?>
                                <option value="<?php echo $year; ?>" <?php echo ($year == $selected_year) ? 'selected' : ''; ?>>
                                    <?php echo $year; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print"></i> Print Report
                        </button>
                    </form>
                </div>
            </div>

            <?php if (!empty($message)): ?>
                <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                    <i class="fas fa-info-circle me-2"></i> <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (!$tables_exist || count($missing_tables) > 0): ?>
                <div class="alert alert-info mb-4">
                    <h5><i class="fas fa-info-circle me-2"></i> Financial Module Information</h5>
                    <p>The financial management system is still being set up. The report currently shows placeholder data.</p>
                    <p>This report provides financial insights based on your transactions in the finances table.</p>
                    <p><strong>Note:</strong> Add some financial records in the Budget & Expenses section to see real data in this report.</p>
                </div>
            <?php endif; ?>

            <!-- Financial Summary Cards -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card financial-card shadow h-100">
                        <div class="card-body p-3">
                            <div class="row align-items-center">
                                <div class="col-4">
                                    <div class="card-icon bg-primary-soft text-primary">
                                        💰
                                    </div>
                                </div>
                                <div class="col-8 text-end">
                                    <h4 class="mt-0 mb-1">₱<?php echo number_format($total_budget, 2); ?></h4>
                                    <p class="mb-0 text-muted">Total Budget</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card financial-card shadow h-100">
                        <div class="card-body p-3">
                            <div class="row align-items-center">
                                <div class="col-4">
                                    <div class="card-icon bg-success-soft text-success">
                                        💵
                                    </div>
                                </div>
                                <div class="col-8 text-end">
                                    <h4 class="mt-0 mb-1">₱<?php echo number_format($total_revenue, 2); ?></h4>
                                    <p class="mb-0 text-muted">Total Revenue</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card financial-card shadow h-100">
                        <div class="card-body p-3">
                            <div class="row align-items-center">
                                <div class="col-4">
                                    <div class="card-icon bg-danger-soft text-danger">
                                        📉
                                    </div>
                                </div>
                                <div class="col-8 text-end">
                                    <h4 class="mt-0 mb-1">₱<?php echo number_format($total_expenditure, 2); ?></h4>
                                    <p class="mb-0 text-muted">Total Expenditure</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card financial-card shadow h-100">
                        <div class="card-body p-3">
                            <div class="row align-items-center">
                                <div class="col-4">
                                    <div class="card-icon bg-info-soft text-info">
                                        ⚖️
                                    </div>
                                </div>
                                <div class="col-8 text-end">
                                    <?php 
                                    $text_class = $current_balance >= 0 ? 'text-success' : 'text-danger';
                                    ?>
                                    <h4 class="mt-0 mb-1 <?php echo $text_class; ?>">₱<?php echo number_format($current_balance, 2); ?></h4>
                                    <p class="mb-0 text-muted">Balance</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <?php
            // Initialize chart data variables
            $monthly_labels = [];
            $revenue_data = [];
            $expenditure_data = [];
            $budget_labels = [];
            $budget_data = [];
            $budget_colors = [];

            if (count($missing_tables) == 0 && $tables_exist) {
                // Get real data for charts
                $monthly_labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                
                try {
                    // Get monthly revenue data
                    $monthly_revenue_query = "SELECT MONTH(transaction_date) as month, SUM(amount) as total 
                                             FROM finances 
                                             WHERE transaction_type = 'Income' AND YEAR(transaction_date) = :year 
                                             GROUP BY MONTH(transaction_date)";
                    $monthly_revenue_stmt = $conn->prepare($monthly_revenue_query);
                    $monthly_revenue_stmt->bindParam(':year', $selected_year);
                    $monthly_revenue_stmt->execute();
                    
                    // Initialize with zeros
                    $revenue_data = array_fill(0, 12, 0);
                    
                    // Fill in actual data
                    while ($row = $monthly_revenue_stmt->fetch(PDO::FETCH_ASSOC)) {
                        $month_index = intval($row['month']) - 1; // Convert to 0-based index
                        $revenue_data[$month_index] = floatval($row['total']);
                    }
                    
                    // Get monthly expenditure data
                    $monthly_expenditure_query = "SELECT MONTH(transaction_date) as month, SUM(amount) as total 
                                                FROM finances 
                                                WHERE transaction_type = 'Expense' AND YEAR(transaction_date) = :year 
                                                GROUP BY MONTH(transaction_date)";
                    $monthly_expenditure_stmt = $conn->prepare($monthly_expenditure_query);
                    $monthly_expenditure_stmt->bindParam(':year', $selected_year);
                    $monthly_expenditure_stmt->execute();
                    
                    // Initialize with zeros
                    $expenditure_data = array_fill(0, 12, 0);
                    
                    // Fill in actual data
                    while ($row = $monthly_expenditure_stmt->fetch(PDO::FETCH_ASSOC)) {
                        $month_index = intval($row['month']) - 1; // Convert to 0-based index
                        $expenditure_data[$month_index] = floatval($row['total']);
                    }
                    
                    // Get budget allocation data for pie chart (using income categories)
                    if (!empty($budget_by_category)) {
                        foreach ($budget_by_category as $item) {
                            $budget_labels[] = $item['category'];
                            $budget_data[] = floatval($item['total']);
                        }
                    } else {
                        // If no categories are found, let's get income categories from finances table
                        $category_query = "SELECT category, SUM(amount) as total 
                                         FROM finances 
                                         WHERE transaction_type = 'Income' AND YEAR(transaction_date) = :year 
                                         GROUP BY category";
                        $category_stmt = $conn->prepare($category_query);
                        $category_stmt->bindParam(':year', $selected_year);
                        $category_stmt->execute();
                        
                        while ($row = $category_stmt->fetch(PDO::FETCH_ASSOC)) {
                            $budget_labels[] = $row['category'];
                            $budget_data[] = floatval($row['total']);
                        }
                    }
                    
                    // Generate colors if we have budget data
                    if (!empty($budget_data)) {
                        $budget_colors = [
                            'rgba(255, 99, 132, 0.8)',
                            'rgba(54, 162, 235, 0.8)',
                            'rgba(255, 206, 86, 0.8)',
                            'rgba(75, 192, 192, 0.8)',
                            'rgba(153, 102, 255, 0.8)',
                            'rgba(255, 159, 64, 0.8)',
                            'rgba(255, 99, 132, 0.8)',
                            'rgba(54, 162, 235, 0.8)',
                            'rgba(255, 206, 86, 0.8)',
                            'rgba(75, 192, 192, 0.8)'
                        ];
                        
                        // Ensure we have enough colors
                        while (count($budget_colors) < count($budget_labels)) {
                            $budget_colors = array_merge($budget_colors, $budget_colors);
                        }
                        
                        // Trim to match the number of categories
                        $budget_colors = array_slice($budget_colors, 0, count($budget_labels));
                    }
                } catch (PDOException $e) {
                    error_log("Error fetching chart data: " . $e->getMessage());
                    
                    // Use placeholder data as fallback
                    $monthly_labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                    $revenue_data = [5000, 6200, 7500, 6800, 7200, 8500, 9200, 8700, 7800, 8100, 9500, 10000];
                    $expenditure_data = [4500, 5800, 7000, 6500, 6800, 7900, 8500, 8200, 7500, 7800, 8800, 9200];
                    
                    $budget_labels = ['Infrastructure', 'Healthcare', 'Education', 'Security', 'Administration', 'Community Events'];
                    $budget_data = [30, 20, 15, 10, 15, 10];
                    $budget_colors = [
                        'rgba(255, 99, 132, 0.8)',
                        'rgba(54, 162, 235, 0.8)',
                        'rgba(255, 206, 86, 0.8)',
                        'rgba(75, 192, 192, 0.8)',
                        'rgba(153, 102, 255, 0.8)',
                        'rgba(255, 159, 64, 0.8)'
                    ];
                }
            } else {
                // Placeholder data for charts when no tables exist
                $monthly_labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
                $revenue_data = [5000, 6200, 7500, 6800, 7200, 8500, 9200, 8700, 7800, 8100, 9500, 10000];
                $expenditure_data = [4500, 5800, 7000, 6500, 6800, 7900, 8500, 8200, 7500, 7800, 8800, 9200];
                
                $budget_labels = ['Infrastructure', 'Healthcare', 'Education', 'Security', 'Administration', 'Community Events'];
                $budget_data = [30, 20, 15, 10, 15, 10];
                $budget_colors = [
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 206, 86, 0.8)',
                    'rgba(75, 192, 192, 0.8)',
                    'rgba(153, 102, 255, 0.8)',
                    'rgba(255, 159, 64, 0.8)'
                ];
            }
            ?>

            <!-- Charts Row -->
            <div class="row">
                <div class="col-lg-6 mb-4">
                    <div class="card chart-card shadow mb-4">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">Monthly Revenue vs Expenditure (<?php echo $selected_year; ?>)</h6>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="revenueVsExpChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-6 mb-4">
                    <div class="card chart-card shadow mb-4">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">Budget Allocation by Category</h6>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="budgetAllocationChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Budget Details Table -->
            <div class="card table-card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Budget Details</h6>
                </div>
                <div class="card-body">
                    <?php if (!$tables_exist || count($missing_tables) > 0): ?>
                        <div class="alert alert-info">
                            <p><i class="fas fa-info-circle me-2"></i> Placeholder budget allocation data is shown below.</p>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-bordered" id="budgetTable" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Category</th>
                                        <th>Amount</th>
                                        <th>Percentage</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Infrastructure</td>
                                        <td>₱300,000.00</td>
                                        <td>30%</td>
                                        <td><span class="badge bg-primary">Active</span></td>
                                    </tr>
                                    <tr>
                                        <td>Healthcare</td>
                                        <td>₱200,000.00</td>
                                        <td>20%</td>
                                        <td><span class="badge bg-primary">Active</span></td>
                                    </tr>
                                    <tr>
                                        <td>Education</td>
                                        <td>₱150,000.00</td>
                                        <td>15%</td>
                                        <td><span class="badge bg-primary">Active</span></td>
                                    </tr>
                                    <tr>
                                        <td>Security</td>
                                        <td>₱100,000.00</td>
                                        <td>10%</td>
                                        <td><span class="badge bg-primary">Active</span></td>
                                    </tr>
                                    <tr>
                                        <td>Administration</td>
                                        <td>₱150,000.00</td>
                                        <td>15%</td>
                                        <td><span class="badge bg-primary">Active</span></td>
                                    </tr>
                                    <tr>
                                        <td>Community Events</td>
                                        <td>₱100,000.00</td>
                                        <td>10%</td>
                                        <td><span class="badge bg-primary">Active</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <!-- Actual budget data table -->
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Category</th>
                                        <th>Amount (₱)</th>
                                        <th>Status</th>
                                        <th>% of Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($budget_by_category)): ?>
                                        <?php foreach ($budget_by_category as $item): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($item['category']); ?></td>
                                                <td>₱<?php echo number_format($item['total'], 2); ?></td>
                                                <td>
                                                    <?php 
                                                        // This is a placeholder - in a real implementation you would fetch the status
                                                        echo '<span class="badge bg-success">Approved</span>';
                                                    ?>
                                                </td>
                                                <td>
                                                    <?php 
                                                        $percentage = ($total_budget > 0) 
                                                            ? ($item['total'] / $total_budget) * 100 
                                                            : 0;
                                                        echo number_format($percentage, 1) . '%';
                                                    ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="4" class="text-center">No budget data available for <?php echo $selected_year; ?></td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Print Button -->
            <div class="text-end mb-4">
                <button class="btn btn-primary" onclick="window.print()">
                    <i class="fas fa-print me-2"></i> Print Report
                </button>
            </div>

           
        </main>
    </div>
</div>

<!-- Include Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Revenue vs Expenditure Chart
        var revenueVsExpCtx = document.getElementById('revenueVsExpChart').getContext('2d');
        var revenueVsExpChart = new Chart(revenueVsExpCtx, {
            type: 'line',
            data: {
                labels: <?php echo json_encode($monthly_labels); ?>,
                datasets: [{
                    label: 'Revenue',
                    data: <?php echo json_encode($revenue_data); ?>,
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 2,
                    tension: 0.3
                }, {
                    label: 'Expenditure',
                    data: <?php echo json_encode($expenditure_data); ?>,
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    borderColor: 'rgba(255, 99, 132, 1)',
                    borderWidth: 2,
                    tension: 0.3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '₱' + value.toLocaleString();
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                var label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                if (context.parsed.y !== null) {
                                    label += '₱' + context.parsed.y.toLocaleString();
                                }
                                return label;
                            }
                        }
                    }
                }
            }
        });

        // Budget Allocation Chart
        var budgetAllocationCtx = document.getElementById('budgetAllocationChart').getContext('2d');
        var budgetAllocationChart = new Chart(budgetAllocationCtx, {
            type: 'pie',
            data: {
                labels: <?php echo json_encode($budget_labels); ?>,
                datasets: [{
                    data: <?php echo json_encode($budget_data); ?>,
                    backgroundColor: <?php echo json_encode($budget_colors); ?>,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                var label = context.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                label += context.parsed.toFixed(1) + '%';
                                return label;
                            }
                        }
                    }
                }
            }
        });
    });
</script>

<?php include_once '../../includes/footer.php'; ?> 