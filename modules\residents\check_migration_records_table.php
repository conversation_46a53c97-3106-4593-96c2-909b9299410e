<?php
include '../../includes/functions/permission_functions.php';
// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// For direct script execution
if (!isset($_SESSION)) {
    session_start();
}

// Determine if running from command line
$is_cli = php_sapi_name() === 'cli';

// Adjust include paths based on execution environment
if ($is_cli) {
    // When running from command line in the project root
    require_once __DIR__ . '/../../includes/config/database.php';
    require_once __DIR__ . '/../../includes/functions/utility.php';
} else {
    // When running from browser
    require_once '../../includes/config/database.php';
    require_once '../../includes/functions/utility.php';
}

// Skip permission checks when running the script directly
$direct_execution = ($is_cli || isset($_GET['direct']));

// Check permissions only when not in direct execution mode
if (!$direct_execution && (!isset($_SESSION['user_id']) || !hasPermission('manage_system'))) {
    echo "Unauthorized access.";
    exit;
}

echo "<h1>Migration Records Table Test</h1>";

if (!isset($_SESSION['user_id'])) {
    echo "<p>Please login first to run tests.</p>";
    echo "<p><a href='../../login.php'>Go to Login Page</a></p>";
    exit;
}

// Check if we need to create a test record
if (isset($_GET['create_test'])) {
    echo "<h2>Creating Test Migration Record</h2>";
    
    try {
        // First, get a valid resident ID
        $resident_query = "SELECT resident_id, first_name, last_name FROM residents WHERE status = 'Active' LIMIT 1";
        $resident_stmt = $conn->query($resident_query);
        $resident = $resident_stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$resident) {
            echo "<p>Error: No active residents found in the database. Please add a resident first.</p>";
            exit;
        }
        
        // Second, get a valid official ID (if any exists)
        $official_query = "SELECT o.official_id, r.first_name, r.last_name 
                          FROM officials o 
                          JOIN residents r ON o.resident_id = r.resident_id 
                          LIMIT 1";
        $official_stmt = $conn->query($official_query);
        $official = $official_stmt->fetch(PDO::FETCH_ASSOC);
        
        // Begin transaction
        $conn->beginTransaction();
        
        // Create test data
        $migration_type = "Arrival";
        $origin_address = "Test Origin Address " . date('Y-m-d H:i:s');
        $destination_address = "Test Destination Address " . date('Y-m-d H:i:s');
        $migration_date = date('Y-m-d');
        $reason = "Test migration record with recorded_by";
        $remarks = "Test record created on " . date('Y-m-d H:i:s');
        
        // Use official_id if found, otherwise null
        $recorded_by = $official ? $official['official_id'] : null;
        
        // Insert the test record
        $insert_query = "INSERT INTO migration_records (
            resident_id, migration_type, origin_address, destination_address,
            migration_date, reason, remarks, recorded_by
        ) VALUES (
            :resident_id, :migration_type, :origin_address, :destination_address,
            :migration_date, :reason, :remarks, :recorded_by
        )";
        
        $insert_stmt = $conn->prepare($insert_query);
        $insert_stmt->bindParam(':resident_id', $resident['resident_id'], PDO::PARAM_INT);
        $insert_stmt->bindParam(':migration_type', $migration_type, PDO::PARAM_STR);
        $insert_stmt->bindParam(':origin_address', $origin_address, PDO::PARAM_STR);
        $insert_stmt->bindParam(':destination_address', $destination_address, PDO::PARAM_STR);
        $insert_stmt->bindParam(':migration_date', $migration_date, PDO::PARAM_STR);
        $insert_stmt->bindParam(':reason', $reason, PDO::PARAM_STR);
        $insert_stmt->bindParam(':remarks', $remarks, PDO::PARAM_STR);
        $insert_stmt->bindParam(':recorded_by', $recorded_by, PDO::PARAM_INT);
        
        $insert_stmt->execute();
        $record_id = $conn->lastInsertId();
        
        // Commit transaction
        $conn->commit();
        
        echo "<div style='padding: 10px; background-color: #dff0d8; border: 1px solid #d6e9c6; color: #3c763d; margin-bottom: 20px;'>";
        echo "<p>Test migration record created successfully!</p>";
        echo "<p>Record ID: {$record_id}</p>";
        echo "<p>Resident: {$resident['first_name']} {$resident['last_name']} (ID: {$resident['resident_id']})</p>";
        echo "<p>Migration Type: {$migration_type}</p>";
        echo "<p>Recorded By: " . ($official ? "{$official['first_name']} {$official['last_name']} (ID: {$official['official_id']})" : "NULL (System)") . "</p>";
        echo "</div>";
        
        echo "<p><a href='migration_records.php' class='btn btn-primary'>View Migration Records</a></p>";
        
    } catch (PDOException $e) {
        // Rollback transaction on error
        if ($conn->inTransaction()) {
            $conn->rollBack();
        }
        
        echo "<div style='padding: 10px; background-color: #f2dede; border: 1px solid #ebccd1; color: #a94442; margin-bottom: 20px;'>";
        echo "<p>Error creating test record: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    
    exit;
}

try {
    echo "Checking migration_records table...\n";
    
    // Check if migration_records table exists
    $table_exists = false;
    
    // For MySQL
    $check_table_query = "SELECT COUNT(*) as count FROM information_schema.tables 
                         WHERE table_schema = DATABASE() 
                         AND table_name = 'migration_records'";
    $check_table_stmt = $conn->prepare($check_table_query);
    $check_table_stmt->execute();
    $result = $check_table_stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($result['count'] > 0) {
        $table_exists = true;
        echo "The migration_records table already exists in the database.\n";
    }
    
    // Create table if it doesn't exist
    if (!$table_exists) {
        // Create the migration_records table
        $create_table_sql = "CREATE TABLE migration_records (
            record_id INT AUTO_INCREMENT PRIMARY KEY,
            resident_id INT NOT NULL,
            migration_type ENUM('Arrival', 'Departure', 'Transfer') NOT NULL,
            origin_address VARCHAR(255),
            destination_address VARCHAR(255),
            migration_date DATE NOT NULL,
            reason VARCHAR(255),
            remarks TEXT,
            recorded_by INT,
            date_recorded TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (resident_id) REFERENCES residents(resident_id) ON DELETE CASCADE
        )";
        
        $conn->exec($create_table_sql);
        echo "Successfully created the migration_records table.\n";
        
        // Create some test data if needed
        $insert_test_data = false; // Change to true if you want to add test data
        
        if ($insert_test_data) {
            // Get some residents to create migration records
            $residents_query = "SELECT resident_id, first_name, last_name 
                              FROM residents 
                              WHERE status = 'Active' 
                              ORDER BY resident_id 
                              LIMIT 10";
            $residents_stmt = $conn->query($residents_query);
            $residents = $residents_stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (count($residents) >= 2) {
                // Sample migration types
                $migration_types = ['Arrival', 'Departure', 'Transfer'];
                $reasons = [
                    'Employment opportunity', 
                    'Family relocation', 
                    'Education', 
                    'Marriage', 
                    'Better living conditions'
                ];
                
                // Create some migration records
                $conn->beginTransaction();
                
                foreach ($residents as $index => $resident) {
                    // Skip some residents to make it more realistic
                    if ($index % 3 != 0) continue;
                    
                    $migration_type = $migration_types[array_rand($migration_types)];
                    $reason = $reasons[array_rand($reasons)];
                    $origin = "Previous Address " . ($index + 1) . ", Old City";
                    $destination = "New Address " . ($index + 1) . ", New City";
                    
                    // Create dates between 1 and 2 years ago
                    $days_ago = rand(30, 730);
                    $migration_date = date('Y-m-d', strtotime("-$days_ago days"));
                    
                    $insert_query = "INSERT INTO migration_records 
                                   (resident_id, migration_type, origin_address, destination_address, 
                                   migration_date, reason, remarks, recorded_by)
                                   VALUES (:resident_id, :migration_type, :origin_address, 
                                   :destination_address, :migration_date, :reason, :remarks, :recorded_by)";
                    
                    $insert_stmt = $conn->prepare($insert_query);
                    $insert_stmt->bindParam(':resident_id', $resident['resident_id'], PDO::PARAM_INT);
                    $insert_stmt->bindParam(':migration_type', $migration_type, PDO::PARAM_STR);
                    $insert_stmt->bindParam(':origin_address', $origin, PDO::PARAM_STR);
                    $insert_stmt->bindParam(':destination_address', $destination, PDO::PARAM_STR);
                    $insert_stmt->bindParam(':migration_date', $migration_date, PDO::PARAM_STR);
                    $insert_stmt->bindParam(':reason', $reason, PDO::PARAM_STR);
                    
                    $remarks = "Test migration record for " . $resident['first_name'] . " " . $resident['last_name'];
                    $insert_stmt->bindParam(':remarks', $remarks, PDO::PARAM_STR);
                    
                    $recorded_by = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 1;
                    $insert_stmt->bindParam(':recorded_by', $recorded_by, PDO::PARAM_INT);
                    
                    $insert_stmt->execute();
                    
                    echo "Created migration record: {$resident['first_name']} {$resident['last_name']} - {$migration_type}\n";
                }
                
                $conn->commit();
                echo "Test data inserted successfully.\n";
            } else {
                echo "Not enough residents to create test migration records.\n";
            }
        }
    }
    
    // Check the structure of the migration_records table
    echo "<h2>Migration Records Table Structure</h2>";
    $describe_table_query = "DESCRIBE migration_records";
    $describe_table_stmt = $conn->prepare($describe_table_query);
    $describe_table_stmt->execute();
    $columns = $describe_table_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' cellpadding='5' cellspacing='0'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        foreach ($column as $key => $value) {
            echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
    
    // Create a form to create a test migration record
    echo "<h2>Create Test Migration Record</h2>";
    echo "<p>Click the button below to create a test migration record with recorder information:</p>";
    echo "<p><a href='?create_test=1' class='btn btn-success'>Create Test Record</a></p>";
    
    // Count existing records
    $count_query = "SELECT COUNT(*) as count FROM migration_records";
    $count_stmt = $conn->query($count_query);
    $count_result = $count_stmt->fetch(PDO::FETCH_ASSOC);
    
    echo "Current number of migration records in the table: {$count_result['count']}\n";
    
    echo "Migration records table setup is complete.\n";
    echo "Go to Migration Records Management at: " . ($is_cli ? "" : "<a href='migration_records.php'>") . "modules/residents/migration_records.php" . ($is_cli ? "" : "</a>") . "\n";
    
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?> 