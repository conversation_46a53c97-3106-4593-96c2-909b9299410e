<?php
session_start();
include '../../includes/config/database.php';
include '../../includes/functions/permission_functions.php';
include '../../includes/functions/utility.php';
include '../../includes/functions/log_function.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check permission
if (!hasPermission('delete_complaint')) {
    header("Location: ../../index.php");
    exit;
}

// Check if complaint ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = "No complaint ID provided.";
    header("Location: complaints.php");
    exit;
}

$complaint_id = (int)$_GET['id'];

// Verify the complaint exists and is in Pending status
try {
    $check_query = "SELECT status FROM complaints WHERE complaint_id = :complaint_id";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bindValue(':complaint_id', $complaint_id, PDO::PARAM_INT);
    $check_stmt->execute();
    
    if ($check_stmt->rowCount() == 0) {
        $_SESSION['error'] = "Complaint not found.";
        header("Location: complaints.php");
        exit;
    }
    
    $complaint = $check_stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($complaint['status'] != 'Pending') {
        $_SESSION['error'] = "Only pending complaints can be deleted.";
        header("Location: complaints.php");
        exit;
    }
    
    // Process deletion if form is submitted
    if (isset($_POST['confirm_delete']) && $_POST['confirm_delete'] == 'yes') {
        try {
            // Begin transaction
            $conn->beginTransaction();
            
            // Delete any related updates
            $delete_updates = "DELETE FROM complaint_updates WHERE complaint_id = :complaint_id";
            $updates_stmt = $conn->prepare($delete_updates);
            $updates_stmt->bindValue(':complaint_id', $complaint_id, PDO::PARAM_INT);
            $updates_stmt->execute();
            
            // Delete the complaint
            $delete_query = "DELETE FROM complaints WHERE complaint_id = :complaint_id";
            $delete_stmt = $conn->prepare($delete_query);
            $delete_stmt->bindValue(':complaint_id', $complaint_id, PDO::PARAM_INT);
            $delete_stmt->execute();
            
            // Log activity
            log_activity_safe($conn, $_SESSION['user_id'], 'Delete Complaint', "Deleted complaint #$complaint_id", 'complaints', $complaint_id);
            
            // Commit transaction
            $conn->commit();
            
            $_SESSION['success'] = "Complaint #$complaint_id has been deleted successfully.";
            header("Location: complaints.php");
            exit;
        } catch (PDOException $e) {
            // Rollback transaction on error
            $conn->rollBack();
            
            error_log("Error deleting complaint: " . $e->getMessage());
            $_SESSION['error'] = "Failed to delete complaint. Please try again.";
            header("Location: complaints.php");
            exit;
        }
    }
} catch (PDOException $e) {
    error_log("Error checking complaint status: " . $e->getMessage());
    $_SESSION['error'] = "Database error occurred. Please try again.";
    header("Location: complaints.php");
    exit;
}

// Page title
$page_title = "Delete Complaint - Barangay Management System";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../../assets/css/style.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Delete Complaint</h1>
                    <a href="complaints.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Complaints
                    </a>
                </div>
                
                <div class="card shadow mb-4">
                    <div class="card-header bg-danger text-white">
                        <h5 class="card-title mb-0">Confirm Deletion</h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i> Warning: This action cannot be undone!
                        </div>
                        
                        <p>Are you sure you want to delete Complaint #<?php echo $complaint_id; ?>?</p>
                        <p>All associated data including updates will be permanently removed from the system.</p>
                        
                        <form method="POST" action="">
                            <input type="hidden" name="confirm_delete" value="yes">
                            
                            <div class="mt-4">
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash"></i> Yes, Delete Complaint
                                </button>
                                <a href="complaints.php" class="btn btn-secondary ms-2">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</body>
</html> 