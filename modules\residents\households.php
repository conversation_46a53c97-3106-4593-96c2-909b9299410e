<?php
session_start();
include '../../includes/config/database.php';
include '../../includes/functions/utility.php';
include '../../includes/functions/permission_functions.php';

// Clear any lingering error messages if this is a fresh page load (not an AJAX request)
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) != 'xmlhttprequest') {
    if (isset($_SESSION['error'])) {
        unset($_SESSION['error']);
    }
}

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check if database connection has an error
$db_error = isset($db_error) ? $db_error : false;

// Check permission
if (!hasPermission('view_household') && !$db_error) {
    header("Location: ../../index.php");
    exit;
}

// Process form submission for adding a household
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_household'])) {
    // Sanitize and validate input
    $household_code = htmlspecialchars(trim($_POST['household_code'] ?? ''));
    $household_head = isset($_POST['household_head']) ? (int)$_POST['household_head'] : 0;
    $address = htmlspecialchars(trim($_POST['address'] ?? ''));
    $status = htmlspecialchars(trim($_POST['status'] ?? 'Active'));
    $landmark = htmlspecialchars(trim($_POST['landmark'] ?? ''));
    $years_of_residency = isset($_POST['years_of_residency']) ? (int)$_POST['years_of_residency'] : null;
    $house_ownership = htmlspecialchars(trim($_POST['house_ownership'] ?? 'Owned'));
    $monthly_rent = isset($_POST['monthly_rent']) ? (float)$_POST['monthly_rent'] : 0;
    $economic_status = htmlspecialchars(trim($_POST['economic_status'] ?? ''));
    $has_electricity = isset($_POST['has_electricity']) ? 1 : 0;
    $has_water_supply = isset($_POST['has_water_supply']) ? 1 : 0;
    $has_internet = isset($_POST['has_internet']) ? 1 : 0;

    $errors = [];

    // Validate required fields
    if (empty($household_code)) $errors[] = "Household code is required";
    if (empty($household_head)) $errors[] = "Household head is required";
    if (empty($address)) $errors[] = "Address is required";

    // Check if household code already exists
    $check_query = "SELECT household_id FROM households WHERE household_code = :household_code";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bindParam(':household_code', $household_code, PDO::PARAM_STR);
    $check_stmt->execute();
    if ($check_stmt->rowCount() > 0) {
        $errors[] = "Household code already exists. Please use a different code.";
    }

    // Proceed if no errors
    if (empty($errors)) {
        // Begin transaction
        $conn->beginTransaction();

        try {
            // Insert new household
            $insert_query = "INSERT INTO households (household_code, household_head_id, address, status,
                landmark, years_of_residency, house_ownership, monthly_rent, economic_status,
                has_electricity, has_water_supply, has_internet)
                VALUES (:household_code, :household_head, :address, :status,
                :landmark, :years_of_residency, :house_ownership, :monthly_rent, :economic_status,
                :has_electricity, :has_water_supply, :has_internet)";

            $insert_stmt = $conn->prepare($insert_query);
            $insert_stmt->bindParam(':household_code', $household_code, PDO::PARAM_STR);
            $insert_stmt->bindParam(':household_head', $household_head, PDO::PARAM_INT);
            $insert_stmt->bindParam(':address', $address, PDO::PARAM_STR);
            $insert_stmt->bindParam(':status', $status, PDO::PARAM_STR);
            $insert_stmt->bindParam(':landmark', $landmark, PDO::PARAM_STR);
            $insert_stmt->bindParam(':years_of_residency', $years_of_residency, PDO::PARAM_INT);
            $insert_stmt->bindParam(':house_ownership', $house_ownership, PDO::PARAM_STR);
            $insert_stmt->bindParam(':monthly_rent', $monthly_rent, PDO::PARAM_STR);
            $insert_stmt->bindParam(':economic_status', $economic_status, PDO::PARAM_STR);
            $insert_stmt->bindParam(':has_electricity', $has_electricity, PDO::PARAM_INT);
            $insert_stmt->bindParam(':has_water_supply', $has_water_supply, PDO::PARAM_INT);
            $insert_stmt->bindParam(':has_internet', $has_internet, PDO::PARAM_INT);

            if (!$insert_stmt->execute()) {
                throw new Exception("Error creating household");
            }

            $household_id = $conn->lastInsertId();

            // Add household head as a member with relation "Head"
            $insert_member_query = "INSERT INTO household_members (household_id, resident_id, relation_to_head)
                                   VALUES (:household_id, :resident_id, 'Head')";
            $member_stmt = $conn->prepare($insert_member_query);
            $member_stmt->bindParam(':household_id', $household_id, PDO::PARAM_INT);
            $member_stmt->bindParam(':resident_id', $household_head, PDO::PARAM_INT);
            if (!$member_stmt->execute()) {
                throw new Exception("Error adding household head as member");
            }

            // Log activity
            if (function_exists('logActivity')) {
                logActivity('Created household: ' . $household_code, $_SESSION['user_id']);
            }

            // Commit transaction
            $conn->commit();
            $_SESSION['success'] = "Household has been created successfully.";

            // Redirect to the same page to avoid form resubmission
            header("Location: households.php");
            exit;

        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollBack();
            $_SESSION['error'] = $e->getMessage();
        }
    } else {
        // Store errors in session
        $_SESSION['error'] = implode('<br>', $errors);
    }
}

// Initialize variables
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;
$offset = ($page - 1) * $limit;
$search = isset($_GET['search']) ? $_GET['search'] : '';
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$result = [];
$total_records = 0;
$total_pages = 0;
$stats = [
    'total_households' => 0,
    'active_households' => 0,
    'avg_household_size' => 0
];

// Set page title
$page_title = "Households Management - Barangay Management System";

if (!$db_error) {
try {
    // Base SQL queries
    $sql = "SELECT h.*,
            CONCAT(r.last_name, ', ', r.first_name, ' ', COALESCE(r.middle_name, '')) as head_name,
            (SELECT COUNT(*) FROM household_members hm WHERE hm.household_id = h.household_id) as member_count
            FROM households h
            LEFT JOIN residents r ON h.household_head_id = r.resident_id";

    $sql_count = "SELECT COUNT(DISTINCT h.household_id) as total FROM households h
                  LEFT JOIN residents r ON h.household_head_id = r.resident_id";

    // Add search condition if search term exists
    $params = [];
    $sql_condition = "";
    if (!empty($search)) {
        $sql_condition .= " WHERE (h.household_code LIKE :search1 OR
                                  r.last_name LIKE :search2 OR
                                  r.first_name LIKE :search3 OR
                                  h.address LIKE :search4)";
        $search_param = "%$search%";
        $params['search1'] = $search_param;
        $params['search2'] = $search_param;
        $params['search3'] = $search_param;
        $params['search4'] = $search_param;
    }

    // Add status filter if selected
    if (!empty($status_filter)) {
        $sql_condition .= empty($sql_condition) ? " WHERE" : " AND";
        $sql_condition .= " h.status = :status";
        $params['status'] = $status_filter;
    }

    // Add conditions to queries
    $sql_count .= $sql_condition;
    $sql .= $sql_condition;

    // Add sorting and limit
    $sql .= " ORDER BY h.household_code ASC LIMIT :offset, :limit";
    $params['offset'] = $offset;
    $params['limit'] = $limit;

    // Prepare and execute count query
    $stmt_count = $conn->prepare($sql_count);
    foreach ($params as $key => $value) {
        if ($key !== 'offset' && $key !== 'limit') {
            $stmt_count->bindValue(":$key", $value);
        }
    }
    $stmt_count->execute();
    $total_records = $stmt_count->fetchColumn();
    $total_pages = ceil($total_records / $limit);

    // Prepare and execute main query
    $stmt = $conn->prepare($sql);
    foreach ($params as $key => $value) {
        $stmt->bindValue(":$key", $value, ($key === 'offset' || $key === 'limit') ? PDO::PARAM_INT : PDO::PARAM_STR);
    }
    $stmt->execute();
    $result = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // Get household statistics
    $stats_query = "SELECT
                    COUNT(DISTINCT h.household_id) as total_households,
                    SUM(CASE WHEN h.status = 'Active' THEN 1 ELSE 0 END) as active_households,
                    COALESCE(AVG(
                        (SELECT COUNT(*)
                        FROM household_members hm
                        WHERE hm.household_id = h.household_id)
                    ), 0) as avg_household_size
                    FROM households h";
    $stats = $conn->query($stats_query)->fetch(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
        error_log("Database error: " . $e->getMessage());
        $_SESSION['error'] = 'A database error occurred. Please try again later.';
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>

    <!-- Favicon -->
    <link rel="icon" href="<?php echo get_favicon_url($conn, '../../'); ?>" type="image/png">

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        /* Ensure all Active badges are green */
        .badge:contains('Active'),
        span.badge:contains('Active'),
        .badge-success,
        .bg-success {
            background-color: #28a745 !important;
            color: white !important;
        }
        /* Stat Card Styles */
        .stat-card {
            border-radius: 0.75rem;
            overflow: hidden;
            height: 100%;
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            position: relative;
            z-index: 1;
        }
        .stat-card:hover {
            transform: translateY(-5px) !important;
            box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2) !important;
            z-index: 2;
            cursor: pointer;
        }
        .stat-card:hover .stat-icon {
            transform: scale(1.1);
        }
        .stat-icon {
            font-size: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 60px;
            width: 60px;
            border-radius: 50%;
            transition: transform 0.3s ease;
        }

        /* Border colors for different statuses */
        .border-primary { border-left: 4px solid #4e73df !important; }
        .border-success { border-left: 4px solid #1cc88a !important; }
        .border-info { border-left: 4px solid #36b9cc !important; }
        .border-warning { border-left: 4px solid #f6c23e !important; }
        .border-danger { border-left: 4px solid #e74a3b !important; }

        /* Background colors with opacity */
        .bg-primary-soft { background-color: rgba(78, 115, 223, 0.1) !important; }
        .bg-success-soft { background-color: rgba(28, 200, 138, 0.1) !important; }
        .bg-info-soft { background-color: rgba(54, 185, 204, 0.1) !important; }
        .bg-warning-soft { background-color: rgba(246, 194, 62, 0.1) !important; }
        .bg-danger-soft { background-color: rgba(231, 74, 59, 0.1) !important; }

        /* Card hover effects */
        .card.shadow {
            transition: all 0.3s ease;
        }
        .card.shadow:hover {
            box-shadow: 0 0.5rem 1.5rem 0 rgba(58, 59, 69, 0.15) !important;
            transform: translateY(-3px);
        }

        /* Table styles */
        .table-bordered-columns th,
        .table-bordered-columns td {
            border-right: 1px solid #dee2e6;
            border-bottom: 1px solid #dee2e6;
        }
        .table-bordered-columns th:last-child,
        .table-bordered-columns td:last-child {
            border-right: none;
        }
        .table-bordered-columns {
            border-left: 1px solid #dee2e6;
            border-right: 1px solid #dee2e6;
            border-top: 1px solid #dee2e6;
        }
        .table-bordered-columns thead th {
            border-bottom: 2px solid #4e73df;
            background-color: #f8f9fc;
        }

        /* Action buttons styling */
        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 5px;
        }

        .action-column {
            text-align: center;
            min-width: 120px;
            white-space: nowrap;
        }

        .btn-action {
            width: 36px;
            height: 36px;
            padding: 6px;
            border-radius: 5px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: transform 0.2s ease;
        }

        .btn-action:hover {
            transform: scale(1.1);
        }

        .table td {
            vertical-align: middle;
            padding: 12px 8px;
        }

        /* Style the table header */
        .table thead th {
            font-weight: 600;
        }

        .text-xs {
            font-size: 0.7rem;
        }

        .font-weight-bold {
            font-weight: 700 !important;
        }

        .text-gray-800 {
            color: #5a5c69 !important;
        }
    </style>
</head>
<body>
            <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>

            <!-- Main Content -->
            <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">🏘️ Households Management</h1>
                    <div class="btn-toolbar">
                        <?php if (!$db_error && hasPermission('edit_household')): ?>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addHouseholdModal">
                            ➕ Add New Household
                        </button>
                        <?php endif; ?>
                    </div>
                </div>

                <?php if ($db_error): ?>
                <!-- Database Error Message -->
                <div class="alert alert-danger">
                    <strong>Database Connection Error:</strong> Could not connect to the database. Please contact the system administrator.
            </div>
                <?php else: ?>

                <?php if (isset($_SESSION['success'])): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        ✅ <?php
                        echo $_SESSION['success'];
                        unset($_SESSION['success']);
                        ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($_GET['success']) && $_GET['success'] == 'deleted'): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        ✅ Household has been deleted successfully.
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($_SESSION['error'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        ❌ <?php
                        echo $_SESSION['error'];
                        unset($_SESSION['error']);
                        ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($_GET['error'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        ❌ <?php
                        $error_message = "An error occurred.";
                        switch($_GET['error']) {
                            case 'not_found':
                                $error_message = "Household not found.";
                                break;
                            case 'delete_failed':
                                $error_message = "Failed to delete household. Please try again.";
                                break;
                            case 'invalid_id':
                                $error_message = "Invalid household ID.";
                                break;
                        }
                        echo $error_message;
                        ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($_SESSION['warning'])): ?>
                <div class="alert alert-warning alert-dismissible fade show" role="alert">
                    ⚠️ <?php
                    echo $_SESSION['warning'];
                    unset($_SESSION['warning']);
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <!-- Quick Statistics -->
                <div class="row mb-4">
                    <div class="col-xl-4 col-md-6 mb-4">
                        <div class="card stat-card border-primary">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-primary-soft text-primary">
                                            🏘️
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1"><?php echo number_format($stats['total_households'] ?? 0); ?></h4>
                                        <p class="mb-0 text-muted">Total Households</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-4 col-md-6 mb-4">
                        <div class="card stat-card border-success">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-success-soft text-success">
                                            🏡
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1"><?php echo number_format($stats['active_households'] ?? 0); ?></h4>
                                        <p class="mb-0 text-muted">Active Households</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-4 col-md-6 mb-4">
                        <div class="card stat-card border-info">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-info-soft text-info">
                                            👪
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1"><?php echo number_format($stats['avg_household_size'] ?? 0, 1); ?></h4>
                                        <p class="mb-0 text-muted">Avg. Household Size</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Search and Filter -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">🔍 Search and Filter</h6>
                    </div>
                    <div class="card-body">
                        <form method="GET" class="row g-3">
                            <div class="col-md-4">
                                <input type="text" class="form-control" name="search" placeholder="Search by household code, head name, address..." value="<?php echo htmlspecialchars($search); ?>">
                            </div>
                            <div class="col-md-4">
                                <select class="form-select" name="status">
                                    <option value="">All Status</option>
                                    <option value="Active" <?php echo ($status_filter === 'Active') ? 'selected' : ''; ?>>Active</option>
                                    <option value="Inactive" <?php echo ($status_filter === 'Inactive') ? 'selected' : ''; ?>>Inactive</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                    <button type="submit" class="btn btn-primary me-md-2">🔍 Filter</button>
                                    <a href="?" class="btn btn-secondary">🔄 Reset</a>
                                    <button type="button" class="btn btn-success" onclick="exportToExcel()">
                                        📊 Export
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Households List -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                        <h6 class="m-0 font-weight-bold text-primary">📋 Households List <span class="badge bg-primary"><?php echo number_format($total_records); ?></span></h6>
                    </div>
                    <div class="card-body">
                        <?php if (empty($result)): ?>
                            <div class="alert alert-info mb-0">
                                ℹ️ No households found matching your criteria.
                            </div>
                        <?php else: ?>
                        <div class="table-responsive">
                                <table class="table table-hover table-bordered-columns" width="100%" cellspacing="0">
                                <thead class="table-light">
                                    <tr>
                                        <th>ID</th>
                                            <th>Household Code</th>
                                        <th>Address</th>
                                        <th>Total Members</th>
                                        <th>Head of Family</th>
                                            <th>Status</th>
                                        <th class="action-column">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                        <?php foreach ($result as $row): ?>
                                        <tr>
                                            <td><?php echo $row['household_id']; ?></td>
                                            <td><small class="text-muted me-1">🏷️</small> <?php echo htmlspecialchars($row['household_code']); ?></td>
                                            <td><small class="text-muted me-1">🏠</small> <?php echo htmlspecialchars($row['address']); ?></td>
                                            <td><small class="text-muted me-1">👥</small> <?php echo $row['member_count']; ?></td>
                                            <td><small class="text-muted me-1">👤</small> <?php echo htmlspecialchars($row['head_name']); ?></td>
                                            <td>
                                                <span class="badge <?php echo strtolower($row['status']) == 'active' ? 'bg-success' : 'bg-warning text-dark'; ?>">
                                                    <?php echo strtolower($row['status']) == 'active' ? '✅ ' : '⚠️ '; ?><?php echo ucfirst($row['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="d-flex gap-1">
                                                    <a href="view_household.php?id=<?php echo $row['household_id']; ?>" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="View Household">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <?php if (hasPermission('edit_household')): ?>
                                                    <a href="edit_household.php?id=<?php echo $row['household_id']; ?>" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="Edit Household">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <?php endif; ?>
                                                    <?php if (hasPermission('edit_household')): ?>
                                                    <a href="#" class="btn btn-sm btn-danger" onclick="confirmDelete(<?php echo $row['household_id']; ?>)" data-bs-toggle="tooltip" title="Delete Household">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                    </tr>
                                        <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                            <!-- Pagination -->
                            <?php if (isset($total_pages) && $total_pages > 1): ?>
                            <div class="d-flex justify-content-center mt-4">
                                <nav aria-label="Page navigation">
                                    <ul class="pagination">
                                        <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $page-1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($status_filter) ? '&status=' . urlencode($status_filter) : ''; ?>" aria-label="Previous">
                                                <span aria-hidden="true">&laquo;</span>
                                            </a>
                                        </li>

                                        <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                            <?php if ($i == 1 || $i == $total_pages || ($i >= $page - 1 && $i <= $page + 1)): ?>
                                                <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                                                    <a class="page-link" href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($status_filter) ? '&status=' . urlencode($status_filter) : ''; ?>">
                                                        <?php echo $i; ?>
                                                    </a>
                                                </li>
                                            <?php elseif ($i == 2 || $i == $total_pages - 1): ?>
                                                <li class="page-item disabled">
                                                    <a class="page-link" href="#">...</a>
                                                </li>
                                            <?php endif; ?>
                                        <?php endfor; ?>

                                        <li class="page-item <?php echo ($page >= $total_pages) ? 'disabled' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $page+1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?><?php echo !empty($status_filter) ? '&status=' . urlencode($status_filter) : ''; ?>" aria-label="Next">
                                                <span aria-hidden="true">&raquo;</span>
                                            </a>
                                        </li>
                                    </ul>
                                </nav>
                            </div>
                            <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">⚠️ Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    Are you sure you want to delete this household? This action cannot be undone and will also remove all member associations.
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <a href="#" id="deleteHousehold" class="btn btn-danger">Delete</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Household Modal -->
    <div class="modal fade" id="addHouseholdModal" tabindex="-1" aria-labelledby="addHouseholdModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addHouseholdModalLabel">➕ Add New Household</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="households.php" method="post">
                    <div class="modal-body">
                        <input type="hidden" name="add_household" value="1">
                        <div class="mb-3">
                            <label for="household_code" class="form-label">🏷️ Household Code <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="household_code" name="household_code" required>
                        </div>
                        <div class="mb-3">
                            <label for="address" class="form-label">🏠 Address <span class="text-danger">*</span></label>
                            <textarea class="form-control" id="address" name="address" rows="3" required></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="household_head" class="form-label">👤 Household Head <span class="text-danger">*</span></label>
                            <select class="form-select" id="household_head" name="household_head" required>
                                <option value="">Select Resident</option>
                                <?php
                                try {
                                    $residents_query = "SELECT resident_id, CONCAT(last_name, ', ', first_name, ' ', COALESCE(middle_name, '')) as full_name FROM residents WHERE status = 'Active' ORDER BY last_name, first_name";
                                    $residents_result = $conn->query($residents_query);
                                    while ($resident = $residents_result->fetch(PDO::FETCH_ASSOC)) {
                                        echo '<option value="' . $resident['resident_id'] . '">' . htmlspecialchars($resident['full_name']) . '</option>';
                                    }
                                } catch (PDOException $e) {
                                    error_log("Error fetching residents: " . $e->getMessage());
                                }
                                ?>
                            </select>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="landmark" class="form-label">🗿 Landmark</label>
                                    <input type="text" class="form-control" id="landmark" name="landmark">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="years_of_residency" class="form-label">📅 Years of Residency</label>
                                    <input type="number" class="form-control" id="years_of_residency" name="years_of_residency" min="0">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="house_ownership" class="form-label">🔑 House Ownership</label>
                                    <select class="form-select" id="house_ownership" name="house_ownership">
                                        <option value="Owned">Owned</option>
                                        <option value="Rented">Rented</option>
                                        <option value="Living with Relatives">Living with Relatives</option>
                                        <option value="Others">Others</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="monthly_rent" class="form-label">💰 Monthly Rent (if applicable)</label>
                                    <input type="number" class="form-control" id="monthly_rent" name="monthly_rent" min="0" step="0.01">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="has_electricity" name="has_electricity" value="1" checked>
                                    <label class="form-check-label" for="has_electricity">⚡ Has Electricity</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="has_water_supply" name="has_water_supply" value="1" checked>
                                    <label class="form-check-label" for="has_water_supply">💧 Has Water Supply</label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="has_internet" name="has_internet" value="1">
                                    <label class="form-check-label" for="has_internet">🌐 Has Internet</label>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="economic_status" class="form-label">💼 Economic Status</label>
                                    <select class="form-select" id="economic_status" name="economic_status">
                                        <option value="">Select Status</option>
                                        <option value="Low Income">Low Income</option>
                                        <option value="Middle Income">Middle Income</option>
                                        <option value="High Income">High Income</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">🚦 Status</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="Active">Active</option>
                                        <option value="Inactive">Inactive</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <button type="submit" class="btn btn-primary">Save Household</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Function to show toast notifications
        function showToast(message, type = 'success') {
            // Create toast container if it doesn't exist
            let toastContainer = document.querySelector('.toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
                toastContainer.style.zIndex = '9999';
                document.body.appendChild(toastContainer);
            }

            // Create toast element
            const toastId = 'toast-' + Date.now();
            const toastElement = document.createElement('div');
            toastElement.id = toastId;
            toastElement.className = `toast align-items-center text-white bg-${type} border-0`;
            toastElement.setAttribute('role', 'alert');
            toastElement.setAttribute('aria-live', 'assertive');
            toastElement.setAttribute('aria-atomic', 'true');

            // Create toast content
            toastElement.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'danger' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            `;

            // Add toast to container
            toastContainer.appendChild(toastElement);

            // Initialize and show toast
            const toast = new bootstrap.Toast(toastElement, {
                autohide: true,
                delay: 5000
            });
            toast.show();

            // Remove toast after it's hidden
            toastElement.addEventListener('hidden.bs.toast', function() {
                toastElement.remove();
                // Remove container if empty
                if (toastContainer.children.length === 0) {
                    toastContainer.remove();
                }
            });
        }

        // Handle delete confirmation
        function confirmDelete(householdId) {
            const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            document.getElementById('deleteHousehold').href = 'delete_household.php?id=' + householdId;
            deleteModal.show();
        }

        // Handle session messages
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl)
            });

            // Fix Active badges styling
            document.querySelectorAll('.badge').forEach(function(badge) {
                if (badge.textContent.trim() === 'Active') {
                    badge.classList.add('bg-success');
                    badge.classList.remove('bg-warning', 'text-dark');
                    badge.style.backgroundColor = '#28a745';
                    badge.style.color = 'white';
                }
            });

            // Show session messages as toasts
            <?php if (isset($_SESSION['success'])): ?>
                showToast("<?php echo addslashes($_SESSION['success']); ?>", "success");
                <?php unset($_SESSION['success']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['error'])): ?>
                showToast("<?php echo addslashes($_SESSION['error']); ?>", "danger");
                <?php unset($_SESSION['error']); ?>
            <?php endif; ?>

            <?php if (isset($_SESSION['info'])): ?>
                showToast("<?php echo addslashes($_SESSION['info']); ?>", "info");
                <?php unset($_SESSION['info']); ?>
            <?php endif; ?>

            // Set up event handler for household modal
            document.getElementById('addHouseholdModal').addEventListener('show.bs.modal', function() {
                // Generate household code
                generateHouseholdCode();
            });
        });

        // Function to generate a unique household code
        function generateHouseholdCode() {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');

            // Get existing household codes to find the latest
            fetch('get_latest_household_code.php?year=' + year + '&month=' + month)
                .then(response => response.json())
                .then(data => {
                    let newNumber = 1;
                    if (data.success && data.latest_number) {
                        newNumber = parseInt(data.latest_number) + 1;
                    }

                    // Format: HH-YYYYMM-####
                    const suggestedCode = 'HH-' + year + month + String(newNumber).padStart(4, '0');
                    document.getElementById('household_code').value = suggestedCode;
                })
                .catch(error => {
                    // If API fails, fallback to a default pattern with timestamp
                    const timestamp = now.getTime() % 10000;
                    const suggestedCode = 'HH-' + year + month + String(timestamp).padStart(4, '0');
                    document.getElementById('household_code').value = suggestedCode;
                });
        }

        function exportToExcel() {
            alert('Export to Excel functionality will be implemented soon');
        }
    </script>
</body>
</html>