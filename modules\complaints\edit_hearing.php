<?php
session_start();
include '../../includes/config/database.php';
include '../../includes/functions/permission_functions.php';
include '../../includes/functions/utility.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check permission
if (!hasPermission('update_hearing')) {
    header("Location: ../../index.php");
    exit;
}

// Initialize variables
$success_message = '';
$error_message = '';
$hearing = null;
$complaints = [];

// Get hearing ID from URL
$hearing_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($hearing_id <= 0) {
    $_SESSION['error'] = "Invalid hearing ID";
    header("Location: hearings.php");
    exit;
}

// Fetch hearing details
try {
    $query = "SELECT h.*, c.complaint_type, c.complainant_name, c.respondent_name
              FROM hearings h
              LEFT JOIN complaints c ON h.complaint_id = c.complaint_id
              WHERE h.hearing_id = :hearing_id";

    $stmt = $conn->prepare($query);
    $stmt->bindParam(':hearing_id', $hearing_id, PDO::PARAM_INT);
    $stmt->execute();

    if ($stmt->rowCount() == 0) {
        $_SESSION['error'] = "Hearing not found";
        header("Location: hearings.php");
        exit;
    }

    $hearing = $stmt->fetch();

    // Check if hearing can be edited
    if ($hearing['status'] != 'Scheduled') {
        $_SESSION['error'] = "Only scheduled hearings can be edited";
        header("Location: view_hearing.php?id=" . $hearing_id);
        exit;
    }

    // Get all active complaints for dropdown
    $complaints_query = "SELECT complaint_id, complaint_type, complainant_name, respondent_name
                        FROM complaints
                        WHERE status != 'Resolved' AND status != 'Dismissed'
                        ORDER BY complaint_id DESC";
    $complaints_stmt = $conn->prepare($complaints_query);
    $complaints_stmt->execute();
    $complaints = $complaints_stmt->fetchAll();

} catch (PDOException $e) {
    $_SESSION['error'] = "Error fetching hearing details: " . $e->getMessage();
    header("Location: hearings.php");
    exit;
}

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get form data
    $complaint_id = isset($_POST['complaint_id']) ? (int)$_POST['complaint_id'] : $hearing['complaint_id'];
    $hearing_date = sanitize($_POST['hearing_date']);
    $hearing_time = sanitize($_POST['hearing_time']);
    $location = sanitize($_POST['location']);
    $mediator = sanitize($_POST['mediator']);
    $notes = sanitize($_POST['notes']);

    // Validate required fields
    if (empty($hearing_date)) {
        $error_message = "Hearing date is required";
    } else {
        try {
            // Update hearing
            $update_query = "UPDATE hearings SET
                            complaint_id = :complaint_id,
                            hearing_date = :hearing_date,
                            hearing_time = :hearing_time,
                            location = :location,
                            mediator = :mediator,
                            notes = :notes,
                            updated_at = NOW()
                            WHERE hearing_id = :hearing_id";

            $update_stmt = $conn->prepare($update_query);
            $update_stmt->bindParam(':complaint_id', $complaint_id, PDO::PARAM_INT);
            $update_stmt->bindParam(':hearing_date', $hearing_date);
            $update_stmt->bindParam(':hearing_time', $hearing_time);
            $update_stmt->bindParam(':location', $location);
            $update_stmt->bindParam(':mediator', $mediator);
            $update_stmt->bindParam(':notes', $notes);
            $update_stmt->bindParam(':hearing_id', $hearing_id, PDO::PARAM_INT);
            $update_stmt->execute();

            // Log activity
            logActivity($_SESSION['user_id'], 'Edit Hearing', "Updated hearing #$hearing_id");

            $_SESSION['message'] = "Hearing has been updated successfully";
            $_SESSION['message_type'] = "success";

            // Redirect to view hearing page
            header("Location: view_hearing.php?id=" . $hearing_id);
            exit;
        } catch (PDOException $e) {
            $error_message = "Error updating hearing: " . $e->getMessage();
        }
    }
}

// Page title
$page_title = "Edit Hearing - Barangay Management System";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        .card {
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            border: none;
            margin-bottom: 24px;
        }

        .card-header {
            background-color: rgba(78, 115, 223, 0.1);
            border-bottom: none;
            padding: 15px 20px;
        }

        .form-label {
            font-weight: 600;
            margin-bottom: 8px;
        }

        .required-indicator {
            color: #dc3545;
            font-weight: bold;
        }

        .btn-primary {
            background-color: #4e73df;
            border-color: #4e73df;
        }

        .btn-primary:hover {
            background-color: #2e59d9;
            border-color: #2e59d9;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>

            <!-- Main Content -->
            <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">📝 Edit Hearing</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="../../index.php">Home</a></li>
                            <li class="breadcrumb-item"><a href="hearings.php">Hearings</a></li>
                            <li class="breadcrumb-item"><a href="view_hearing.php?id=<?php echo $hearing_id; ?>">View Hearing</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Edit Hearing</li>
                        </ol>
                    </nav>
                </div>

                <?php if (!empty($success_message)): ?>
                <div class="alert alert-success" role="alert">
                    <i class="fas fa-check-circle me-2"></i> <?php echo $success_message; ?>
                </div>
                <?php endif; ?>

                <?php if (!empty($error_message)): ?>
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-circle me-2"></i> <?php echo $error_message; ?>
                </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    📅 Hearing Details
                                </h5>
                            </div>
                            <div class="card-body">
                                <form action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]) . '?id=' . $hearing_id; ?>" method="POST">
                                    <div class="mb-3">
                                        <label for="complaint_id" class="form-label">Related Complaint 📋 <span class="required-indicator">*</span></label>
                                        <select class="form-select" id="complaint_id" name="complaint_id" required>
                                            <option value="">Select Complaint</option>
                                            <?php foreach ($complaints as $complaint): ?>
                                            <option value="<?php echo $complaint['complaint_id']; ?>" <?php echo ($complaint['complaint_id'] == $hearing['complaint_id']) ? 'selected' : ''; ?>>
                                                #<?php echo str_pad($complaint['complaint_id'], 5, '0', STR_PAD_LEFT); ?> -
                                                <?php echo htmlspecialchars($complaint['complaint_type']); ?> -
                                                <?php echo htmlspecialchars($complaint['complainant_name']); ?> vs
                                                <?php echo htmlspecialchars($complaint['respondent_name']); ?>
                                            </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>

                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="hearing_date" class="form-label">Hearing Date 📆 <span class="required-indicator">*</span></label>
                                            <input type="date" class="form-control" id="hearing_date" name="hearing_date" required value="<?php echo isset($hearing['hearing_date']) ? $hearing['hearing_date'] : ''; ?>">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="hearing_time" class="form-label">Hearing Time ⏰</label>
                                            <input type="time" class="form-control" id="hearing_time" name="hearing_time" value="<?php echo isset($hearing['hearing_time']) ? $hearing['hearing_time'] : ''; ?>">
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="location" class="form-label">Location 📍</label>
                                        <input type="text" class="form-control" id="location" name="location" value="<?php echo isset($hearing['location']) ? htmlspecialchars($hearing['location']) : ''; ?>" placeholder="Barangay Hall Conference Room">
                                    </div>

                                    <div class="mb-3">
                                        <label for="mediator" class="form-label">Mediator/Officer 👨‍⚖️</label>
                                        <input type="text" class="form-control" id="mediator" name="mediator" value="<?php echo isset($hearing['mediator']) ? htmlspecialchars($hearing['mediator']) : ''; ?>">
                                    </div>

                                    <div class="mb-3">
                                        <label for="notes" class="form-label">Notes 📝</label>
                                        <textarea class="form-control" id="notes" name="notes" rows="4"><?php echo isset($hearing['notes']) ? htmlspecialchars($hearing['notes']) : ''; ?></textarea>
                                        <small class="text-muted">Additional information or instructions for the hearing.</small>
                                    </div>

                                    <div class="d-flex justify-content-end mt-4">
                                        <a href="view_hearing.php?id=<?php echo $hearing_id; ?>" class="btn btn-secondary me-2">
                                            ❌ Cancel
                                        </a>
                                        <button type="submit" class="btn btn-primary">
                                            💾 Save Changes
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    ℹ️ Current Information
                                </h5>
                            </div>
                            <div class="card-body">
                                <p><strong>Hearing ID:</strong> #<?php echo str_pad($hearing['hearing_id'], 4, '0', STR_PAD_LEFT); ?></p>
                                <p><strong>Status:</strong> <?php echo htmlspecialchars($hearing['status']); ?></p>
                                <p><strong>Complaint:</strong> #<?php echo str_pad($hearing['complaint_id'], 5, '0', STR_PAD_LEFT); ?> - <?php echo htmlspecialchars($hearing['complaint_type']); ?></p>
                                <p><strong>Complainant:</strong> <?php echo htmlspecialchars($hearing['complainant_name']); ?></p>
                                <p><strong>Respondent:</strong> <?php echo htmlspecialchars($hearing['respondent_name']); ?></p>
                                <p><strong>Created:</strong> <?php echo date('F d, Y', strtotime($hearing['created_at'])); ?></p>

                                <div class="alert alert-info mt-3">
                                    ⚠️ Only scheduled hearings can be edited. Once a hearing is marked as completed, postponed, or cancelled, it cannot be edited.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</body>
</html>
