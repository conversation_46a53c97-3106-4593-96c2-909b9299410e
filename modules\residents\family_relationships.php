<?php
session_start();
include '../../includes/config/database.php';
include '../../includes/functions/permission_functions.php';
include '../../includes/functions/utility.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check permission
if (!hasPermission('view_residents')) {
    header("Location: ../../index.php");
    exit;
}

// Set page title
$page_title = "Family Relationships - Barangay Management System";

// Initialize variables
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = 10;
$offset = ($page - 1) * $limit;
$search = isset($_GET['search']) ? $_GET['search'] : '';
$error_message = '';
$success_message = '';

// Function to get reciprocal relationship
function get_reciprocal_relationship($relationship) {
    $reciprocals = [
        'Father' => 'Child',
        'Mother' => 'Child',
        'Child' => 'Parent',
        'Husband' => 'Wife',
        'Wife' => 'Husband',
        'Brother' => 'Sibling',
        'Sister' => 'Sibling',
        'Sibling' => 'Sibling',
        'Grandfather' => 'Grandchild',
        '<PERSON>mother' => '<PERSON>child',
        '<PERSON>child' => 'Grandparent',
        'Uncle' => 'Nephew/Niece',
        'Aunt' => 'Nephew/Niece',
        'Nephew' => 'Uncle/Aunt',
        'Niece' => 'Uncle/Aunt',
        'Cousin' => 'Cousin',
        'Guardian' => 'Ward',
        'Ward' => 'Guardian',
        'Father-in-law' => 'Son/Daughter-in-law',
        'Mother-in-law' => 'Son/Daughter-in-law',
        'Son-in-law' => 'Father/Mother-in-law',
        'Daughter-in-law' => 'Father/Mother-in-law',
        'Stepfather' => 'Stepchild',
        'Stepmother' => 'Stepchild',
        'Stepchild' => 'Stepparent'
    ];
    
    return isset($reciprocals[$relationship]) ? $reciprocals[$relationship] : null;
}

// Check for success messages
if (isset($_GET['success'])) {
    switch ($_GET['success']) {
        case 'added':
            $success_message = "Family relationship has been added successfully.";
            break;
        case 'deleted':
            $success_message = "Family relationship has been deleted successfully.";
            break;
        case 'updated':
            $success_message = "Family relationship has been updated successfully.";
            break;
    }
}

// Check for error messages
if (isset($_GET['error'])) {
    switch ($_GET['error']) {
        case 'duplicate':
            $error_message = "A relationship between these residents already exists.";
            break;
        case 'not_found':
            $error_message = "Relationship not found.";
            break;
        case 'db_error':
            $error_message = "A database error occurred. Please try again.";
            break;
        case 'validation':
            $error_message = "Please fill in all required fields.";
            break;
    }
}

// Load residents for dropdowns
try {
    $residents_query = "SELECT resident_id, 
                      CONCAT(last_name, ', ', first_name, ' ', COALESCE(middle_name, '')) as full_name,
                      gender, birthdate
                      FROM residents 
                      WHERE status = 'Active' 
                      ORDER BY last_name, first_name";
    $residents_stmt = $conn->prepare($residents_query);
    $residents_stmt->execute();
    $residents = $residents_stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    error_log("Error fetching residents: " . $e->getMessage());
    $residents = [];
}

// Load relationships with pagination
try {
    // Build the search condition
    $search_condition = "";
    $params = [];
    
    if (!empty($search)) {
        $search_condition = " WHERE (
            r1.first_name LIKE :search1 OR 
            r1.last_name LIKE :search2 OR 
            r2.first_name LIKE :search3 OR 
            r2.last_name LIKE :search4 OR
            fr.relationship LIKE :search5
        )";
        $search_param = "%$search%";
        $params['search1'] = $search_param;
        $params['search2'] = $search_param;
        $params['search3'] = $search_param;
        $params['search4'] = $search_param;
        $params['search5'] = $search_param;
    }
    
    // Count total records for pagination
    $count_query = "SELECT COUNT(*) as total FROM family_relationships fr
                 JOIN residents r1 ON fr.resident_id = r1.resident_id
                  JOIN residents r2 ON fr.related_to = r2.resident_id" . $search_condition;
    
    $count_stmt = $conn->prepare($count_query);
    foreach ($params as $key => $value) {
        $count_stmt->bindValue(":$key", $value);
    }
    $count_stmt->execute();
    $total_records = $count_stmt->fetchColumn();
    $total_pages = ceil($total_records / $limit);
    
    // Get relationships with resident names
    $relationships_query = "SELECT fr.*, 
           CONCAT(r1.last_name, ', ', r1.first_name, ' ', COALESCE(r1.middle_name, '')) as resident_name,
                          CONCAT(r2.last_name, ', ', r2.first_name, ' ', COALESCE(r2.middle_name, '')) as related_name,
                          r1.gender as resident_gender,
                          r2.gender as related_gender
           FROM family_relationships fr
           JOIN residents r1 ON fr.resident_id = r1.resident_id
                          JOIN residents r2 ON fr.related_to = r2.resident_id" . $search_condition . "
                          ORDER BY r1.last_name, r1.first_name
           LIMIT :offset, :limit";
    
    $relationships_stmt = $conn->prepare($relationships_query);
    foreach ($params as $key => $value) {
        $relationships_stmt->bindValue(":$key", $value);
    }
    $relationships_stmt->bindValue(":offset", $offset, PDO::PARAM_INT);
    $relationships_stmt->bindValue(":limit", $limit, PDO::PARAM_INT);
    $relationships_stmt->execute();
    $relationships = $relationships_stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    error_log("Error fetching relationships: " . $e->getMessage());
    $relationships = [];
    $total_pages = 0;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>

    <!-- Favicon -->
    <link rel="icon" href="<?php echo get_favicon_url($conn, '../../'); ?>" type="image/png">

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css">
    <link rel="stylesheet" href="../../assets/css/style.css">
</head>
<body>
            <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">👨‍👩‍👧‍👦 Family Relationships Management</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <?php if (hasPermission('edit_residents')): ?>
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addRelationshipModal">
                            ➕ Add Relationship
                        </button>
                        <?php endif; ?>
            </div>
        </div>

                <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                    ✅ <?php echo $success_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (!empty($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    ❌ <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <!-- Search and Filter Section -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="card-title mb-0">🔍 Search and Filter</h5>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="family_relationships.php" class="row g-3">
                            <div class="col-md-8">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="search" name="search" placeholder="Search by resident name or relationship" value="<?php echo htmlspecialchars($search); ?>">
                                    <button class="btn btn-primary" type="submit">
                                        🔍 Search
                                    </button>
                                    <?php if (!empty($search)): ?>
                                    <a href="family_relationships.php" class="btn btn-secondary">
                                        🔄 Reset
                                    </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Relationships List -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">👪 Family Relationships (<?php echo $total_records; ?>)</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($relationships)): ?>
                        <div class="alert alert-info">
                            ℹ️ No family relationships found.
                            <?php if (!empty($search)): ?>
                            <p class="mb-0">Try adjusting your search criteria.</p>
                            <?php else: ?>
                            <p class="mb-0">Start by adding relationships between residents.</p>
                            <?php endif; ?>
                        </div>
                        <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>👤 Resident</th>
                                        <th>🔄 Relationship</th>
                                        <th>👥 Related To</th>
                                        <th>⚙️ Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($relationships as $relationship): ?>
                                    <tr>
                                        <td>
                                            <a href="view_resident.php?id=<?php echo $relationship['resident_id']; ?>">
                                                <?php echo htmlspecialchars($relationship['resident_name']); ?>
                                            </a>
                                        </td>
                                        <td><?php echo htmlspecialchars($relationship['relationship']); ?></td>
                                        <td>
                                            <a href="view_resident.php?id=<?php echo $relationship['related_to']; ?>">
                                                <?php echo htmlspecialchars($relationship['related_name']); ?>
                                            </a>
                                        </td>
                                        <td>
                                            <div class="d-flex gap-1">
                                            <?php if (hasPermission('edit_residents')): ?>
                                            <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="Edit Relationship"
                                                    onclick="editRelationship(<?php echo $relationship['relationship_id']; ?>, 
                                                                             '<?php echo addslashes($relationship['resident_name']); ?>', 
                                                                             '<?php echo addslashes($relationship['related_name']); ?>', 
                                                                             '<?php echo $relationship['relationship']; ?>', 
                                                                             <?php echo $relationship['resident_id']; ?>, 
                                                                             <?php echo $relationship['related_to']; ?>)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="Delete Relationship"
                                                    onclick="confirmDelete(<?php echo $relationship['relationship_id']; ?>, 
                                                                          '<?php echo addslashes($relationship['resident_name']); ?>', 
                                                                          '<?php echo $relationship['relationship']; ?>', 
                                                                          '<?php echo addslashes($relationship['related_name']); ?>')">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <!-- Pagination -->
                        <?php if ($total_pages > 1): ?>
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center mt-4">
                                <li class="page-item <?php echo ($page <= 1) ? 'disabled' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $page-1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                
                                <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                    <?php if ($i == 1 || $i == $total_pages || ($i >= $page - 1 && $i <= $page + 1)): ?>
                                        <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $i; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>">
                                                <?php echo $i; ?>
                                            </a>
                                        </li>
                                    <?php elseif ($i == 2 || $i == $total_pages - 1): ?>
                                        <li class="page-item disabled">
                                            <a class="page-link" href="#">...</a>
                                        </li>
                                    <?php endif; ?>
                                <?php endfor; ?>
                                
                                <li class="page-item <?php echo ($page >= $total_pages) ? 'disabled' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $page+1; ?><?php echo !empty($search) ? '&search=' . urlencode($search) : ''; ?>" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                            </ul>
                        </nav>
                        <?php endif; ?>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
            </div>
    </div>

<!-- Add Relationship Modal -->
    <div class="modal fade" id="addRelationshipModal" tabindex="-1" aria-labelledby="addRelationshipModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                    <h5 class="modal-title" id="addRelationshipModalLabel">➕ Add New Relationship</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
                <form id="addRelationshipForm" action="relationship_actions.php" method="POST">
            <div class="modal-body">
                        <div class="mb-3">
                            <label for="resident_id" class="form-label">👤 Resident <span class="text-danger">*</span></label>
                            <select class="form-select select2" id="resident_id" name="resident_id" required>
                                <option value="">Select resident</option>
                            <?php foreach ($residents as $resident): ?>
                                <option value="<?php echo $resident['resident_id']; ?>">
                                    <?php echo htmlspecialchars($resident['full_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                        <div class="mb-3">
                            <label for="relationship" class="form-label">🔄 Relationship <span class="text-danger">*</span></label>
                            <select class="form-select" id="relationship" name="relationship" required>
                                <option value="">Select relationship</option>
                            <option value="Father">Father</option>
                            <option value="Mother">Mother</option>
                            <option value="Child">Child</option>
                            <option value="Husband">Husband</option>
                            <option value="Wife">Wife</option>
                            <option value="Brother">Brother</option>
                            <option value="Sister">Sister</option>
                            <option value="Grandfather">Grandfather</option>
                            <option value="Grandmother">Grandmother</option>
                            <option value="Grandchild">Grandchild</option>
                            <option value="Uncle">Uncle</option>
                            <option value="Aunt">Aunt</option>
                            <option value="Nephew">Nephew</option>
                            <option value="Niece">Niece</option>
                            <option value="Cousin">Cousin</option>
                                <option value="Father-in-law">Father-in-law</option>
                                <option value="Mother-in-law">Mother-in-law</option>
                                <option value="Son-in-law">Son-in-law</option>
                                <option value="Daughter-in-law">Daughter-in-law</option>
                            <option value="Guardian">Guardian</option>
                            <option value="Ward">Ward</option>
                                <option value="Stepfather">Stepfather</option>
                                <option value="Stepmother">Stepmother</option>
                                <option value="Stepchild">Stepchild</option>
                        </select>
                    </div>
                    
                        <div class="mb-3">
                            <label for="related_to" class="form-label">👥 Related To <span class="text-danger">*</span></label>
                            <select class="form-select select2" id="related_to" name="related_to" required>
                                <option value="">Select related resident</option>
                            <?php foreach ($residents as $resident): ?>
                                <option value="<?php echo $resident['resident_id']; ?>">
                                    <?php echo htmlspecialchars($resident['full_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="create_reciprocal" name="create_reciprocal" value="1" checked>
                            <label class="form-check-label" for="create_reciprocal">
                                Automatically create reciprocal relationship
                            </label>
                            <div class="form-text text-muted">
                                If checked, the system will automatically create the corresponding relationship in the other direction.
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <input type="hidden" name="action" value="add">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Save Relationship</button>
                    </div>
                </form>
        </div>
    </div>
</div>

    <!-- Edit Relationship Modal -->
    <div class="modal fade" id="editRelationshipModal" tabindex="-1" aria-labelledby="editRelationshipModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                    <h5 class="modal-title" id="editRelationshipModalLabel">✏️ Edit Relationship</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
                <form id="editRelationshipForm" action="relationship_actions.php" method="POST">
            <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">👤 Resident</label>
                            <input type="text" class="form-control" id="edit_resident_name" readonly>
                        </div>
                        
                        <div class="mb-3">
                            <label for="edit_relationship" class="form-label">🔄 Relationship <span class="text-danger">*</span></label>
                            <select class="form-select" id="edit_relationship" name="relationship" required>
                                <option value="">Select relationship</option>
                                <option value="Father">Father</option>
                                <option value="Mother">Mother</option>
                                <option value="Child">Child</option>
                                <option value="Husband">Husband</option>
                                <option value="Wife">Wife</option>
                                <option value="Brother">Brother</option>
                                <option value="Sister">Sister</option>
                                <option value="Grandfather">Grandfather</option>
                                <option value="Grandmother">Grandmother</option>
                                <option value="Grandchild">Grandchild</option>
                                <option value="Uncle">Uncle</option>
                                <option value="Aunt">Aunt</option>
                                <option value="Nephew">Nephew</option>
                                <option value="Niece">Niece</option>
                                <option value="Cousin">Cousin</option>
                                <option value="Father-in-law">Father-in-law</option>
                                <option value="Mother-in-law">Mother-in-law</option>
                                <option value="Son-in-law">Son-in-law</option>
                                <option value="Daughter-in-law">Daughter-in-law</option>
                                <option value="Guardian">Guardian</option>
                                <option value="Ward">Ward</option>
                                <option value="Stepfather">Stepfather</option>
                                <option value="Stepmother">Stepmother</option>
                                <option value="Stepchild">Stepchild</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">👥 Related To</label>
                            <input type="text" class="form-control" id="edit_related_name" readonly>
                        </div>
                        
                        <div class="form-check mb-3">
                            <input class="form-check-input" type="checkbox" id="edit_update_reciprocal" name="update_reciprocal" value="1" checked>
                            <label class="form-check-label" for="edit_update_reciprocal">
                                Also update reciprocal relationship
                            </label>
                            <div class="form-text text-muted">
                                If checked, the system will also update the corresponding relationship in the other direction.
                            </div>
                        </div>
            </div>
            <div class="modal-footer">
                        <input type="hidden" name="action" value="edit">
                        <input type="hidden" name="relationship_id" id="edit_relationship_id">
                        <input type="hidden" name="resident_id" id="edit_resident_id">
                        <input type="hidden" name="related_to" id="edit_related_to">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update Relationship</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteModal" tabindex="-1" aria-labelledby="deleteModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteModalLabel">⚠️ Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete this relationship?</p>
                    <p id="deleteConfirmText" class="fw-bold"></p>
                    <p>This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <form action="relationship_actions.php" method="POST">
                        <input type="hidden" name="action" value="delete">
                        <input type="hidden" name="relationship_id" id="delete_relationship_id">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
        $(document).ready(function() {
            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl)
            });
            
            // Initialize Select2 for resident dropdowns
            $('.select2').select2({
                theme: 'bootstrap-5',
                width: '100%'
            });
            
            // Prevent selecting the same resident in both fields
            $('#resident_id').on('change', function() {
                const residentId = $(this).val();
                
                $('#related_to option').prop('disabled', false);
                if (residentId) {
                    $('#related_to option[value="' + residentId + '"]').prop('disabled', true);
                }
                
                if ($('#related_to').val() === residentId) {
                    $('#related_to').val('').trigger('change');
                }
            });
            
            $('#related_to').on('change', function() {
                const relatedId = $(this).val();
                
                $('#resident_id option').prop('disabled', false);
                if (relatedId) {
                    $('#resident_id option[value="' + relatedId + '"]').prop('disabled', true);
                }
                
                if ($('#resident_id').val() === relatedId) {
                    $('#resident_id').val('').trigger('change');
        }
    });
});
        
        // Handle edit relationship
        function editRelationship(id, residentName, relatedName, relationship, residentId, relatedTo) {
            $('#edit_relationship_id').val(id);
            $('#edit_resident_name').val(residentName);
            $('#edit_related_name').val(relatedName);
            $('#edit_relationship').val(relationship);
            $('#edit_resident_id').val(residentId);
            $('#edit_related_to').val(relatedTo);
            
            const editModal = new bootstrap.Modal(document.getElementById('editRelationshipModal'));
            editModal.show();
        }
        
        // Handle delete confirmation
        function confirmDelete(id, residentName, relationship, relatedName) {
            $('#delete_relationship_id').val(id);
            $('#deleteConfirmText').text(`${residentName} is ${relationship} of ${relatedName}`);
            
            const deleteModal = new bootstrap.Modal(document.getElementById('deleteModal'));
            deleteModal.show();
        }
        
        // Function to show toast notifications
        function showToast(message, type = 'success') {
            // Create toast container if it doesn't exist
            let toastContainer = document.querySelector('.toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
                toastContainer.style.zIndex = '9999';
                document.body.appendChild(toastContainer);
            }
            
            // Create toast element
            const toastId = 'toast-' + Date.now();
            const toastElement = document.createElement('div');
            toastElement.id = toastId;
            toastElement.className = `toast align-items-center text-white bg-${type} border-0`;
            toastElement.setAttribute('role', 'alert');
            toastElement.setAttribute('aria-live', 'assertive');
            toastElement.setAttribute('aria-atomic', 'true');
            
            // Set icon based on type
            let icon = '📝'; // default/info
            if (type === 'success') icon = '✅';
            else if (type === 'danger') icon = '❌';
            else if (type === 'warning') icon = '⚠️';
            
            // Create toast content
            toastElement.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        ${icon} ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            `;
            
            // Add toast to container
            toastContainer.appendChild(toastElement);
            
            // Initialize and show toast
            const toast = new bootstrap.Toast(toastElement, {
                autohide: true,
                delay: 5000
            });
            toast.show();
            
            // Remove toast after it's hidden
            toastElement.addEventListener('hidden.bs.toast', function() {
                toastElement.remove();
                // Remove container if empty
                if (toastContainer.children.length === 0) {
                    toastContainer.remove();
                }
            });
        }
        
        // Show messages as toasts when page loads
        document.addEventListener('DOMContentLoaded', function() {
            <?php if (!empty($success_message)): ?>
            showToast("<?php echo addslashes($success_message); ?>", "success");
            <?php endif; ?>
            
            <?php if (!empty($error_message)): ?>
            showToast("<?php echo addslashes($error_message); ?>", "danger");
            <?php endif; ?>
        });
</script>
</body>
</html> 