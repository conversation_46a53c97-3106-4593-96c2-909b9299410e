<?php
session_start();
include '../../includes/config/database.php';
include '../../includes/functions/permission_functions.php';
include '../../includes/functions/utility.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check permission
if (!hasPermission('edit_household')) {
    header("Location: ../../index.php");
    exit;
}

// Get household ID from URL
$household_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($household_id <= 0) {
    header("Location: households.php?error=invalid_id");
    exit;
}

// Get household information for logging
$query = "SELECT household_code FROM households WHERE household_id = :household_id";
$stmt = $conn->prepare($query);
$stmt->bindParam(':household_id', $household_id, PDO::PARAM_INT);
$stmt->execute();

if ($stmt->rowCount() == 0) {
    // Household not found
    header("Location: households.php?error=not_found");
    exit;
}

$household = $stmt->fetch(PDO::FETCH_ASSOC);
$household_code = $household['household_code'];

// Begin transaction
try {
    $conn->beginTransaction();
    
    // First, delete all household member associations
    $delete_members_query = "DELETE FROM household_members WHERE household_id = :household_id";
    $delete_members_stmt = $conn->prepare($delete_members_query);
    $delete_members_stmt->bindParam(':household_id', $household_id, PDO::PARAM_INT);
    $delete_members_stmt->execute();
    
    // Then, delete the household record
    $delete_household_query = "DELETE FROM households WHERE household_id = :household_id";
    $delete_household_stmt = $conn->prepare($delete_household_query);
    $delete_household_stmt->bindParam(':household_id', $household_id, PDO::PARAM_INT);
    $delete_household_stmt->execute();
    
    // Log activity
    if (function_exists('logActivity')) {
        logActivity('Deleted household: ' . $household_code, $_SESSION['user_id'], 'delete', 'households', $household_id);
    }
    
    // Commit transaction
    $conn->commit();
    
    // Redirect with success message
    header("Location: households.php?success=deleted");
    exit;
    
} catch (PDOException $e) {
    // Rollback transaction on error
    $conn->rollBack();
    
    // Log error
    error_log("Error deleting household: " . $e->getMessage());
    
    // Redirect with error message
    header("Location: households.php?error=delete_failed");
    exit;
}
?> 