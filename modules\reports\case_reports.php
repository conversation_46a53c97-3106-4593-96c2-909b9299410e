<?php
include '../../includes/functions/permission_functions.php';
// Start session
session_start();

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include necessary files
require_once '../../includes/config/config.php';
require_once '../../includes/config/database.php';
require_once '../../includes/functions/utility.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit();
}

// Check if user has permission for case reports
if (!hasPermission('view_reports') && !hasPermission('admin')) {
    header("Location: ../../index.php");
    exit();
}

// Set page title and module info
$page_title = "Bail Management Reports";
$module_name = "Reports";
$sub_module = "Bail";

// Initialize variables
$message = "";
$message_type = "";
$selected_year = isset($_GET['year']) ? $_GET['year'] : date('Y');
$selected_month = isset($_GET['month']) ? $_GET['month'] : '';
$selected_type = isset($_GET['type']) ? $_GET['type'] : '';
$available_years = [date('Y')]; // Initialize with current year
$case_types = [];

// Check if required tables exist
$tables_exist = true;
try {
    // Check if bail tables exist
    $bail_tables_exist = true;
    $bail_tables_check = $conn->query("SHOW TABLES LIKE 'bail_payments'");
    $blotter_check = $conn->query("SHOW TABLES LIKE 'blotter_entries'");
    
    if ($bail_tables_check->rowCount() == 0 || $blotter_check->rowCount() == 0) {
        $bail_tables_exist = false;
        $message = "The bail management tables do not exist yet. Please set up the bail management module first.";
        $message_type = "info";
    }
    
    // If bail tables exist, load data
    if ($bail_tables_exist) {
        // Get available years for bail payments
        $year_query = "SELECT DISTINCT YEAR(payment_date) as year FROM bail_payments ORDER BY year DESC";
        $year_stmt = $conn->query($year_query);
        $available_years = $year_stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (empty($available_years)) {
            $available_years = [date('Y')];
        }
        
        // First - fetch ALL blotter entries to get real counts regardless of filtering
        $total_blotter_query = "SELECT COUNT(*) as total FROM blotter_entries";
        $total_blotter_stmt = $conn->query($total_blotter_query);
        $total_blotter_count = $total_blotter_stmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;
        
        // Second - fetch active bail cases with direct query
        $active_bail_query = "SELECT COUNT(*) as total FROM blotter_entries WHERE status = 'On Bail'";
        $active_bail_stmt = $conn->query($active_bail_query);
        $active_bail = $active_bail_stmt->fetch()['total'] ?? 0;
        
        // Third - fetch pending assessments with specific query
        $pending_assessment_query = "
            SELECT COUNT(*) as total FROM blotter_entries be
            LEFT JOIN bail_assessments ba ON be.blotter_id = ba.blotter_id
            WHERE be.status = 'Active' AND (ba.assessment_id IS NULL OR ba.is_bailable IS NULL)
        ";
        $pending_assessment_stmt = $conn->query($pending_assessment_query);
        $pending_assessment = $pending_assessment_stmt->fetch()['total'] ?? 0;
        
        // Fourth - fetch ALL bail status distribution regardless of filtering
        $bail_status_query = "
            SELECT status, COUNT(*) as count 
            FROM blotter_entries
            GROUP BY status
            ORDER BY count DESC
        ";
        
        $bail_status_stmt = $conn->query($bail_status_query);
        
        $bail_status_counts = [];
        $bail_status_data = $bail_status_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Ensure all possible statuses are initialized with 0
        $all_statuses = ['Active', 'On Bail', 'Closed', 'Transferred', 'Dismissed'];
        foreach ($all_statuses as $status) {
            $bail_status_counts[$status] = 0;
        }
        
        // Then fill in actual counts from database
        foreach ($bail_status_data as $row) {
            if (isset($row['status']) && $row['status'] !== null) {
                $bail_status_counts[$row['status']] = (int)$row['count'];
            }
        }
        
        // Fifth - fetch ALL payment data regardless of filtering
        $payment_query = "
            SELECT 
                COALESCE(SUM(amount), 0) as total_amount,
                COUNT(DISTINCT blotter_id) as cases_with_payments,
                COALESCE(AVG(amount), 0) as average_payment
            FROM bail_payments
            WHERE payment_status = 'Completed'
        ";
        
        $payment_stmt = $conn->query($payment_query);
        
        $payment_data = $payment_stmt->fetch(PDO::FETCH_ASSOC);
        $total_bail_amount = $payment_data['total_amount'] ?? 0;
        $cases_with_payments = $payment_data['cases_with_payments'] ?? 0;
        $average_payment = $payment_data['average_payment'] ?? 0;

        // Calculate percentage of cases with bail payments
        $bail_percentage = $total_blotter_count > 0 ? round(($cases_with_payments / $total_blotter_count) * 100, 1) : 0;
        
        // Sixth - fetch ALL payment method distribution regardless of filtering
        $payment_method_query = "
            SELECT 
                payment_method, 
                COUNT(*) as count,
                COALESCE(SUM(amount), 0) as total_amount
            FROM bail_payments
            WHERE payment_status = 'Completed'
            GROUP BY payment_method
            ORDER BY count DESC
        ";
        
        $payment_method_stmt = $conn->query($payment_method_query);
        
        $payment_method_counts = [];
        $payment_method_amounts = [];
        $payment_method_data = $payment_method_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Initialize standard payment methods with 0 values
        $standard_methods = ['Cash', 'Check', 'Bank Transfer', 'Online Payment'];
        foreach ($standard_methods as $method) {
            $payment_method_counts[$method] = 0;
            $payment_method_amounts[$method] = 0;
        }
        
        // Then fill in actual values from database
        foreach ($payment_method_data as $row) {
            if (isset($row['payment_method']) && $row['payment_method'] !== null) {
                $payment_method_counts[$row['payment_method']] = (int)$row['count'];
                $payment_method_amounts[$row['payment_method']] = (float)$row['total_amount'];
            }
        }
        
        // Seventh - fetch top case types with bail regardless of filtering
        $case_type_bail_query = "
            SELECT 
                be.case_type, 
                COUNT(DISTINCT bp.blotter_id) as count,
                COALESCE(AVG(bp.amount), 0) as avg_bail_amount
            FROM bail_payments bp
            JOIN blotter_entries be ON bp.blotter_id = be.blotter_id
            WHERE bp.payment_status = 'Completed'
            GROUP BY be.case_type
            ORDER BY count DESC
            LIMIT 5
        ";
        
        $case_type_bail_stmt = $conn->query($case_type_bail_query);
        $case_type_bail_data = $case_type_bail_stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Check if we have any case type data
        $has_case_type_data = !empty($case_type_bail_data);
        
        // For monthly data, we DO respect the selected year filter
        $monthly_bail_query = "
            SELECT 
                MONTH(payment_date) as month, 
                COALESCE(SUM(amount), 0) as amount 
            FROM bail_payments
            WHERE YEAR(payment_date) = :year
            AND payment_status = 'Completed'
            GROUP BY MONTH(payment_date)
            ORDER BY month
        ";
        
        $monthly_bail_stmt = $conn->prepare($monthly_bail_query);
        $monthly_bail_stmt->bindValue(':year', $selected_year);
        $monthly_bail_stmt->execute();
        
        $monthly_bail_data = array_fill(1, 12, 0); // Initialize all months with 0
        $monthly_bail_results = $monthly_bail_stmt->fetchAll(PDO::FETCH_ASSOC);
        $has_bail_data = false;
        
        foreach ($monthly_bail_results as $row) {
            $monthly_bail_data[intval($row['month'])] = floatval($row['amount']);
            if (floatval($row['amount']) > 0) {
                $has_bail_data = true;
            }
        }
        $monthly_bail_amounts = array_values($monthly_bail_data);
    } else {
        // Sample data for bail status
        $bail_status_counts = [
            'Active' => 25,
            'On Bail' => 45,
            'Closed' => 30,
            'Transferred' => 8,
            'Dismissed' => 12
        ];
        
        // Sample data for payments
        $total_bail_amount = 350000;
        $cases_with_payments = 45;
        $average_payment = 7777.78;
        $total_blotter_count = 120;
        $bail_percentage = 37.5;
        $active_bail = 45;
        $pending_assessment = 15;
        
        // Sample data for payment methods
        $payment_method_counts = [
            'Cash' => 25,
            'Check' => 10,
            'Bank Transfer' => 15,
            'Online Payment' => 5
        ];
        
        $payment_method_amounts = [
            'Cash' => 150000,
            'Check' => 75000,
            'Bank Transfer' => 100000,
            'Online Payment' => 25000
        ];
        
        // Sample data for case types with bail
        $case_type_bail_data = [
            ['case_type' => 'Assault', 'count' => 15, 'avg_bail_amount' => 10000],
            ['case_type' => 'Theft', 'count' => 12, 'avg_bail_amount' => 7500],
            ['case_type' => 'Property Damage', 'count' => 9, 'avg_bail_amount' => 5000],
            ['case_type' => 'Disorderly Conduct', 'count' => 7, 'avg_bail_amount' => 3000],
            ['case_type' => 'Trespassing', 'count' => 5, 'avg_bail_amount' => 2500]
        ];
        
        // Sample data for monthly bail payments
        $monthly_bail_amounts = [25000, 30000, 35000, 20000, 40000, 45000, 30000, 35000, 25000, 30000, 20000, 15000];
    }
} catch (PDOException $e) {
    // Don't show database errors to users
    error_log("Error retrieving bail data: " . $e->getMessage());
    $message = "The bail management reporting system is currently being set up. Please check back later.";
    $message_type = "info";
    
    // Initialize with sample data for demonstration
    $bail_status_counts = [
            'Active' => 25,
            'On Bail' => 45,
            'Closed' => 30,
            'Transferred' => 8,
            'Dismissed' => 12
        ];
        
    // Sample data for payments
    $total_bail_amount = 350000;
    $cases_with_payments = 45;
    $average_payment = 7777.78;
    $total_blotter_count = 120;
    $bail_percentage = 37.5;
    $active_bail = 45;
    $pending_assessment = 15;
    
    // Sample data for payment methods
    $payment_method_counts = [
        'Cash' => 25,
        'Check' => 10,
        'Bank Transfer' => 15,
        'Online Payment' => 5
    ];
    
    $payment_method_amounts = [
        'Cash' => 150000,
        'Check' => 75000,
        'Bank Transfer' => 100000,
        'Online Payment' => 25000
    ];
    
    // Sample data for case types with bail
    $case_type_bail_data = [
        ['case_type' => 'Assault', 'count' => 15, 'avg_bail_amount' => 10000],
        ['case_type' => 'Theft', 'count' => 12, 'avg_bail_amount' => 7500],
        ['case_type' => 'Property Damage', 'count' => 9, 'avg_bail_amount' => 5000],
        ['case_type' => 'Disorderly Conduct', 'count' => 7, 'avg_bail_amount' => 3000],
        ['case_type' => 'Trespassing', 'count' => 5, 'avg_bail_amount' => 2500]
    ];
    
    // Sample data for monthly bail payments
    $monthly_bail_amounts = [25000, 30000, 35000, 20000, 40000, 45000, 30000, 35000, 25000, 30000, 20000, 15000];
    
    $tables_exist = false;
}

// Include header
include_once '../../includes/header.php';
?>

<style>
    /* Case Card Styles */
    .case-card {
        border-radius: 0.75rem;
        overflow: hidden;
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
        position: relative;
        z-index: 1;
    }
    .case-card:hover {
        transform: translateY(-5px) !important;
        box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2) !important;
        z-index: 2;
    }
    
    /* Quick stats cards specific styling */
    .case-card .card-icon {
        font-size: 2.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 60px;
        width: 60px;
        border-radius: 50%;
        transition: transform 0.3s ease;
    }
    .case-card:hover .card-icon {
        transform: scale(1.1);
    }
    
    /* Chart card hover effects */
    .chart-card {
        transition: all 0.3s ease;
        border-radius: 0.75rem;
        overflow: hidden;
        border: none;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
    }
    .chart-card:hover {
        transform: translateY(-5px) !important;
        box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2) !important;
    }
    
    /* Table hover effects */
    .table-card {
        transition: all 0.3s ease;
        border-radius: 0.75rem;
        overflow: hidden;
        border: none;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
    }
    .table-card:hover {
        transform: translateY(-5px) !important;
        box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2) !important;
    }
    
    /* Background colors with opacity */
    .bg-primary-soft { background-color: rgba(78, 115, 223, 0.1) !important; }
    .bg-success-soft { background-color: rgba(28, 200, 138, 0.1) !important; }
    .bg-info-soft { background-color: rgba(54, 185, 204, 0.1) !important; }
    .bg-warning-soft { background-color: rgba(246, 194, 62, 0.1) !important; }
    .bg-danger-soft { background-color: rgba(231, 74, 59, 0.1) !important; }
    
    .chart-container {
        position: relative;
        height: 300px;
        margin-bottom: 20px;
    }
    @media (max-width: 768px) {
        .chart-container {
            height: 250px;
        }
    }
</style>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <?php include_once '../../includes/sidebar.php'; ?>
        
        <!-- Main Content -->
        <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-gavel me-2"></i> Bail Management Reports</h1>
                <div class="btn-toolbar mb-2 mb-md-0">
                    <form action="" method="GET" class="d-flex">
                        <select class="form-select me-2" name="year" onchange="this.form.submit()">
                            <?php foreach($available_years as $year): ?>
                                <option value="<?php echo $year; ?>" <?php echo ($year == $selected_year) ? 'selected' : ''; ?>>
                                    <?php echo $year; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="window.print()">
                            <i class="fas fa-print"></i> Print Report
                        </button>
                    </form>
                </div>
            </div>

            <?php if (!empty($message)): ?>
                <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                    <i class="fas fa-info-circle me-2"></i> <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <!-- Content Row -->
            <div class="row mb-4">
                <!-- Case Summary Cards -->
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card case-card shadow h-100">
                        <div class="card-body p-3">
                            <div class="row align-items-center">
                                <div class="col-4">
                                    <div class="card-icon bg-primary-soft text-primary">
                                        📊
                                    </div>
                                </div>
                                <div class="col-8 text-end">
                                    <h4 class="mt-0 mb-1"><?php echo number_format($total_blotter_count); ?></h4>
                                    <p class="mb-0 text-muted">Total Blotter Cases</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card case-card shadow h-100">
                        <div class="card-body p-3">
                            <div class="row align-items-center">
                                <div class="col-4">
                                    <div class="card-icon bg-success-soft text-success">
                                        💰
                                    </div>
                                </div>
                                <div class="col-8 text-end">
                                    <h4 class="mt-0 mb-1"><?php echo number_format($cases_with_payments); ?></h4>
                                    <p class="mb-0 text-muted">Bail Payments</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card case-card shadow h-100">
                        <div class="card-body p-3">
                            <div class="row align-items-center">
                                <div class="col-4">
                                    <div class="card-icon bg-info-soft text-info">
                                        ⚖️
                                    </div>
                                </div>
                                <div class="col-8 text-end">
                                    <h4 class="mt-0 mb-1"><?php echo number_format($active_bail); ?></h4>
                                    <p class="mb-0 text-muted">On Bail Cases</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card case-card shadow h-100">
                        <div class="card-body p-3">
                            <div class="row align-items-center">
                                <div class="col-4">
                                    <div class="card-icon bg-warning-soft text-warning">
                                        ⏳
                                    </div>
                                </div>
                                <div class="col-8 text-end">
                                    <h4 class="mt-0 mb-1"><?php echo number_format($pending_assessment); ?></h4>
                                    <p class="mb-0 text-muted">Pending Assessments</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bail Overview Metrics -->
            <div class="row mb-4">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <h5 class="card-title">Total Bail Amount</h5>
                            <div class="display-4 font-weight-bold text-success">
                                ₱<?php echo number_format(floatval($total_bail_amount), 2); ?>
                            </div>
                            <p class="card-text">
                                <small class="text-muted">Total bail payments received</small>
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <h5 class="card-title">Bail Cases</h5>
                            <div class="display-4 font-weight-bold text-primary">
                                <?php echo number_format(intval($cases_with_payments)); ?>
                                <span class="fs-6 fw-normal text-muted">
                                    <?php 
                                        echo "({$bail_percentage}%)";
                                    ?>
                                </span>
                            </div>
                            <p class="card-text">
                                <small class="text-muted">Cases with bail payments of <?php echo number_format(intval($total_blotter_count)); ?> total</small>
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-12 mb-4">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <h5 class="card-title">Average Bail Amount</h5>
                            <div class="display-4 font-weight-bold text-info">
                                ₱<?php echo number_format(floatval($average_payment), 2); ?>
                            </div>
                            <p class="card-text">
                                <small class="text-muted">Average bail amount per case</small>
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bail Charts Row -->
            <div class="row">
                <!-- Bail Status Distribution -->
                <div class="col-xl-6 col-lg-6">
                    <div class="card chart-card shadow mb-4">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">Bail Status Distribution</h6>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="bailStatusChart"></canvas>
                            </div>
                            <?php if (!$tables_exist || !isset($bail_tables_exist) || !$bail_tables_exist): ?>
                            <div class="mt-3 text-center">
                                <small class="text-muted">Sample data shown. Actual data will be displayed once the bail management module is set up.</small>
                            </div>
                            <?php elseif (array_sum($bail_status_counts) == 0): ?>
                            <div class="mt-3 text-center">
                                <small class="text-muted">No bail status data available for the selected time period.</small>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Payment Method Distribution -->
                <div class="col-xl-6 col-lg-6">
                    <div class="card chart-card shadow mb-4">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">Payment Method Distribution</h6>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="paymentMethodChart"></canvas>
                            </div>
                            <?php if (!$tables_exist || !isset($bail_tables_exist) || !$bail_tables_exist): ?>
                            <div class="mt-3 text-center">
                                <small class="text-muted">Sample data shown. Actual data will be displayed once the bail management module is set up.</small>
                            </div>
                            <?php elseif (array_sum($payment_method_counts) == 0): ?>
                            <div class="mt-3 text-center">
                                <small class="text-muted">No payment method data available for the selected time period.</small>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Monthly Bail Payments and Case Types with Bail -->
            <div class="row">
                <!-- Monthly Bail Payments -->
                <div class="col-xl-8 col-lg-7">
                    <div class="card chart-card shadow mb-4">
                        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">Monthly Bail Payments</h6>
                        </div>
                        <div class="card-body">
                            <div class="chart-container">
                                <canvas id="monthlyBailChart"></canvas>
                            </div>
                            <?php if (!$tables_exist || !isset($bail_tables_exist) || !$bail_tables_exist): ?>
                            <div class="mt-3 text-center">
                                <small class="text-muted">Sample data shown. Actual data will be displayed once the bail management module is set up.</small>
                            </div>
                            <?php elseif (!$has_bail_data): ?>
                            <div class="mt-3 text-center">
                                <small class="text-muted">No bail payment data available for <?php echo $selected_year; ?>.</small>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Case Types with Bail -->
                <div class="col-xl-4 col-lg-5">
                    <div class="card table-card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                            <h6 class="m-0 font-weight-bold text-primary">Top Case Types with Bail</h6>
                        </div>
                        <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Case Type</th>
                                        <th>Count</th>
                                            <th>Avg. Bail Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                        <?php if (!empty($case_type_bail_data)): ?>
                                            <?php foreach ($case_type_bail_data as $case_type): ?>
                                            <tr>
                                                <td><?php echo $case_type['case_type']; ?></td>
                                                <td><?php echo $case_type['count']; ?></td>
                                                <td>₱<?php echo number_format($case_type['avg_bail_amount'], 2); ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                                <td colspan="3" class="text-center">No case types with bail data available</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                            <?php if (!$tables_exist || !isset($bail_tables_exist) || !$bail_tables_exist): ?>
                            <div class="mt-3 text-center">
                                <small class="text-muted">Sample data shown. Actual data will be displayed once the bail management module is set up.</small>
                        </div>
                    <?php endif; ?>
                        </div>
                    </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Print Button -->
            <div class="text-end mb-4">
                <button class="btn btn-primary" onclick="window.print()">
                    <i class="fas fa-print me-2"></i> Print Report
                </button>
            </div> 
        </main>
    </div>
</div>

<?php include '../../includes/footer.php'; ?>

<!-- Chart.js Library -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<!-- Page specific scripts -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Bail Status Distribution Chart
        var bailStatusCtx = document.getElementById('bailStatusChart').getContext('2d');
        var bailStatusLabels = <?php 
            if (!empty($bail_status_counts)) {
                echo json_encode(array_keys($bail_status_counts));
            } else {
                echo json_encode(['Active', 'On Bail', 'Closed', 'Transferred', 'Dismissed']);
            }
        ?>;
        var bailStatusData = <?php 
            if (!empty($bail_status_counts)) {
                echo json_encode(array_values($bail_status_counts));
            } else {
                echo json_encode([25, 45, 30, 8, 12]);
            }
        ?>;
        
        var bailStatusColors = [
            'rgba(255, 193, 7, 0.8)',  // Active - Warning
            'rgba(0, 123, 255, 0.8)',  // On Bail - Primary
            'rgba(40, 167, 69, 0.8)',  // Closed - Success
            'rgba(23, 162, 184, 0.8)',  // Transferred - Info
            'rgba(220, 53, 69, 0.8)'   // Dismissed - Danger
        ];
        
        var bailStatusChart = new Chart(bailStatusCtx, {
            type: 'doughnut',
            data: {
                labels: bailStatusLabels,
                datasets: [{
                    data: bailStatusData,
                    backgroundColor: bailStatusColors,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '50%',
                plugins: {
                    legend: {
                        position: 'right',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                var label = context.label || '';
                                var value = context.raw || 0;
                                var total = context.dataset.data.reduce((a, b) => a + b, 0);
                                var percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                                return label + ': ' + value + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });

        // Payment Method Distribution Chart
        var paymentMethodCtx = document.getElementById('paymentMethodChart').getContext('2d');
        var paymentMethodLabels = <?php 
            if (!empty($payment_method_counts)) {
                echo json_encode(array_keys($payment_method_counts));
            } else {
                echo json_encode(['Cash', 'Check', 'Bank Transfer', 'Online Payment']);
            }
        ?>;
        var paymentMethodData = <?php 
            if (!empty($payment_method_counts)) {
                echo json_encode(array_values($payment_method_counts));
            } else {
                echo json_encode([25, 10, 15, 5]);
            }
        ?>;
        
        var paymentMethodColors = [
            'rgba(40, 167, 69, 0.8)',  // Cash - Success
            'rgba(0, 123, 255, 0.8)',  // Check - Primary
            'rgba(255, 193, 7, 0.8)',  // Bank Transfer - Warning
            'rgba(23, 162, 184, 0.8)'   // Online Payment - Info
        ];
        
        var paymentMethodChart = new Chart(paymentMethodCtx, {
            type: 'pie',
            data: {
                labels: paymentMethodLabels,
                datasets: [{
                    data: paymentMethodData,
                    backgroundColor: paymentMethodColors,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                var label = context.label || '';
                                var value = context.raw || 0;
                                var total = context.dataset.data.reduce((a, b) => a + b, 0);
                                var percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                                return label + ': ' + value + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });

        // Monthly Bail Payments Chart
        var monthlyBailCtx = document.getElementById('monthlyBailChart').getContext('2d');
        var monthlyBailData = <?php echo !empty($monthly_bail_amounts) ? json_encode($monthly_bail_amounts) : json_encode(array_fill(0, 12, 0)); ?>;
        
        var monthlyBailChart = new Chart(monthlyBailCtx, {
            type: 'bar',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                datasets: [{
                    label: 'Bail Payments (₱)',
                    data: monthlyBailData,
                    backgroundColor: 'rgba(0, 123, 255, 0.6)',
                    borderColor: 'rgba(0, 123, 255, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '₱' + new Intl.NumberFormat().format(value);
                            }
                        }
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return '₱' + new Intl.NumberFormat().format(context.raw);
                            }
                        }
                    },
                    legend: {
                        display: false
                    }
                }
            }
        });
    });
</script>

</body>
</html> 