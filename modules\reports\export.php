<?php
include '../../includes/functions/permission_functions.php';
// Start session
session_start();

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include necessary files
require_once '../../includes/config/config.php';
require_once '../../includes/config/database.php';
require_once '../../includes/functions/utility.php';
require_once '../../vendor/autoload.php';
use Dompdf\Dompdf;
use Dompdf\Options;

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit();
}

// Check if user has permission for reports
if (!hasPermission('view_reports') && !hasPermission('admin')) {
    header("Location: ../../index.php");
    exit();
}

// Initialize variables
$message = "";
$message_type = "";
$report_type = isset($_GET['type']) ? $_GET['type'] : '';
$format = isset($_GET['format']) ? $_GET['format'] : 'csv';
$year = isset($_GET['year']) && $_GET['year'] !== '' ? intval($_GET['year']) : null;
$month = isset($_GET['month']) && $_GET['month'] !== '' ? intval($_GET['month']) : null;

// Set content type based on export format
if ($format == 'csv' && !empty($report_type)) {
    // Set headers for file download
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $report_type . '_report_' . date('Y-m-d') . '.csv"');
    
    // Create output file handle
    $output = fopen('php://output', 'w');
    
    // Generate CSV file content based on report type
    generateCsvReport($output, $report_type, $year, $month);
    
    // Close file handle and exit
    fclose($output);
    exit();
}
if ($format == 'pdf' && !empty($report_type)) {
    $dompdf = new Dompdf();
    $dompdf->setPaper('A4', 'portrait');

    // Generate HTML for the report using actual data
    ob_start();
    echo '<h2 style="text-align:center;">' . ucfirst($report_type) . ' Report</h2>';
    echo '<p><strong>Year:</strong> ' . $year . '</p>';
    if ($month) echo '<p><strong>Month:</strong> ' . $month . '</p>';
    echo '<table border="1" cellpadding="5" cellspacing="0" width="100%">';
    switch ($report_type) {
        case 'case':
            echo '<thead><tr><th>Complaint ID</th><th>Type</th><th>Description</th><th>Complainant</th><th>Respondent</th><th>Incident Date</th><th>Date Filed</th><th>Status</th><th>Assigned To</th><th>Resolution Date</th></tr></thead><tbody>';
            $data = [];
            $date_column = 'date_filed';
            $resolution_date_column = 'resolution_date';
            try {
                $check_stmt = $conn->prepare("SHOW COLUMNS FROM complaints LIKE 'date_filed'");
                $check_stmt->execute();
                if ($check_stmt->rowCount() == 0) {
                    $check_stmt = $conn->prepare("SHOW COLUMNS FROM complaints LIKE 'date_created'");
                    $check_stmt->execute();
                    if ($check_stmt->rowCount() > 0) $date_column = 'date_created';
                    else {
                        $check_stmt = $conn->prepare("SHOW COLUMNS FROM complaints LIKE 'created_at'");
                        $check_stmt->execute();
                        if ($check_stmt->rowCount() > 0) $date_column = 'created_at';
                    }
                }
                $check_stmt = $conn->prepare("SHOW COLUMNS FROM complaints LIKE 'resolution_date'");
                $check_stmt->execute();
                if ($check_stmt->rowCount() == 0) {
                    $check_stmt = $conn->prepare("SHOW COLUMNS FROM complaints LIKE 'settlement_date'");
                    $check_stmt->execute();
                    if ($check_stmt->rowCount() > 0) $resolution_date_column = 'settlement_date';
                }
            } catch (PDOException $e) {}
            $conditions = [];
            $params = [];
            if (!is_null($year)) {
                $conditions[] = "YEAR(c.$date_column) = :year";
                $params[':year'] = $year;
            }
            if (!is_null($month)) {
                $conditions[] = "MONTH(c.$date_column) = :month";
                $params[':month'] = $month;
            }
            $where_clause = !empty($conditions) ? "WHERE " . implode(" AND ", $conditions) : "";
            $stmt = $conn->prepare("
                SELECT 
                    c.complaint_id,
                    c.complaint_type,
                    c.complaint_details AS description,
                    CONCAT(rc.last_name, ', ', rc.first_name) AS complainant_name,
                    CASE 
                        WHEN c.respondent_id IS NOT NULL THEN CONCAT(rr.last_name, ', ', rr.first_name)
                        ELSE c.respondent_name
                    END AS respondent_name,
                    c.incident_date,
                    c.$date_column AS date_filed,
                    c.status,
                    CONCAT(oRes.first_name, ' ', oRes.last_name, IFNULL(CONCAT(' (', o.position, ')'), '')) AS assigned_to,
                    c.$resolution_date_column AS resolution_date
                FROM complaints c
                LEFT JOIN residents rc ON c.complainant_id = rc.resident_id
                LEFT JOIN residents rr ON c.respondent_id = rr.resident_id
                LEFT JOIN officials o ON c.assigned_official_id = o.official_id
                LEFT JOIN residents oRes ON o.resident_id = oRes.resident_id
                $where_clause
                ORDER BY c.$date_column DESC
            ");
            foreach ($params as $key => $value) $stmt->bindValue($key, $value);
            $stmt->execute();
            $rowCount = 0;
            while ($row = $stmt->fetch()) {
                $rowCount++;
                // Suppress deprecated warning in complainant_name
                $complainant = isset($row['complainant_name']) ? htmlspecialchars($row['complainant_name']) : '';
                $respondent = isset($row['respondent_name']) ? htmlspecialchars($row['respondent_name']) : '';
                $description = isset($row['description']) ? htmlspecialchars($row['description']) : '';
                $incident_date = isset($row['incident_date']) ? htmlspecialchars($row['incident_date']) : '';
                $date_filed = isset($row['date_filed']) ? htmlspecialchars($row['date_filed']) : '';
                $status = isset($row['status']) ? htmlspecialchars($row['status']) : '';
                $assigned_to = isset($row['assigned_to']) ? htmlspecialchars($row['assigned_to']) : 'Unassigned';
                $resolution_date = isset($row['resolution_date']) && $row['resolution_date'] ? htmlspecialchars($row['resolution_date']) : 'N/A';
                echo '<tr>';
                echo '<td style="vertical-align:top;">' . htmlspecialchars($row['complaint_id']) . '</td>';
                echo '<td style="vertical-align:top;">' . htmlspecialchars($row['complaint_type']) . '</td>';
                echo '<td style="vertical-align:top;">' . $description . '</td>';
                echo '<td style="vertical-align:top;">' . $complainant . '</td>';
                echo '<td style="vertical-align:top;">' . $respondent . '</td>';
                echo '<td style="vertical-align:top;">' . $incident_date . '</td>';
                echo '<td style="vertical-align:top;">' . $date_filed . '</td>';
                echo '<td style="vertical-align:top;">' . $status . '</td>';
                echo '<td style="vertical-align:top;">' . $assigned_to . '</td>';
                echo '<td style="vertical-align:top;">' . $resolution_date . '</td>';
                echo '</tr>';
            }
            if ($rowCount === 0) {
                echo '<tr><td colspan="10">No data found for the selected filters.</td></tr>';
            }
            echo '</tbody>';
            break;
        case 'project':
            echo '<thead><tr><th>Project ID</th><th>Project Name</th><th>Type</th><th>Description</th><th>Location</th><th>Start Date</th><th>End Date</th><th>Budget</th><th>Status</th><th>Completion %</th></tr></thead><tbody>';
            $conditions = [];
            $params = [];
            if (!is_null($year)) {
                $conditions[] = "(YEAR(start_date) = :year OR YEAR(end_date) = :year)";
                $params[':year'] = $year;
            }
            if (!is_null($month)) {
                $conditions[] = "(MONTH(start_date) = :month OR MONTH(end_date) = :month)";
                $params[':month'] = $month;
            }
            $where_clause = !empty($conditions) ? "WHERE " . implode(" AND ", $conditions) : "";
            $stmt = $conn->prepare("
                SELECT 
                    project_id,
                    project_name,
                    project_type,
                    description,
                    location,
                    start_date,
                    end_date,
                    budget,
                    status,
                    percent_completed
                FROM projects
                $where_clause
                ORDER BY start_date DESC
            ");
            foreach ($params as $key => $value) $stmt->bindValue($key, $value);
            $stmt->execute();
            $rowCount = 0;
            while ($row = $stmt->fetch()) {
                $rowCount++;
                echo '<tr>';
                echo '<td>' . htmlspecialchars($row['project_id']) . '</td>';
                echo '<td>' . htmlspecialchars($row['project_name']) . '</td>';
                echo '<td>' . htmlspecialchars($row['project_type']) . '</td>';
                echo '<td>' . htmlspecialchars($row['description']) . '</td>';
                echo '<td>' . htmlspecialchars($row['location']) . '</td>';
                echo '<td>' . htmlspecialchars($row['start_date']) . '</td>';
                echo '<td>' . htmlspecialchars($row['end_date']) . '</td>';
                echo '<td>' . htmlspecialchars($row['budget']) . '</td>';
                echo '<td>' . htmlspecialchars($row['status']) . '</td>';
                echo '<td>' . htmlspecialchars($row['percent_completed']) . '%</td>';
                echo '</tr>';
            }
            if ($rowCount === 0) {
                echo '<tr><td colspan="10">No data found for the selected filters.</td></tr>';
            }
            echo '</tbody>';
            break;
        case 'population':
            echo '<thead><tr><th>ID</th><th>Name</th><th>Gender</th><th>Age</th><th>Address</th><th>Contact Number</th><th>Voter Status</th></tr></thead><tbody>';
            $stmt = $conn->prepare("
                SELECT 
                    resident_id,
                    CONCAT(last_name, ', ', first_name, ' ', COALESCE(middle_name, '')) AS full_name,
                    gender,
                    TIMESTAMPDIFF(YEAR, birthdate, CURDATE()) AS age,
                    address,
                    contact_number,
                    voter_status
                FROM residents
                WHERE status = 'Active'
                ORDER BY last_name, first_name
            ");
            $stmt->execute();
            $rowCount = 0;
            while ($row = $stmt->fetch()) {
                $rowCount++;
                echo '<tr>';
                echo '<td>' . htmlspecialchars($row['resident_id']) . '</td>';
                echo '<td>' . htmlspecialchars($row['full_name']) . '</td>';
                echo '<td>' . htmlspecialchars($row['gender']) . '</td>';
                echo '<td>' . htmlspecialchars($row['age']) . '</td>';
                echo '<td>' . htmlspecialchars($row['address']) . '</td>';
                echo '<td>' . htmlspecialchars($row['contact_number']) . '</td>';
                echo '<td>' . htmlspecialchars($row['voter_status']) . '</td>';
                echo '</tr>';
            }
            if ($rowCount === 0) {
                echo '<tr><td colspan="7">No data found for the selected filters.</td></tr>';
            }
            echo '</tbody>';
            break;
        case 'financial':
            echo '<thead><tr><th>Transaction ID</th><th>Type</th><th>Category</th><th>Amount</th><th>Date</th><th>Reference #</th><th>Description</th><th>Status</th><th>Recorded By</th><th>Approved By</th><th>Date Recorded</th></tr></thead><tbody>';
            $conditions = [];
            $params = [];
            if (!empty($year)) {
                $conditions[] = "YEAR(transaction_date) = ?";
                $params[] = $year;
            }
            if (!is_null($month) && $month !== "" && $month !== 0) {
                $conditions[] = "MONTH(transaction_date) = ?";
                $params[] = $month;
            }
            $where_clause = !empty($conditions) ? " WHERE " . implode(" AND ", $conditions) : "";
            $sql = "SELECT * FROM finances" . $where_clause . " ORDER BY transaction_date DESC, date_recorded DESC";
            $stmt = $conn->prepare($sql);
            foreach ($params as $key => $value) {
                $stmt->bindValue($key + 1, $value);
            }
            $stmt->execute();
            $rowCount = 0;
            while ($row = $stmt->fetch()) {
                $rowCount++;
                echo '<tr>';
                echo '<td>' . htmlspecialchars($row['finance_id']) . '</td>';
                echo '<td>' . htmlspecialchars($row['transaction_type']) . '</td>';
                echo '<td>' . htmlspecialchars($row['category']) . '</td>';
                echo '<td>' . htmlspecialchars($row['amount']) . '</td>';
                echo '<td>' . htmlspecialchars($row['transaction_date']) . '</td>';
                echo '<td>' . htmlspecialchars($row['reference_number']) . '</td>';
                echo '<td>' . htmlspecialchars($row['description']) . '</td>';
                echo '<td>' . htmlspecialchars($row['status']) . '</td>';
                echo '<td>' . htmlspecialchars($row['recorded_by']) . '</td>';
                echo '<td>' . htmlspecialchars($row['approved_by']) . '</td>';
                echo '<td>' . htmlspecialchars($row['date_recorded']) . '</td>';
                echo '</tr>';
            }
            if ($rowCount === 0) {
                echo '<tr><td colspan="11">No data found for the selected filters.</td></tr>';
            }
            echo '</tbody>';
            break;

        case 'voters':
            echo '<thead><tr><th>Resident ID</th><th>Full Name</th><th>Gender</th><th>Age</th><th>Address</th><th>Contact</th><th>Precinct</th><th>Registration Date</th></tr></thead><tbody>';

            // Build query for voters data
            $query = "SELECT
                r.resident_id,
                CONCAT(r.first_name, ' ', IFNULL(r.middle_name, ''), ' ', r.last_name) as full_name,
                r.gender,
                TIMESTAMPDIFF(YEAR, r.birthdate, CURDATE()) as age,
                CONCAT(r.house_number, ' ', r.street, ', ', r.barangay) as address,
                r.contact_number,
                r.precinct_number,
                r.voter_registration_date
                FROM residents r
                WHERE r.voter_status = 1";

            // Add date filters if specified
            $params = [];
            if ($year) {
                $query .= " AND YEAR(r.voter_registration_date) = ?";
                $params[] = $year;
            }
            if ($month) {
                $query .= " AND MONTH(r.voter_registration_date) = ?";
                $params[] = $month;
            }

            $query .= " ORDER BY r.last_name, r.first_name";

            $stmt = $conn->prepare($query);
            $stmt->execute($params);

            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                echo '<tr>';
                echo '<td>' . htmlspecialchars($row['resident_id']) . '</td>';
                echo '<td>' . htmlspecialchars($row['full_name']) . '</td>';
                echo '<td>' . htmlspecialchars($row['gender']) . '</td>';
                echo '<td>' . htmlspecialchars($row['age']) . '</td>';
                echo '<td>' . htmlspecialchars($row['address']) . '</td>';
                echo '<td>' . htmlspecialchars($row['contact_number'] ?? '') . '</td>';
                echo '<td>' . htmlspecialchars($row['precinct_number'] ?? '') . '</td>';
                echo '<td>' . htmlspecialchars($row['voter_registration_date'] ?? '') . '</td>';
                echo '</tr>';
            }
            echo '</tbody>';
            break;

        default:
            echo '<tr><td colspan="10">Invalid report type or not specified</td></tr></tbody>';
            break;
    }
    echo '</table>';
    $html = ob_get_clean();
    $dompdf->loadHtml($html);
    $dompdf->render();
    $dompdf->stream($report_type . '_report_' . date('Y-m-d') . '.pdf', ["Attachment" => 1]);
    exit();
} else {
    // Set page title and module info for the interface
    $page_title = "Export Reports";
    $module_name = "Reports";
    $sub_module = "Export";
    
    // Include header
    include_once '../../includes/header.php';
}

/**
 * Generate CSV report data
 *
 * @param resource $handle File handle to write to
 * @param string $report_type Type of report to generate
 * @param int $year Year to filter data by
 * @param int|null $month Month to filter data by (optional)
 * @return void
 */
function generateCsvReport($handle, $report_type, $year, $month = null) {
    global $conn;
    try {
        switch ($report_type) {
            case 'case':
                fputcsv($handle, [
                    'Complaint ID', 'Type', 'Description', 'Complainant', 'Respondent', 'Incident Date', 'Date Filed', 'Status', 'Assigned To', 'Resolution Date'
                ]);
                $date_column = 'date_filed';
                $resolution_date_column = 'resolution_date';
                try {
                    $check_stmt = $conn->prepare("SHOW COLUMNS FROM complaints LIKE 'date_filed'");
                    $check_stmt->execute();
                    if ($check_stmt->rowCount() == 0) {
                        $check_stmt = $conn->prepare("SHOW COLUMNS FROM complaints LIKE 'date_created'");
                        $check_stmt->execute();
                        if ($check_stmt->rowCount() > 0) {
                            $date_column = 'date_created';
                        } else {
                            $check_stmt = $conn->prepare("SHOW COLUMNS FROM complaints LIKE 'created_at'");
                            $check_stmt->execute();
                            if ($check_stmt->rowCount() > 0) {
                                $date_column = 'created_at';
                            }
                        }
                    }
                    $check_stmt = $conn->prepare("SHOW COLUMNS FROM complaints LIKE 'resolution_date'");
                    $check_stmt->execute();
                    if ($check_stmt->rowCount() == 0) {
                        $check_stmt = $conn->prepare("SHOW COLUMNS FROM complaints LIKE 'settlement_date'");
                        $check_stmt->execute();
                        if ($check_stmt->rowCount() > 0) {
                            $resolution_date_column = 'settlement_date';
                        }
                    }
                } catch (PDOException $e) {
                    error_log("Error checking complaint table columns: " . $e->getMessage());
                }
                $conditions = [];
                $params = [];
                if (!is_null($year)) {
                    $conditions[] = "YEAR(c.$date_column) = :year";
                    $params[':year'] = $year;
                }
                if (!is_null($month)) {
                    $conditions[] = "MONTH(c.$date_column) = :month";
                    $params[':month'] = $month;
                }
                $where_clause = !empty($conditions) ? "WHERE " . implode(" AND ", $conditions) : "";
                $sql = "SELECT c.complaint_id, c.complaint_type, c.complaint_details AS description, CONCAT(rc.last_name, ', ', rc.first_name) AS complainant_name, CASE WHEN c.respondent_id IS NOT NULL THEN CONCAT(rr.last_name, ', ', rr.first_name) ELSE c.respondent_name END AS respondent_name, c.incident_date, c.$date_column AS date_filed, c.status, CONCAT(oRes.first_name, ' ', oRes.last_name, IFNULL(CONCAT(' (', o.position, ')'), '')) AS assigned_to, c.$resolution_date_column AS resolution_date FROM complaints c LEFT JOIN residents rc ON c.complainant_id = rc.resident_id LEFT JOIN residents rr ON c.respondent_id = rr.resident_id LEFT JOIN officials o ON c.assigned_official_id = o.official_id LEFT JOIN residents oRes ON o.resident_id = oRes.resident_id $where_clause ORDER BY c.$date_column DESC";
                error_log('CASE SQL: ' . $sql . ' PARAMS: ' . json_encode($params));
                $stmt = $conn->prepare($sql);
                foreach ($params as $key => $value) {
                    $stmt->bindValue($key, $value);
                }
                $stmt->execute();
                $rowCount = 0;
                while ($row = $stmt->fetch()) {
                    $rowCount++;
                    fputcsv($handle, [
                        $row['complaint_id'], $row['complaint_type'], $row['description'], $row['complainant_name'], $row['respondent_name'], $row['incident_date'], $row['date_filed'], $row['status'], $row['assigned_to'] ?: 'Unassigned', $row['resolution_date'] ?: 'N/A'
                    ]);
                }
                if ($rowCount === 0) {
                    fputcsv($handle, ['No data found for the selected filters.']);
                }
                break;
                
            case 'project':
                // Headers
                fputcsv($handle, [
                    'Project ID', 
                    'Project Name', 
                    'Type', 
                    'Description', 
                    'Location', 
                    'Start Date', 
                    'End Date', 
                    'Budget', 
                    'Status', 
                    'Completion %'
                ]);
                
                // Build query
                $conditions = ["(YEAR(start_date) = :year OR YEAR(end_date) = :year)"];
                $params = [':year' => $year];
                
                if (!is_null($month)) {
                    $conditions[] = "(MONTH(start_date) = :month OR MONTH(end_date) = :month)";
                    $params[':month'] = $month;
                }
                
                $where_clause = !empty($conditions) ? "WHERE " . implode(" AND ", $conditions) : "";
                
                // Query the database
                $stmt = $conn->prepare("
                    SELECT 
                        project_id,
                        project_name,
                        project_type,
                        description,
                        location,
                        start_date,
                        end_date,
                        budget,
                        status,
                        percent_completed
                    FROM projects
                    $where_clause
                    ORDER BY start_date DESC
                ");
                
                foreach ($params as $key => $value) {
                    $stmt->bindValue($key, $value);
                }
                
                $stmt->execute();
                $rowCount = 0;
                while ($row = $stmt->fetch()) {
                    $rowCount++;
                    fputcsv($handle, [
                        $row['project_id'], $row['project_name'], $row['project_type'], $row['description'], $row['location'], $row['start_date'], $row['end_date'], $row['budget'], $row['status'], $row['percent_completed'] . '%'
                    ]);
                }
                if ($rowCount === 0) {
                    fputcsv($handle, ['No data found for the selected filters.']);
                }
                break;
                
            case 'population':
                // Headers
                fputcsv($handle, [
                    'ID', 
                    'Name', 
                    'Gender', 
                    'Age', 
                    'Address', 
                    'Contact Number', 
                    'Voter Status'
                ]);
                
                // Query the database
                $stmt = $conn->prepare("
                    SELECT 
                        resident_id,
                        CONCAT(last_name, ', ', first_name, ' ', COALESCE(middle_name, '')) AS full_name,
                        gender,
                        TIMESTAMPDIFF(YEAR, birthdate, CURDATE()) AS age,
                        address,
                        contact_number,
                        voter_status
                    FROM residents
                    WHERE status = 'Active'
                    ORDER BY last_name, first_name
                ");
                
                $stmt->execute();
                $rowCount = 0;
                while ($row = $stmt->fetch()) {
                    $rowCount++;
                    fputcsv($handle, [
                        $row['resident_id'], $row['full_name'], $row['gender'], $row['age'], $row['address'], $row['contact_number'], $row['voter_status']
                    ]);
                }
                if ($rowCount === 0) {
                    fputcsv($handle, ['No data found for the selected filters.']);
                }
                break;
                
            case 'financial':
                // Headers
                fputcsv($handle, [
                    'Transaction ID',
                    'Type',
                    'Category',
                    'Amount',
                    'Date',
                    'Reference #',
                    'Description',
                    'Status',
                    'Recorded By',
                    'Approved By',
                    'Date Recorded'
                ]);
                // Build query (show all if no year/month selected)
                $conditions = [];
                $params = [];
                if (!empty($year)) {
                    $conditions[] = "YEAR(transaction_date) = ?";
                    $params[] = $year;
                }
                if (!is_null($month) && $month !== "" && $month !== 0) {
                    $conditions[] = "MONTH(transaction_date) = ?";
                    $params[] = $month;
                }
                $where_clause = !empty($conditions) ? " WHERE " . implode(" AND ", $conditions) : "";
                $sql = "SELECT * FROM finances" . $where_clause . " ORDER BY transaction_date DESC, date_recorded DESC";
                $stmt = $conn->prepare($sql);
                foreach ($params as $key => $value) {
                    $stmt->bindValue($key + 1, $value);
                }
                $stmt->execute();
                $rowCount = 0;
                while ($row = $stmt->fetch()) {
                    $rowCount++;
                    fputcsv($handle, [
                        $row['finance_id'],
                        $row['transaction_type'],
                        $row['category'],
                        $row['amount'],
                        $row['transaction_date'],
                        $row['reference_number'],
                        $row['description'],
                        $row['status'],
                        $row['recorded_by'],
                        $row['approved_by'],
                        $row['date_recorded']
                    ]);
                }
                if ($rowCount === 0) {
                    fputcsv($handle, ['No data found for the selected filters.']);
                }
                break;

            case 'voters':
                // Write CSV headers for voters report
                fputcsv($handle, [
                    'Resident ID', 'Full Name', 'Gender', 'Age', 'Birthdate', 'Address', 'Contact Number', 'Email', 'Voter Status', 'Precinct Number', 'Registration Date'
                ]);

                // Build query for voters data
                $query = "SELECT
                    r.resident_id,
                    CONCAT(r.first_name, ' ', IFNULL(r.middle_name, ''), ' ', r.last_name) as full_name,
                    r.gender,
                    TIMESTAMPDIFF(YEAR, r.birthdate, CURDATE()) as age,
                    r.birthdate,
                    CONCAT(r.house_number, ' ', r.street, ', ', r.barangay, ', ', r.municipality, ', ', r.province) as address,
                    r.contact_number,
                    r.email,
                    CASE WHEN r.voter_status = 1 THEN 'Registered' ELSE 'Not Registered' END as voter_status,
                    r.precinct_number,
                    r.voter_registration_date
                    FROM residents r
                    WHERE r.voter_status = 1";

                // Add date filters if specified
                $params = [];
                if ($year) {
                    $query .= " AND YEAR(r.voter_registration_date) = ?";
                    $params[] = $year;
                }
                if ($month) {
                    $query .= " AND MONTH(r.voter_registration_date) = ?";
                    $params[] = $month;
                }

                $query .= " ORDER BY r.last_name, r.first_name";

                $stmt = $conn->prepare($query);
                $stmt->execute($params);

                $rowCount = 0;
                while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                    fputcsv($handle, [
                        $row['resident_id'],
                        $row['full_name'],
                        $row['gender'],
                        $row['age'],
                        $row['birthdate'],
                        $row['address'],
                        $row['contact_number'] ?? '',
                        $row['email'] ?? '',
                        $row['voter_status'],
                        $row['precinct_number'] ?? '',
                        $row['voter_registration_date'] ?? ''
                    ]);
                    $rowCount++;
                }
                if ($rowCount === 0) {
                    fputcsv($handle, ['No registered voters found for the selected filters.']);
                }
                break;

            default:
                // Write error message if report type is invalid
                fputcsv($handle, ['Invalid report type or not specified']);
                break;
        }
    } catch (PDOException $e) {
        // Write error message
        fputcsv($handle, ['Error retrieving data: ' . $e->getMessage()]);
        error_log("Export error: " . $e->getMessage());
    }
}

?>

<style>
    /* Export Card Styles */
    .export-card {
        border-radius: 0.75rem;
        overflow: hidden;
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
        position: relative;
        z-index: 1;
    }
    .export-card:hover {
        transform: translateY(-5px) !important;
        box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2) !important;
        z-index: 2;
    }
    
    /* Card icon styling */
    .export-card .card-icon {
        font-size: 2.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 60px;
        width: 60px;
        border-radius: 50%;
        margin: 0 auto 15px;
        transition: transform 0.3s ease;
    }
    .export-card:hover .card-icon {
        transform: scale(1.1);
    }
    
    /* Chart card hover effects */
    .chart-card {
        transition: all 0.3s ease;
        border-radius: 0.75rem;
        overflow: hidden;
        border: none;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
    }
    .chart-card:hover {
        transform: translateY(-5px) !important;
        box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2) !important;
    }
    
    /* Table hover effects */
    .table-card {
        transition: all 0.3s ease;
        border-radius: 0.75rem;
        overflow: hidden;
        border: none;
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
    }
    .table-card:hover {
        transform: translateY(-5px) !important;
        box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2) !important;
    }
    
    /* Background colors with opacity */
    .bg-primary-soft { background-color: rgba(78, 115, 223, 0.1) !important; }
    .bg-success-soft { background-color: rgba(28, 200, 138, 0.1) !important; }
    .bg-info-soft { background-color: rgba(54, 185, 204, 0.1) !important; }
    .bg-warning-soft { background-color: rgba(246, 194, 62, 0.1) !important; }
    .bg-danger-soft { background-color: rgba(231, 74, 59, 0.1) !important; }
    .bg-secondary-soft { background-color: rgba(108, 117, 125, 0.1) !important; }
</style>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <?php include_once '../../includes/sidebar.php'; ?>
        
        <!-- Main Content -->
        <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                <h1 class="h2"><i class="fas fa-file-export me-2"></i> Export Reports</h1>
            </div>

            <?php if (!empty($message)): ?>
                <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                    <i class="fas fa-info-circle me-2"></i> <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card chart-card shadow mb-4">
                        <div class="card-header py-3">
                            <h6 class="m-0 font-weight-bold text-primary">Select Report to Export</h6>
                        </div>
                        <div class="card-body">
                            <p class="mb-4">Select the report type and format to export data. The exported file will contain detailed information based on the selected parameters.</p>
                            
                            <form action="" method="GET" class="mb-4">
                                <div class="row g-3 align-items-center">
                                    <div class="col-md-3">
                                        <label for="type" class="form-label">Report Type</label>
                                        <select class="form-select" id="type" name="type" required>
                                            <option value="" disabled selected>Select a report type...</option>
                                            <option value="case">Case Reports</option>
                                            <option value="project">Project Reports</option>
                                            <option value="population">Population Reports</option>
                                            <option value="financial">Financial Reports</option>
                                            <option value="voters">Registered Voters</option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-2">
                                        <label for="format" class="form-label">Format</label>
                                        <select class="form-select" id="format" name="format" required>
                                            <option value="csv" selected>CSV</option>
                                            <option value="pdf">PDF</option>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-2">
                                        <label for="year" class="form-label">Year</label>
                                        <select class="form-select" id="year" name="year">
                                            <?php 
                                            $current_year = date('Y');
                                            for ($y = $current_year; $y >= $current_year - 5; $y--) {
                                                $selected = ($y == $current_year) ? 'selected' : '';
                                                echo "<option value=\"$y\" $selected>$y</option>";
                                            }
                                            ?>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-2">
                                        <label for="month" class="form-label">Month (Optional)</label>
                                        <select class="form-select" id="month" name="month">
                                            <option value="">All Months</option>
                                            <?php
                                            $months = [
                                                1 => 'January', 2 => 'February', 3 => 'March',
                                                4 => 'April', 5 => 'May', 6 => 'June',
                                                7 => 'July', 8 => 'August', 9 => 'September',
                                                10 => 'October', 11 => 'November', 12 => 'December'
                                            ];
                                            
                                            foreach ($months as $num => $name) {
                                                echo "<option value=\"$num\">$name</option>";
                                            }
                                            ?>
                                        </select>
                                    </div>
                                    
                                    <div class="col-md-3 d-flex align-items-end">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-download me-2"></i> Export Report
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card export-card shadow h-100">
                        <div class="card-body p-4 text-center">
                            <div class="card-icon bg-primary-soft text-primary">
                                ⚖️
                            </div>
                            <h5 class="card-title">Case Reports</h5>
                            <p class="card-text">Export case data including complaint details, status, and resolution information.</p>
                            <a href="?type=case&format=csv" class="btn btn-primary btn-sm">
                                <i class="fas fa-download me-1"></i> Export CSV
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card export-card shadow h-100">
                        <div class="card-body p-4 text-center">
                            <div class="card-icon bg-success-soft text-success">
                                📊
                            </div>
                            <h5 class="card-title">Project Reports</h5>
                            <p class="card-text">Export project data including status, budget, and completion information.</p>
                            <a href="?type=project&format=csv" class="btn btn-success btn-sm">
                                <i class="fas fa-download me-1"></i> Export CSV
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card export-card shadow h-100">
                        <div class="card-body p-4 text-center">
                            <div class="card-icon bg-info-soft text-info">
                                👥
                            </div>
                            <h5 class="card-title">Population Reports</h5>
                            <p class="card-text">Export resident demographic data, household information, and statistics.</p>
                            <a href="?type=population&format=csv" class="btn btn-info btn-sm">
                                <i class="fas fa-download me-1"></i> Export CSV
                            </a>
                        </div>
                    </div>
                </div>
                
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="card export-card shadow h-100">
                        <div class="card-body p-4 text-center">
                            <div class="card-icon bg-warning-soft text-warning">
                                💰
                            </div>
                            <h5 class="card-title">Financial Reports</h5>
                            <p class="card-text">Export financial transaction data, including income, expenses, and balances.</p>
                            <a href="?type=financial&format=csv" class="btn btn-warning btn-sm">
                                <i class="fas fa-download me-1"></i> Export CSV
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card table-card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Export Guide</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5>How to use the export tool</h5>
                            <ol>
                                <li>Select the report type you want to export (Case, Project, Population, Financial, or Registered Voters)</li>
                                <li>Choose the export format (currently only CSV is available)</li>
                                <li>Specify the year and optional month to filter the data</li>
                                <li>Click "Export Report" to download the file</li>
                            </ol>
                            <p>You can also use the quick export buttons below each report card.</p>
                        </div>
                        <div class="col-md-6">
                            <h5>File Format Information</h5>
                            <p><strong>CSV (Comma Separated Values)</strong> - A plain text file that can be opened in Excel or other spreadsheet applications.</p>
                            <p><strong>Coming Soon:</strong></p>
                            <ul>
                                <li><strong>PDF</strong> - Formatted document with tables and charts</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include '../../includes/footer.php'; ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Custom validation for form
        const form = document.querySelector('form');
        form.addEventListener('submit', function(event) {
            const reportType = document.getElementById('type').value;
            if (!reportType) {
                event.preventDefault();
                alert('Please select a report type to export.');
            }
        });
    });
</script>