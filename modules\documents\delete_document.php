<?php
session_start();
include '../../includes/config/database.php';
include '../../includes/functions/permission_functions.php';
include '../../includes/functions/utility.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check if user has permission to delete documents
if (!hasPermission('delete_document')) {
    $_SESSION['error'] = "You don't have permission to delete document requests.";
    header("Location: documents.php");
    exit;
}

// Check if request_id is provided
if (!isset($_POST['request_id']) || empty($_POST['request_id'])) {
    $_SESSION['error'] = "No document request ID provided.";
    header("Location: documents.php");
    exit;
}

$request_id = $_POST['request_id'];

try {
    // Start transaction
    $conn->beginTransaction();

    // Check if document request exists and is in Pending status
    $check_query = "SELECT dr.*, CONCAT(r.first_name, ' ', r.last_name) AS resident_name, dr.document_type
                   FROM document_requests dr
                   JOIN residents r ON dr.resident_id = r.resident_id
                   WHERE dr.request_id = :request_id";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bindParam(':request_id', $request_id);
    $check_stmt->execute();

    if ($check_stmt->rowCount() === 0) {
        throw new Exception("Document request not found.");
    }

    $document = $check_stmt->fetch(PDO::FETCH_ASSOC);

    // Only allow deletion of Pending documents
    if ($document['status'] !== 'Pending') {
        throw new Exception("Only pending document requests can be deleted.");
    }

    // Delete document request
    $delete_query = "DELETE FROM document_requests WHERE request_id = :request_id";
    $delete_stmt = $conn->prepare($delete_query);
    $delete_stmt->bindParam(':request_id', $request_id);
    $delete_stmt->execute();

    // Check if deletion was successful
    if ($delete_stmt->rowCount() === 0) {
        throw new Exception("Failed to delete document request.");
    }

    // Log activity
    $user_id = $_SESSION['user_id'];
    $log_details = "Deleted {$document['document_type']} request for {$document['resident_name']}";

    if (function_exists('logActivity')) {
        logActivity($log_details, $user_id, "Delete Document Request", "Documents");
    }

    // Commit transaction
    $conn->commit();

    $_SESSION['success'] = "Document request successfully deleted.";
    header("Location: documents.php");
    exit;

} catch (Exception $e) {
    // Rollback transaction on error
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }

    $_SESSION['error'] = "Error: " . $e->getMessage();
    header("Location: documents.php");
    exit;
}
?>
