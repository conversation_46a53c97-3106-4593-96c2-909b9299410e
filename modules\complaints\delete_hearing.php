<?php
session_start();
include '../../includes/config/database.php';
include '../../includes/functions/permission_functions.php';
include '../../includes/functions/utility.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check permission
if (!hasPermission('delete_hearing')) {
    $_SESSION['error'] = "You don't have permission to delete hearings.";
    header("Location: hearings.php");
    exit;
}

// Initialize variables
$error_message = '';
$hearing_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$confirm = isset($_GET['confirm']) && $_GET['confirm'] === 'yes';

// Validate hearing ID
if ($hearing_id <= 0) {
    $_SESSION['error'] = "Invalid hearing ID.";
    header("Location: hearings.php");
    exit;
}

// Check if the hearing exists
try {
    $check_query = "SELECT h.*, c.complaint_type, c.status as complaint_status 
                   FROM hearings h
                   LEFT JOIN complaints c ON h.complaint_id = c.complaint_id
                   WHERE h.hearing_id = :hearing_id";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bindParam(':hearing_id', $hearing_id);
    $check_stmt->execute();
    
    if ($check_stmt->rowCount() === 0) {
        $_SESSION['error'] = "Hearing not found.";
        header("Location: hearings.php");
        exit;
    }
    
    $hearing = $check_stmt->fetch(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $_SESSION['error'] = "Error retrieving hearing information: " . $e->getMessage();
    header("Location: hearings.php");
    exit;
}

// Process deletion if confirmed
if ($confirm) {
    try {
        // Start transaction
        $conn->beginTransaction();
        
        // Delete hearing
        $delete_query = "DELETE FROM hearings WHERE hearing_id = :hearing_id";
        $delete_stmt = $conn->prepare($delete_query);
        $delete_stmt->bindParam(':hearing_id', $hearing_id);
        $delete_stmt->execute();
        
        // Commit transaction
        $conn->commit();
        
        // Set success message and redirect
        $_SESSION['success'] = "Hearing has been successfully deleted.";
        header("Location: hearings.php");
        exit;
    } catch (PDOException $e) {
        // Rollback transaction on error
        $conn->rollBack();
        $error_message = "Error deleting hearing: " . $e->getMessage();
    }
}

// Page title
$page_title = "Delete Hearing - Barangay Management System";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../../assets/css/style.css">
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">Delete Hearing</h1>
                    <div>
                        <a href="hearings.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Hearings
                        </a>
                    </div>
                </div>
                
                <?php if ($error_message): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $error_message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>
                
                <div class="card mb-4">
                    <div class="card-header bg-danger text-white">
                        <h5 class="card-title mb-0">Confirm Deletion</h5>
                    </div>
                    <div class="card-body">
                        <p class="mb-4">Are you sure you want to delete the following hearing? This action cannot be undone.</p>
                        
                        <div class="table-responsive mb-4">
                            <table class="table table-bordered">
                                <tbody>
                                    <tr>
                                        <th style="width: 200px;">Hearing ID</th>
                                        <td><?php echo $hearing_id; ?></td>
                                    </tr>
                                    <tr>
                                        <th>Related Complaint</th>
                                        <td>
                                            <?php echo htmlspecialchars($hearing['complaint_type'] ?? 'Unknown'); ?>
                                            (ID: <?php echo $hearing['complaint_id']; ?>)
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>Hearing Date</th>
                                        <td><?php echo date('F j, Y \a\t g:i A', strtotime($hearing['hearing_date'])); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Location</th>
                                        <td><?php echo htmlspecialchars($hearing['hearing_location']); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Purpose</th>
                                        <td><?php echo htmlspecialchars($hearing['purpose']); ?></td>
                                    </tr>
                                    <tr>
                                        <th>Status</th>
                                        <td>
                                            <?php
                                            $statusClass = '';
                                            switch ($hearing['status']) {
                                                case 'Scheduled':
                                                    $statusClass = 'bg-primary';
                                                    break;
                                                case 'Completed':
                                                    $statusClass = 'bg-success';
                                                    break;
                                                case 'Postponed':
                                                    $statusClass = 'bg-warning';
                                                    break;
                                                case 'Cancelled':
                                                    $statusClass = 'bg-danger';
                                                    break;
                                                default:
                                                    $statusClass = 'bg-secondary';
                                            }
                                            ?>
                                            <span class="badge <?php echo $statusClass; ?>">
                                                <?php echo htmlspecialchars($hearing['status']); ?>
                                            </span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i> 
                            <strong>Warning:</strong> Deleting this hearing will permanently remove it from the system. 
                            There will be no record of this hearing in relation to the complaint.
                        </div>
                        
                        <div class="d-flex justify-content-center gap-3 mt-4">
                            <a href="hearings.php" class="btn btn-secondary btn-lg">
                                <i class="fas fa-times"></i> Cancel
                            </a>
                            <a href="?id=<?php echo $hearing_id; ?>&confirm=yes" class="btn btn-danger btn-lg">
                                <i class="fas fa-trash"></i> Delete Hearing
                            </a>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html> 